{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projet fitness\\\\DATA\\\\fitness-ui\\\\src\\\\pages\\\\Social.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { FiUsers, FiTrendingUp, FiAward, FiUserPlus, FiMessageCircle, FiTarget, FiClock, FiMapPin, FiCheck, FiX, FiSearch, FiPlus, FiEdit3, FiTrash2, FiShare2, FiHeart, FiThumbsUp } from 'react-icons/fi';\nimport { useAuth } from '../contexts/AuthContext';\nimport { generateMockUsers, generateChallenges, generateFriendships, generateFriendSuggestions, formatTime, formatPace } from '../utils/socialUtils';\nimport { generateSegments } from '../utils/mapUtils';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Social = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const [activeTab, setActiveTab] = useState('friends');\n  const [challenges, setChallenges] = useState([]);\n  const [segments, setSegments] = useState([]);\n  const [friendships, setFriendships] = useState([]);\n  const [friendSuggestions, setFriendSuggestions] = useState([]);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [userActivities, setUserActivities] = useState([]);\n  const [showCreateChallenge, setShowCreateChallenge] = useState(false);\n  const [newChallenge, setNewChallenge] = useState({\n    name: '',\n    description: '',\n    type: 'distance',\n    target: '',\n    duration: 7,\n    difficulty: 'modéré'\n  });\n  useEffect(() => {\n    // Charger les données sauvegardées\n    try {\n      const savedChallenges = JSON.parse(localStorage.getItem('socialChallenges') || '[]');\n      const savedActivities = JSON.parse(localStorage.getItem('userActivities') || '[]');\n      const savedFriendships = JSON.parse(localStorage.getItem('friendships') || '[]');\n      if (savedChallenges.length > 0) {\n        setChallenges(savedChallenges);\n      }\n      setUserActivities(savedActivities);\n      if (savedFriendships.length > 0) {\n        setFriendships(savedFriendships);\n      }\n    } catch (error) {\n      console.error('Erreur lors du chargement des données sociales:', error);\n    }\n\n    // Générer des données fictives si aucune donnée sauvegardée\n    const mockUsers = generateMockUsers(50);\n    const mockChallenges = generateChallenges(mockUsers);\n    const mockSegments = generateSegments([\n    // Quelques routes fictives pour générer des segments\n    {\n      name: 'Parcours du Parc',\n      points: Array.from({\n        length: 20\n      }, (_, i) => ({\n        lat: 48.8566 + i * 0.001,\n        lng: 2.3522 + i * 0.001,\n        elevation: 100 + Math.sin(i) * 50\n      })),\n      type: 'running'\n    }, {\n      name: 'Circuit de la Forêt',\n      points: Array.from({\n        length: 15\n      }, (_, i) => ({\n        lat: 48.8566 + i * 0.0015,\n        lng: 2.3522 - i * 0.0008,\n        elevation: 120 + Math.cos(i) * 40\n      })),\n      type: 'cycling'\n    }]);\n\n    // Utiliser les données fictives si pas de données sauvegardées\n    if (challenges.length === 0) {\n      setChallenges(mockChallenges);\n    }\n    setSegments(mockSegments);\n    if (user && friendships.length === 0) {\n      const userFriendships = generateFriendships(mockUsers, user.id);\n      const suggestions = generateFriendSuggestions(mockUsers, user.id, userFriendships);\n      setFriendships(userFriendships);\n      setFriendSuggestions(suggestions);\n    }\n  }, [user]);\n  const acceptedFriends = friendships.filter(f => f.status === 'accepted');\n  const pendingRequests = friendships.filter(f => f.status === 'pending');\n  const activeChallenges = challenges.filter(c => c.isActive);\n  const completedChallenges = challenges.filter(c => !c.isActive);\n  const handleAcceptFriend = friendshipId => {\n    setFriendships(prev => prev.map(f => f.id === friendshipId ? {\n      ...f,\n      status: 'accepted'\n    } : f));\n  };\n  const handleRejectFriend = friendshipId => {\n    setFriendships(prev => prev.filter(f => f.id !== friendshipId));\n  };\n  const handleJoinChallenge = challengeId => {\n    const updatedChallenges = challenges.map(challenge => {\n      if (challenge.id === challengeId) {\n        const newParticipant = {\n          user: {\n            id: user.id,\n            firstName: user.firstName,\n            lastName: user.lastName,\n            avatar: user.avatar || `https://ui-avatars.com/api/?name=${user.firstName}+${user.lastName}`\n          },\n          progress: 0,\n          rank: challenge.participants.length + 1,\n          joinedAt: new Date(),\n          isCompleted: false,\n          lastActivity: new Date()\n        };\n        return {\n          ...challenge,\n          participants: [...challenge.participants, newParticipant]\n        };\n      }\n      return challenge;\n    });\n    setChallenges(updatedChallenges);\n    localStorage.setItem('socialChallenges', JSON.stringify(updatedChallenges));\n  };\n  const createChallenge = () => {\n    if (!newChallenge.name || !newChallenge.target) return;\n    const challenge = {\n      id: `challenge_${Date.now()}`,\n      name: newChallenge.name,\n      description: newChallenge.description,\n      type: newChallenge.type,\n      target: parseFloat(newChallenge.target),\n      duration: newChallenge.duration,\n      difficulty: newChallenge.difficulty,\n      isActive: true,\n      startDate: new Date(),\n      endDate: new Date(Date.now() + newChallenge.duration * 24 * 60 * 60 * 1000),\n      createdBy: (user === null || user === void 0 ? void 0 : user.firstName) || 'Utilisateur',\n      participants: [{\n        user: {\n          id: (user === null || user === void 0 ? void 0 : user.id) || 'user1',\n          firstName: (user === null || user === void 0 ? void 0 : user.firstName) || 'Utilisateur',\n          lastName: (user === null || user === void 0 ? void 0 : user.lastName) || '',\n          avatar: (user === null || user === void 0 ? void 0 : user.avatar) || `https://ui-avatars.com/api/?name=${user === null || user === void 0 ? void 0 : user.firstName}+${user === null || user === void 0 ? void 0 : user.lastName}`\n        },\n        progress: 0,\n        rank: 1,\n        joinedAt: new Date(),\n        isCompleted: false,\n        lastActivity: new Date()\n      }],\n      icon: getChallengeIcon(newChallenge.type)\n    };\n    const updatedChallenges = [challenge, ...challenges];\n    setChallenges(updatedChallenges);\n    localStorage.setItem('socialChallenges', JSON.stringify(updatedChallenges));\n    setShowCreateChallenge(false);\n    setNewChallenge({\n      name: '',\n      description: '',\n      type: 'distance',\n      target: '',\n      duration: 7,\n      difficulty: 'modéré'\n    });\n  };\n  const deleteChallenge = challengeId => {\n    const updatedChallenges = challenges.filter(c => c.id !== challengeId);\n    setChallenges(updatedChallenges);\n    localStorage.setItem('socialChallenges', JSON.stringify(updatedChallenges));\n  };\n  const updateChallengeProgress = (challengeId, progress) => {\n    const updatedChallenges = challenges.map(challenge => {\n      if (challenge.id === challengeId) {\n        const updatedParticipants = challenge.participants.map(p => {\n          if (p.user.id === (user === null || user === void 0 ? void 0 : user.id)) {\n            return {\n              ...p,\n              progress: Math.min(progress, challenge.target),\n              isCompleted: progress >= challenge.target,\n              lastActivity: new Date()\n            };\n          }\n          return p;\n        });\n\n        // Recalculer les rangs\n        const sortedParticipants = updatedParticipants.sort((a, b) => b.progress - a.progress);\n        sortedParticipants.forEach((p, index) => {\n          p.rank = index + 1;\n        });\n        return {\n          ...challenge,\n          participants: sortedParticipants\n        };\n      }\n      return challenge;\n    });\n    setChallenges(updatedChallenges);\n    localStorage.setItem('socialChallenges', JSON.stringify(updatedChallenges));\n  };\n  const filteredFriends = acceptedFriends.filter(friendship => friendship.user2.firstName.toLowerCase().includes(searchTerm.toLowerCase()) || friendship.user2.lastName.toLowerCase().includes(searchTerm.toLowerCase()));\n  const getDifficultyColor = difficulty => {\n    switch (difficulty) {\n      case 'facile':\n        return 'text-green-600 bg-green-100';\n      case 'modéré':\n        return 'text-yellow-600 bg-yellow-100';\n      case 'difficile':\n        return 'text-red-600 bg-red-100';\n      default:\n        return 'text-gray-600 bg-gray-100';\n    }\n  };\n  const getTypeIcon = type => {\n    switch (type) {\n      case 'running':\n        return '🏃‍♂️';\n      case 'cycling':\n        return '🚴‍♂️';\n      case 'hiking':\n        return '🥾';\n      case 'walking':\n        return '🚶‍♂️';\n      default:\n        return '📍';\n    }\n  };\n  const getChallengeIcon = type => {\n    switch (type) {\n      case 'distance':\n        return '📏';\n      case 'activities':\n        return '🏃‍♂️';\n      case 'time':\n        return '⏱️';\n      case 'streak':\n        return '🔥';\n      default:\n        return '🎯';\n    }\n  };\n  const addActivity = activity => {\n    const newActivity = {\n      id: Date.now(),\n      user: {\n        id: (user === null || user === void 0 ? void 0 : user.id) || 'user1',\n        firstName: (user === null || user === void 0 ? void 0 : user.firstName) || 'Utilisateur',\n        lastName: (user === null || user === void 0 ? void 0 : user.lastName) || '',\n        avatar: (user === null || user === void 0 ? void 0 : user.avatar) || `https://ui-avatars.com/api/?name=${user === null || user === void 0 ? void 0 : user.firstName}+${user === null || user === void 0 ? void 0 : user.lastName}`\n      },\n      ...activity,\n      timestamp: new Date(),\n      likes: [],\n      comments: []\n    };\n    const updatedActivities = [newActivity, ...userActivities];\n    setUserActivities(updatedActivities);\n    localStorage.setItem('userActivities', JSON.stringify(updatedActivities));\n  };\n  const likeActivity = activityId => {\n    const updatedActivities = userActivities.map(activity => {\n      if (activity.id === activityId) {\n        const hasLiked = activity.likes.includes(user === null || user === void 0 ? void 0 : user.id);\n        return {\n          ...activity,\n          likes: hasLiked ? activity.likes.filter(id => id !== (user === null || user === void 0 ? void 0 : user.id)) : [...activity.likes, user === null || user === void 0 ? void 0 : user.id]\n        };\n      }\n      return activity;\n    });\n    setUserActivities(updatedActivities);\n    localStorage.setItem('userActivities', JSON.stringify(updatedActivities));\n  };\n  const addComment = (activityId, comment) => {\n    const updatedActivities = userActivities.map(activity => {\n      if (activity.id === activityId) {\n        const newComment = {\n          id: Date.now(),\n          user: {\n            id: (user === null || user === void 0 ? void 0 : user.id) || 'user1',\n            firstName: (user === null || user === void 0 ? void 0 : user.firstName) || 'Utilisateur',\n            lastName: (user === null || user === void 0 ? void 0 : user.lastName) || ''\n          },\n          text: comment,\n          timestamp: new Date()\n        };\n        return {\n          ...activity,\n          comments: [...activity.comments, newComment]\n        };\n      }\n      return activity;\n    });\n    setUserActivities(updatedActivities);\n    localStorage.setItem('userActivities', JSON.stringify(updatedActivities));\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold text-gray-900 mb-4\",\n          children: \"Communaut\\xE9 FitTracker\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 342,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg text-gray-600 max-w-3xl mx-auto\",\n          children: \"Connectez-vous avec d'autres athl\\xE8tes, participez \\xE0 des d\\xE9fis et d\\xE9couvrez les classements des segments populaires.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 345,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 341,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex space-x-1 bg-gray-100 p-1 rounded-lg mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setActiveTab('friends'),\n          className: `flex-1 py-3 px-4 rounded-md text-sm font-medium transition-colors ${activeTab === 'friends' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'}`,\n          children: [/*#__PURE__*/_jsxDEV(FiUsers, {\n            className: \"inline mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 361,\n            columnNumber: 13\n          }, this), \"Amis (\", acceptedFriends.length, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 353,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setActiveTab('challenges'),\n          className: `flex-1 py-3 px-4 rounded-md text-sm font-medium transition-colors ${activeTab === 'challenges' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'}`,\n          children: [/*#__PURE__*/_jsxDEV(FiTarget, {\n            className: \"inline mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 372,\n            columnNumber: 13\n          }, this), \"D\\xE9fis (\", activeChallenges.length, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 364,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setActiveTab('leaderboards'),\n          className: `flex-1 py-3 px-4 rounded-md text-sm font-medium transition-colors ${activeTab === 'leaderboards' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'}`,\n          children: [/*#__PURE__*/_jsxDEV(FiTrendingUp, {\n            className: \"inline mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 383,\n            columnNumber: 13\n          }, this), \"Classements\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 375,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 352,\n        columnNumber: 9\n      }, this), activeTab === 'friends' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:col-span-2\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-xl font-semibold text-gray-900\",\n                children: [\"Mes Amis (\", acceptedFriends.length, \")\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 395,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative\",\n                children: [/*#__PURE__*/_jsxDEV(FiSearch, {\n                  className: \"absolute left-3 top-3 h-4 w-4 text-gray-400\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 399,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  placeholder: \"Rechercher un ami...\",\n                  value: searchTerm,\n                  onChange: e => setSearchTerm(e.target.value),\n                  className: \"pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 400,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 398,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 394,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: filteredFriends.map(friendship => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    src: friendship.user2.avatar,\n                    alt: `${friendship.user2.firstName} ${friendship.user2.lastName}`,\n                    className: \"w-12 h-12 rounded-full\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 414,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"font-medium text-gray-900\",\n                      children: [friendship.user2.firstName, \" \", friendship.user2.lastName]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 420,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center space-x-4 text-sm text-gray-600\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"flex items-center\",\n                        children: [/*#__PURE__*/_jsxDEV(FiMapPin, {\n                          className: \"h-4 w-4 mr-1\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 425,\n                          columnNumber: 31\n                        }, this), friendship.user2.city]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 424,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"flex items-center\",\n                        children: [/*#__PURE__*/_jsxDEV(FiClock, {\n                          className: \"h-4 w-4 mr-1\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 429,\n                          columnNumber: 31\n                        }, this), friendship.user2.isOnline ? 'En ligne' : 'Hors ligne']\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 428,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 423,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-gray-500 mt-1\",\n                      children: [friendship.mutualFriends, \" amis en commun \\u2022 \", friendship.sharedActivities, \" activit\\xE9s partag\\xE9es\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 433,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 419,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 413,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"p-2 text-blue-600 hover:bg-blue-50 rounded-lg\",\n                    children: /*#__PURE__*/_jsxDEV(FiMessageCircle, {\n                      className: \"h-5 w-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 440,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 439,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-right\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm font-medium text-gray-900\",\n                      children: [friendship.user2.stats.totalDistance, \" km\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 443,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-gray-500\",\n                      children: [friendship.user2.stats.totalActivities, \" activit\\xE9s\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 446,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 442,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 438,\n                  columnNumber: 23\n                }, this)]\n              }, friendship.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 412,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 410,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 393,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 392,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-6\",\n          children: [pendingRequests.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-4\",\n              children: [\"Demandes d'amiti\\xE9 (\", pendingRequests.length, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 462,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3\",\n              children: pendingRequests.map(request => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between p-3 border border-gray-200 rounded-lg\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    src: request.user1.avatar,\n                    alt: `${request.user1.firstName} ${request.user1.lastName}`,\n                    className: \"w-10 h-10 rounded-full\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 469,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"font-medium text-gray-900 text-sm\",\n                      children: [request.user1.firstName, \" \", request.user1.lastName]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 475,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-xs text-gray-600\",\n                      children: request.user1.city\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 478,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 474,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 468,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex space-x-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleAcceptFriend(request.id),\n                    className: \"p-1 text-green-600 hover:bg-green-50 rounded\",\n                    children: /*#__PURE__*/_jsxDEV(FiCheck, {\n                      className: \"h-4 w-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 486,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 482,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleRejectFriend(request.id),\n                    className: \"p-1 text-red-600 hover:bg-red-50 rounded\",\n                    children: /*#__PURE__*/_jsxDEV(FiX, {\n                      className: \"h-4 w-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 492,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 488,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 481,\n                  columnNumber: 25\n                }, this)]\n              }, request.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 467,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 465,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 461,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-4\",\n              children: \"Suggestions d'amis\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 503,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3\",\n              children: friendSuggestions.slice(0, 5).map(suggestion => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between p-3 border border-gray-200 rounded-lg\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    src: suggestion.user.avatar,\n                    alt: `${suggestion.user.firstName} ${suggestion.user.lastName}`,\n                    className: \"w-10 h-10 rounded-full\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 510,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"font-medium text-gray-900 text-sm\",\n                      children: [suggestion.user.firstName, \" \", suggestion.user.lastName]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 516,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-xs text-gray-600\",\n                      children: suggestion.reason\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 519,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 515,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 509,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"p-1 text-blue-600 hover:bg-blue-50 rounded\",\n                  children: /*#__PURE__*/_jsxDEV(FiUserPlus, {\n                    className: \"h-4 w-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 523,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 522,\n                  columnNumber: 23\n                }, this)]\n              }, suggestion.user.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 508,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 506,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 502,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 458,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 390,\n        columnNumber: 11\n      }, this), activeTab === 'challenges' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:col-span-2\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-xl font-semibold text-gray-900\",\n                children: [\"D\\xE9fis Actifs (\", activeChallenges.length, \")\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 540,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setShowCreateChallenge(true),\n                className: \"bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(FiPlus, {\n                  className: \"mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 547,\n                  columnNumber: 21\n                }, this), \"Cr\\xE9er un d\\xE9fi\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 543,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 539,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid gap-6\",\n              children: activeChallenges.map(challenge => {\n                var _challenge$participan, _challenge$participan2;\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-start justify-between mb-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center space-x-3\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-2xl\",\n                        children: getChallengeIcon(challenge.type)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 557,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                          className: \"font-semibold text-gray-900\",\n                          children: challenge.name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 559,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-sm text-gray-600\",\n                          children: challenge.description\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 560,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 558,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 556,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `px-3 py-1 rounded-full text-xs font-medium ${challenge.category === 'team' ? 'bg-purple-100 text-purple-700' : 'bg-blue-100 text-blue-700'}`,\n                      children: challenge.category === 'team' ? 'Équipe' : 'Individuel'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 563,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 555,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"grid grid-cols-2 md:grid-cols-4 gap-4 mb-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-lg font-bold text-gray-900\",\n                        children: challenge.participants.length\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 572,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-xs text-gray-600\",\n                        children: \"Participants\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 573,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 571,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-lg font-bold text-gray-900\",\n                        children: challenge.target\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 576,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-xs text-gray-600\",\n                        children: challenge.unit\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 577,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 575,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-lg font-bold text-gray-900\",\n                        children: challenge.duration\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 580,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-xs text-gray-600\",\n                        children: \"jours\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 581,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 579,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-lg font-bold text-gray-900\",\n                        children: Math.ceil((challenge.endDate - new Date()) / (24 * 60 * 60 * 1000))\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 584,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-xs text-gray-600\",\n                        children: \"jours restants\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 587,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 583,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 570,\n                    columnNumber: 23\n                  }, this), challenge.participants.find(p => p.user.id === (user === null || user === void 0 ? void 0 : user.id)) && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mb-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center justify-between text-sm mb-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"Votre progression\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 595,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: [((_challenge$participan = challenge.participants.find(p => p.user.id === (user === null || user === void 0 ? void 0 : user.id))) === null || _challenge$participan === void 0 ? void 0 : _challenge$participan.progress) || 0, \" / \", challenge.target]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 596,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 594,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-full bg-gray-200 rounded-full h-2\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"bg-blue-600 h-2 rounded-full transition-all duration-300\",\n                        style: {\n                          width: `${Math.min(100, (((_challenge$participan2 = challenge.participants.find(p => p.user.id === (user === null || user === void 0 ? void 0 : user.id))) === null || _challenge$participan2 === void 0 ? void 0 : _challenge$participan2.progress) || 0) / challenge.target * 100)}%`\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 599,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 598,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 593,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm text-gray-600\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"font-medium\",\n                        children: \"Prix :\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 611,\n                        columnNumber: 27\n                      }, this), \" \", challenge.prize || 'Badge de réussite']\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 610,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center space-x-2\",\n                      children: challenge.participants.find(p => p.user.id === (user === null || user === void 0 ? void 0 : user.id)) ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                          type: \"number\",\n                          placeholder: \"Progression\",\n                          className: \"w-20 px-2 py-1 border border-gray-300 rounded text-sm\",\n                          onKeyPress: e => {\n                            if (e.key === 'Enter') {\n                              updateChallengeProgress(challenge.id, parseFloat(e.target.value));\n                              e.target.value = '';\n                            }\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 616,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                          onClick: () => deleteChallenge(challenge.id),\n                          className: \"text-red-600 hover:text-red-800 transition-colors\",\n                          title: \"Supprimer le d\\xE9fi\",\n                          children: /*#__PURE__*/_jsxDEV(FiTrash2, {\n                            className: \"h-4 w-4\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 632,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 627,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true) : /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => handleJoinChallenge(challenge.id),\n                        className: \"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm\",\n                        children: \"Rejoindre\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 636,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 613,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 609,\n                    columnNumber: 23\n                  }, this)]\n                }, challenge.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 554,\n                  columnNumber: 21\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 552,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 538,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 537,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-4\",\n              children: \"Mes D\\xE9fis en Cours\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 654,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3\",\n              children: activeChallenges.slice(0, 3).map(challenge => {\n                const userParticipation = challenge.participants.find(p => p.user.id === (user === null || user === void 0 ? void 0 : user.id));\n                if (!userParticipation) return null;\n                const progressPercentage = userParticipation.progress / challenge.target * 100;\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"p-3 border border-gray-200 rounded-lg\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center justify-between mb-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"font-medium text-gray-900 text-sm\",\n                      children: challenge.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 667,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-xs text-gray-600\",\n                      children: [\"#\", userParticipation.rank]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 668,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 666,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full bg-gray-200 rounded-full h-2 mb-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-blue-600 h-2 rounded-full\",\n                      style: {\n                        width: `${Math.min(progressPercentage, 100)}%`\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 671,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 670,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-xs text-gray-600\",\n                    children: [userParticipation.progress, \" / \", challenge.target, \" \", challenge.unit]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 676,\n                    columnNumber: 25\n                  }, this)]\n                }, challenge.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 665,\n                  columnNumber: 23\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 657,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 653,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-4\",\n              children: \"D\\xE9fis Termin\\xE9s\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 686,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3\",\n              children: completedChallenges.slice(0, 3).map(challenge => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-3 border border-gray-200 rounded-lg\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"font-medium text-gray-900 text-sm\",\n                  children: challenge.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 692,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between mt-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-xs text-gray-600\",\n                    children: [challenge.participants.length, \" participants\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 694,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-xs text-green-600 font-medium\",\n                    children: \"Termin\\xE9\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 697,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 693,\n                  columnNumber: 23\n                }, this)]\n              }, challenge.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 691,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 689,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 685,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 652,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 535,\n        columnNumber: 11\n      }, this), activeTab === 'leaderboards' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:col-span-2\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-xl font-semibold text-gray-900 mb-6\",\n              children: \"Classements des Segments\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 713,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-6\",\n              children: segments.slice(0, 5).map(segment => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"border border-gray-200 rounded-lg p-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-start justify-between mb-4\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-xl\",\n                      children: getTypeIcon(segment.type)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 722,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"font-semibold text-gray-900\",\n                        children: segment.name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 724,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm text-gray-600\",\n                        children: segment.description\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 725,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center space-x-4 mt-1\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-xs text-gray-500\",\n                          children: [segment.distance.toFixed(1), \" km\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 727,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: `px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(segment.difficulty)}`,\n                          children: segment.difficulty\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 728,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-xs text-gray-500\",\n                          children: [segment.totalAttempts, \" tentatives\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 731,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 726,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 723,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 721,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 720,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"font-medium text-gray-900 text-sm mb-3\",\n                    children: \"Top 5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 738,\n                    columnNumber: 25\n                  }, this), segment.leaderboard.slice(0, 5).map((entry, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center justify-between p-2 rounded-lg hover:bg-gray-50\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center space-x-3\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: `w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold ${index === 0 ? 'bg-yellow-100 text-yellow-800' : index === 1 ? 'bg-gray-100 text-gray-800' : index === 2 ? 'bg-orange-100 text-orange-800' : 'bg-blue-100 text-blue-800'}`,\n                        children: entry.rank\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 742,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"font-medium text-gray-900 text-sm\",\n                          children: entry.athlete\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 751,\n                          columnNumber: 33\n                        }, this), entry.isLocalLegend && /*#__PURE__*/_jsxDEV(FiAward, {\n                          className: \"inline ml-2 h-4 w-4 text-yellow-500\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 753,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 750,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 741,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-right\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"font-medium text-gray-900 text-sm\",\n                        children: formatTime(entry.time)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 758,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-xs text-gray-500\",\n                        children: formatPace(entry.pace)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 761,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 757,\n                      columnNumber: 29\n                    }, this)]\n                  }, index, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 740,\n                    columnNumber: 27\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 737,\n                  columnNumber: 23\n                }, this)]\n              }, segment.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 719,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 717,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 712,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 711,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-4\",\n              children: \"Mes Performances\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 777,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between p-3 bg-yellow-50 rounded-lg\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(FiAward, {\n                    className: \"h-5 w-5 text-yellow-600\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 783,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-medium text-gray-900\",\n                    children: \"L\\xE9gendes Locales\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 784,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 782,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-lg font-bold text-yellow-600\",\n                  children: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 786,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 781,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between p-3 bg-blue-50 rounded-lg\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(FiTrendingUp, {\n                    className: \"h-5 w-5 text-blue-600\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 791,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-medium text-gray-900\",\n                    children: \"Top 10\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 792,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 790,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-lg font-bold text-blue-600\",\n                  children: \"8\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 794,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 789,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between p-3 bg-green-50 rounded-lg\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(FiAward, {\n                    className: \"h-5 w-5 text-green-600\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 799,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-medium text-gray-900\",\n                    children: \"Records Personnels\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 800,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 798,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-lg font-bold text-green-600\",\n                  children: \"15\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 802,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 797,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 780,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 776,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-4\",\n              children: \"Segments Populaires\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 808,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3\",\n              children: segments.slice(0, 5).map(segment => {\n                var _segment$leaderboard$;\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"font-medium text-gray-900 text-sm\",\n                      children: segment.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 815,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center space-x-2 text-xs text-gray-600\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        children: [segment.distance.toFixed(1), \" km\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 817,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"\\u2022\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 818,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: [segment.totalAttempts, \" tentatives\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 819,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 816,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 814,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-right\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm font-medium text-gray-900\",\n                      children: formatTime(((_segment$leaderboard$ = segment.leaderboard[0]) === null || _segment$leaderboard$ === void 0 ? void 0 : _segment$leaderboard$.time) || 0)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 823,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-gray-500\",\n                      children: \"Record\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 826,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 822,\n                    columnNumber: 23\n                  }, this)]\n                }, segment.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 813,\n                  columnNumber: 21\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 811,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 807,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 775,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 709,\n        columnNumber: 11\n      }, this), showCreateChallenge && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg p-6 max-w-md w-full mx-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-gray-900 mb-4\",\n            children: \"Cr\\xE9er un nouveau d\\xE9fi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 840,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Nom du d\\xE9fi\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 846,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: newChallenge.name,\n                onChange: e => setNewChallenge({\n                  ...newChallenge,\n                  name: e.target.value\n                }),\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                placeholder: \"Ex: D\\xE9fi 100km en janvier\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 849,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 845,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Description\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 859,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                value: newChallenge.description,\n                onChange: e => setNewChallenge({\n                  ...newChallenge,\n                  description: e.target.value\n                }),\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                rows: \"3\",\n                placeholder: \"D\\xE9crivez votre d\\xE9fi...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 862,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 858,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-2 gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-1\",\n                  children: \"Type\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 873,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  value: newChallenge.type,\n                  onChange: e => setNewChallenge({\n                    ...newChallenge,\n                    type: e.target.value\n                  }),\n                  className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"distance\",\n                    children: \"Distance (km)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 881,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"activities\",\n                    children: \"Nombre d'activit\\xE9s\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 882,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"time\",\n                    children: \"Temps (minutes)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 883,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"streak\",\n                    children: \"S\\xE9rie de jours\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 884,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 876,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 872,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-1\",\n                  children: \"Objectif\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 889,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  value: newChallenge.target,\n                  onChange: e => setNewChallenge({\n                    ...newChallenge,\n                    target: e.target.value\n                  }),\n                  className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                  placeholder: \"100\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 892,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 888,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 871,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-2 gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-1\",\n                  children: \"Dur\\xE9e (jours)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 904,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  value: newChallenge.duration,\n                  onChange: e => setNewChallenge({\n                    ...newChallenge,\n                    duration: parseInt(e.target.value)\n                  }),\n                  className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                  min: \"1\",\n                  max: \"365\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 907,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 903,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-1\",\n                  children: \"Difficult\\xE9\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 918,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  value: newChallenge.difficulty,\n                  onChange: e => setNewChallenge({\n                    ...newChallenge,\n                    difficulty: e.target.value\n                  }),\n                  className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"facile\",\n                    children: \"Facile\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 926,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"mod\\xE9r\\xE9\",\n                    children: \"Mod\\xE9r\\xE9\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 927,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"difficile\",\n                    children: \"Difficile\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 928,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 921,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 917,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 902,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 844,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex space-x-3 mt-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: createChallenge,\n              className: \"flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors\",\n              children: \"Cr\\xE9er le d\\xE9fi\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 935,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowCreateChallenge(false),\n              className: \"flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-400 transition-colors\",\n              children: \"Annuler\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 941,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 934,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 839,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 838,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 339,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 338,\n    columnNumber: 5\n  }, this);\n};\n_s(Social, \"0IvbzaJtPgLFneQD1wWFCK9yYKM=\", false, function () {\n  return [useAuth];\n});\n_c = Social;\nexport default Social;\nvar _c;\n$RefreshReg$(_c, \"Social\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "FiUsers", "FiTrendingUp", "FiAward", "FiUserPlus", "FiMessageCircle", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "FiMapPin", "<PERSON><PERSON><PERSON><PERSON>", "FiX", "FiSearch", "FiPlus", "FiEdit3", "FiTrash2", "FiShare2", "<PERSON><PERSON><PERSON><PERSON>", "FiThumbsUp", "useAuth", "generateMockUsers", "generateChallenges", "generateFriendships", "generateFriendSuggestions", "formatTime", "formatPace", "generateSegments", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Social", "_s", "user", "activeTab", "setActiveTab", "challenges", "setChallenges", "segments", "setSegments", "friendships", "setFriendships", "friendSuggestions", "setFriendSuggestions", "searchTerm", "setSearchTerm", "userActivities", "setUserActivities", "showCreateChallenge", "setShowCreateChallenge", "newChallenge", "setNewChallenge", "name", "description", "type", "target", "duration", "difficulty", "savedChallenges", "JSON", "parse", "localStorage", "getItem", "savedActivities", "savedFriendships", "length", "error", "console", "mockUsers", "mockChallenges", "mockSegments", "points", "Array", "from", "_", "i", "lat", "lng", "elevation", "Math", "sin", "cos", "userFriendships", "id", "suggestions", "acceptedFriends", "filter", "f", "status", "pendingRequests", "activeChallenges", "c", "isActive", "completedChallenges", "handleAcceptFriend", "friendshipId", "prev", "map", "handleRejectFriend", "handleJoinChallenge", "challengeId", "updatedChallenges", "challenge", "newParticipant", "firstName", "lastName", "avatar", "progress", "rank", "participants", "joinedAt", "Date", "isCompleted", "lastActivity", "setItem", "stringify", "createChallenge", "now", "parseFloat", "startDate", "endDate", "created<PERSON>y", "icon", "getChallengeIcon", "deleteChallenge", "updateChallengeProgress", "updatedParticipants", "p", "min", "sortedParticipants", "sort", "a", "b", "for<PERSON>ach", "index", "filteredFriends", "friendship", "user2", "toLowerCase", "includes", "getDifficultyColor", "getTypeIcon", "addActivity", "activity", "newActivity", "timestamp", "likes", "comments", "updatedActivities", "likeActivity", "activityId", "<PERSON><PERSON><PERSON>d", "addComment", "comment", "newComment", "text", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "placeholder", "value", "onChange", "e", "src", "alt", "city", "isOnline", "mutualFriends", "sharedActivities", "stats", "totalDistance", "totalActivities", "request", "user1", "slice", "suggestion", "reason", "_challenge$participan", "_challenge$participan2", "category", "unit", "ceil", "find", "style", "width", "prize", "onKeyPress", "key", "title", "userParticipation", "progressPercentage", "segment", "distance", "toFixed", "totalAttempts", "leaderboard", "entry", "athlete", "isLocalLegend", "time", "pace", "_segment$leaderboard$", "rows", "parseInt", "max", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/projet fitness/DATA/fitness-ui/src/pages/Social.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  FiUsers,\n  FiTrendingUp,\n  FiAward,\n  FiUserPlus,\n  FiMessageCircle,\n  FiTarget,\n  FiClock,\n  FiMapPin,\n  FiCheck,\n  FiX,\n  FiSearch,\n  FiPlus,\n  FiEdit3,\n  FiTrash2,\n  FiShare2,\n  FiHeart,\n  FiThumbsUp\n} from 'react-icons/fi';\nimport { useAuth } from '../contexts/AuthContext';\nimport {\n  generateMockUsers,\n  generateChallenges,\n  generateFriendships,\n  generateFriendSuggestions,\n  formatTime,\n  formatPace\n} from '../utils/socialUtils';\nimport { generateSegments } from '../utils/mapUtils';\n\nconst Social = () => {\n  const { user } = useAuth();\n  const [activeTab, setActiveTab] = useState('friends');\n\n  const [challenges, setChallenges] = useState([]);\n  const [segments, setSegments] = useState([]);\n  const [friendships, setFriendships] = useState([]);\n  const [friendSuggestions, setFriendSuggestions] = useState([]);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [userActivities, setUserActivities] = useState([]);\n  const [showCreateChallenge, setShowCreateChallenge] = useState(false);\n  const [newChallenge, setNewChallenge] = useState({\n    name: '',\n    description: '',\n    type: 'distance',\n    target: '',\n    duration: 7,\n    difficulty: 'modéré'\n  });\n\n\n  useEffect(() => {\n    // Charger les données sauvegardées\n    try {\n      const savedChallenges = JSON.parse(localStorage.getItem('socialChallenges') || '[]');\n      const savedActivities = JSON.parse(localStorage.getItem('userActivities') || '[]');\n      const savedFriendships = JSON.parse(localStorage.getItem('friendships') || '[]');\n\n      if (savedChallenges.length > 0) {\n        setChallenges(savedChallenges);\n      }\n      setUserActivities(savedActivities);\n      if (savedFriendships.length > 0) {\n        setFriendships(savedFriendships);\n      }\n    } catch (error) {\n      console.error('Erreur lors du chargement des données sociales:', error);\n    }\n\n    // Générer des données fictives si aucune donnée sauvegardée\n    const mockUsers = generateMockUsers(50);\n    const mockChallenges = generateChallenges(mockUsers);\n    const mockSegments = generateSegments([\n      // Quelques routes fictives pour générer des segments\n      {\n        name: 'Parcours du Parc',\n        points: Array.from({ length: 20 }, (_, i) => ({\n          lat: 48.8566 + (i * 0.001),\n          lng: 2.3522 + (i * 0.001),\n          elevation: 100 + Math.sin(i) * 50\n        })),\n        type: 'running'\n      },\n      {\n        name: 'Circuit de la Forêt',\n        points: Array.from({ length: 15 }, (_, i) => ({\n          lat: 48.8566 + (i * 0.0015),\n          lng: 2.3522 - (i * 0.0008),\n          elevation: 120 + Math.cos(i) * 40\n        })),\n        type: 'cycling'\n      }\n    ]);\n\n    // Utiliser les données fictives si pas de données sauvegardées\n    if (challenges.length === 0) {\n      setChallenges(mockChallenges);\n    }\n    setSegments(mockSegments);\n\n    if (user && friendships.length === 0) {\n      const userFriendships = generateFriendships(mockUsers, user.id);\n      const suggestions = generateFriendSuggestions(mockUsers, user.id, userFriendships);\n      setFriendships(userFriendships);\n      setFriendSuggestions(suggestions);\n    }\n  }, [user]);\n\n  const acceptedFriends = friendships.filter(f => f.status === 'accepted');\n  const pendingRequests = friendships.filter(f => f.status === 'pending');\n  const activeChallenges = challenges.filter(c => c.isActive);\n  const completedChallenges = challenges.filter(c => !c.isActive);\n\n  const handleAcceptFriend = (friendshipId) => {\n    setFriendships(prev => prev.map(f => \n      f.id === friendshipId ? { ...f, status: 'accepted' } : f\n    ));\n  };\n\n  const handleRejectFriend = (friendshipId) => {\n    setFriendships(prev => prev.filter(f => f.id !== friendshipId));\n  };\n\n  const handleJoinChallenge = (challengeId) => {\n    const updatedChallenges = challenges.map(challenge => {\n      if (challenge.id === challengeId) {\n        const newParticipant = {\n          user: {\n            id: user.id,\n            firstName: user.firstName,\n            lastName: user.lastName,\n            avatar: user.avatar || `https://ui-avatars.com/api/?name=${user.firstName}+${user.lastName}`\n          },\n          progress: 0,\n          rank: challenge.participants.length + 1,\n          joinedAt: new Date(),\n          isCompleted: false,\n          lastActivity: new Date()\n        };\n\n        return {\n          ...challenge,\n          participants: [...challenge.participants, newParticipant]\n        };\n      }\n      return challenge;\n    });\n\n    setChallenges(updatedChallenges);\n    localStorage.setItem('socialChallenges', JSON.stringify(updatedChallenges));\n  };\n\n  const createChallenge = () => {\n    if (!newChallenge.name || !newChallenge.target) return;\n\n    const challenge = {\n      id: `challenge_${Date.now()}`,\n      name: newChallenge.name,\n      description: newChallenge.description,\n      type: newChallenge.type,\n      target: parseFloat(newChallenge.target),\n      duration: newChallenge.duration,\n      difficulty: newChallenge.difficulty,\n      isActive: true,\n      startDate: new Date(),\n      endDate: new Date(Date.now() + newChallenge.duration * 24 * 60 * 60 * 1000),\n      createdBy: user?.firstName || 'Utilisateur',\n      participants: [{\n        user: {\n          id: user?.id || 'user1',\n          firstName: user?.firstName || 'Utilisateur',\n          lastName: user?.lastName || '',\n          avatar: user?.avatar || `https://ui-avatars.com/api/?name=${user?.firstName}+${user?.lastName}`\n        },\n        progress: 0,\n        rank: 1,\n        joinedAt: new Date(),\n        isCompleted: false,\n        lastActivity: new Date()\n      }],\n      icon: getChallengeIcon(newChallenge.type)\n    };\n\n    const updatedChallenges = [challenge, ...challenges];\n    setChallenges(updatedChallenges);\n    localStorage.setItem('socialChallenges', JSON.stringify(updatedChallenges));\n\n    setShowCreateChallenge(false);\n    setNewChallenge({\n      name: '',\n      description: '',\n      type: 'distance',\n      target: '',\n      duration: 7,\n      difficulty: 'modéré'\n    });\n  };\n\n  const deleteChallenge = (challengeId) => {\n    const updatedChallenges = challenges.filter(c => c.id !== challengeId);\n    setChallenges(updatedChallenges);\n    localStorage.setItem('socialChallenges', JSON.stringify(updatedChallenges));\n  };\n\n  const updateChallengeProgress = (challengeId, progress) => {\n    const updatedChallenges = challenges.map(challenge => {\n      if (challenge.id === challengeId) {\n        const updatedParticipants = challenge.participants.map(p => {\n          if (p.user.id === user?.id) {\n            return {\n              ...p,\n              progress: Math.min(progress, challenge.target),\n              isCompleted: progress >= challenge.target,\n              lastActivity: new Date()\n            };\n          }\n          return p;\n        });\n\n        // Recalculer les rangs\n        const sortedParticipants = updatedParticipants.sort((a, b) => b.progress - a.progress);\n        sortedParticipants.forEach((p, index) => {\n          p.rank = index + 1;\n        });\n\n        return {\n          ...challenge,\n          participants: sortedParticipants\n        };\n      }\n      return challenge;\n    });\n\n    setChallenges(updatedChallenges);\n    localStorage.setItem('socialChallenges', JSON.stringify(updatedChallenges));\n  };\n\n  const filteredFriends = acceptedFriends.filter(friendship =>\n    friendship.user2.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    friendship.user2.lastName.toLowerCase().includes(searchTerm.toLowerCase())\n  );\n\n  const getDifficultyColor = (difficulty) => {\n    switch (difficulty) {\n      case 'facile': return 'text-green-600 bg-green-100';\n      case 'modéré': return 'text-yellow-600 bg-yellow-100';\n      case 'difficile': return 'text-red-600 bg-red-100';\n      default: return 'text-gray-600 bg-gray-100';\n    }\n  };\n\n  const getTypeIcon = (type) => {\n    switch (type) {\n      case 'running': return '🏃‍♂️';\n      case 'cycling': return '🚴‍♂️';\n      case 'hiking': return '🥾';\n      case 'walking': return '🚶‍♂️';\n      default: return '📍';\n    }\n  };\n\n  const getChallengeIcon = (type) => {\n    switch (type) {\n      case 'distance': return '📏';\n      case 'activities': return '🏃‍♂️';\n      case 'time': return '⏱️';\n      case 'streak': return '🔥';\n      default: return '🎯';\n    }\n  };\n\n  const addActivity = (activity) => {\n    const newActivity = {\n      id: Date.now(),\n      user: {\n        id: user?.id || 'user1',\n        firstName: user?.firstName || 'Utilisateur',\n        lastName: user?.lastName || '',\n        avatar: user?.avatar || `https://ui-avatars.com/api/?name=${user?.firstName}+${user?.lastName}`\n      },\n      ...activity,\n      timestamp: new Date(),\n      likes: [],\n      comments: []\n    };\n\n    const updatedActivities = [newActivity, ...userActivities];\n    setUserActivities(updatedActivities);\n    localStorage.setItem('userActivities', JSON.stringify(updatedActivities));\n  };\n\n  const likeActivity = (activityId) => {\n    const updatedActivities = userActivities.map(activity => {\n      if (activity.id === activityId) {\n        const hasLiked = activity.likes.includes(user?.id);\n        return {\n          ...activity,\n          likes: hasLiked\n            ? activity.likes.filter(id => id !== user?.id)\n            : [...activity.likes, user?.id]\n        };\n      }\n      return activity;\n    });\n\n    setUserActivities(updatedActivities);\n    localStorage.setItem('userActivities', JSON.stringify(updatedActivities));\n  };\n\n  const addComment = (activityId, comment) => {\n    const updatedActivities = userActivities.map(activity => {\n      if (activity.id === activityId) {\n        const newComment = {\n          id: Date.now(),\n          user: {\n            id: user?.id || 'user1',\n            firstName: user?.firstName || 'Utilisateur',\n            lastName: user?.lastName || ''\n          },\n          text: comment,\n          timestamp: new Date()\n        };\n\n        return {\n          ...activity,\n          comments: [...activity.comments, newComment]\n        };\n      }\n      return activity;\n    });\n\n    setUserActivities(updatedActivities);\n    localStorage.setItem('userActivities', JSON.stringify(updatedActivities));\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Header */}\n        <div className=\"text-center mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-4\">\n            Communauté FitTracker\n          </h1>\n          <p className=\"text-lg text-gray-600 max-w-3xl mx-auto\">\n            Connectez-vous avec d'autres athlètes, participez à des défis et \n            découvrez les classements des segments populaires.\n          </p>\n        </div>\n\n        {/* Tabs */}\n        <div className=\"flex space-x-1 bg-gray-100 p-1 rounded-lg mb-8\">\n          <button\n            onClick={() => setActiveTab('friends')}\n            className={`flex-1 py-3 px-4 rounded-md text-sm font-medium transition-colors ${\n              activeTab === 'friends'\n                ? 'bg-white text-blue-600 shadow-sm'\n                : 'text-gray-600 hover:text-gray-900'\n            }`}\n          >\n            <FiUsers className=\"inline mr-2\" />\n            Amis ({acceptedFriends.length})\n          </button>\n          <button\n            onClick={() => setActiveTab('challenges')}\n            className={`flex-1 py-3 px-4 rounded-md text-sm font-medium transition-colors ${\n              activeTab === 'challenges'\n                ? 'bg-white text-blue-600 shadow-sm'\n                : 'text-gray-600 hover:text-gray-900'\n            }`}\n          >\n            <FiTarget className=\"inline mr-2\" />\n            Défis ({activeChallenges.length})\n          </button>\n          <button\n            onClick={() => setActiveTab('leaderboards')}\n            className={`flex-1 py-3 px-4 rounded-md text-sm font-medium transition-colors ${\n              activeTab === 'leaderboards'\n                ? 'bg-white text-blue-600 shadow-sm'\n                : 'text-gray-600 hover:text-gray-900'\n            }`}\n          >\n            <FiTrendingUp className=\"inline mr-2\" />\n            Classements\n          </button>\n        </div>\n\n        {/* Friends Tab */}\n        {activeTab === 'friends' && (\n          <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n            {/* Friends List */}\n            <div className=\"lg:col-span-2\">\n              <div className=\"bg-white rounded-lg shadow-lg p-6\">\n                <div className=\"flex items-center justify-between mb-6\">\n                  <h2 className=\"text-xl font-semibold text-gray-900\">\n                    Mes Amis ({acceptedFriends.length})\n                  </h2>\n                  <div className=\"relative\">\n                    <FiSearch className=\"absolute left-3 top-3 h-4 w-4 text-gray-400\" />\n                    <input\n                      type=\"text\"\n                      placeholder=\"Rechercher un ami...\"\n                      value={searchTerm}\n                      onChange={(e) => setSearchTerm(e.target.value)}\n                      className=\"pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    />\n                  </div>\n                </div>\n\n                <div className=\"space-y-4\">\n                  {filteredFriends.map(friendship => (\n                    <div key={friendship.id} className=\"flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50\">\n                      <div className=\"flex items-center space-x-4\">\n                        <img\n                          src={friendship.user2.avatar}\n                          alt={`${friendship.user2.firstName} ${friendship.user2.lastName}`}\n                          className=\"w-12 h-12 rounded-full\"\n                        />\n                        <div>\n                          <h3 className=\"font-medium text-gray-900\">\n                            {friendship.user2.firstName} {friendship.user2.lastName}\n                          </h3>\n                          <div className=\"flex items-center space-x-4 text-sm text-gray-600\">\n                            <span className=\"flex items-center\">\n                              <FiMapPin className=\"h-4 w-4 mr-1\" />\n                              {friendship.user2.city}\n                            </span>\n                            <span className=\"flex items-center\">\n                              <FiClock className=\"h-4 w-4 mr-1\" />\n                              {friendship.user2.isOnline ? 'En ligne' : 'Hors ligne'}\n                            </span>\n                          </div>\n                          <div className=\"text-xs text-gray-500 mt-1\">\n                            {friendship.mutualFriends} amis en commun • {friendship.sharedActivities} activités partagées\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"flex items-center space-x-2\">\n                        <button className=\"p-2 text-blue-600 hover:bg-blue-50 rounded-lg\">\n                          <FiMessageCircle className=\"h-5 w-5\" />\n                        </button>\n                        <div className=\"text-right\">\n                          <div className=\"text-sm font-medium text-gray-900\">\n                            {friendship.user2.stats.totalDistance} km\n                          </div>\n                          <div className=\"text-xs text-gray-500\">\n                            {friendship.user2.stats.totalActivities} activités\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </div>\n\n            {/* Sidebar */}\n            <div className=\"space-y-6\">\n              {/* Pending Requests */}\n              {pendingRequests.length > 0 && (\n                <div className=\"bg-white rounded-lg shadow-lg p-6\">\n                  <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">\n                    Demandes d'amitié ({pendingRequests.length})\n                  </h3>\n                  <div className=\"space-y-3\">\n                    {pendingRequests.map(request => (\n                      <div key={request.id} className=\"flex items-center justify-between p-3 border border-gray-200 rounded-lg\">\n                        <div className=\"flex items-center space-x-3\">\n                          <img\n                            src={request.user1.avatar}\n                            alt={`${request.user1.firstName} ${request.user1.lastName}`}\n                            className=\"w-10 h-10 rounded-full\"\n                          />\n                          <div>\n                            <h4 className=\"font-medium text-gray-900 text-sm\">\n                              {request.user1.firstName} {request.user1.lastName}\n                            </h4>\n                            <p className=\"text-xs text-gray-600\">{request.user1.city}</p>\n                          </div>\n                        </div>\n                        <div className=\"flex space-x-1\">\n                          <button\n                            onClick={() => handleAcceptFriend(request.id)}\n                            className=\"p-1 text-green-600 hover:bg-green-50 rounded\"\n                          >\n                            <FiCheck className=\"h-4 w-4\" />\n                          </button>\n                          <button\n                            onClick={() => handleRejectFriend(request.id)}\n                            className=\"p-1 text-red-600 hover:bg-red-50 rounded\"\n                          >\n                            <FiX className=\"h-4 w-4\" />\n                          </button>\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              )}\n\n              {/* Friend Suggestions */}\n              <div className=\"bg-white rounded-lg shadow-lg p-6\">\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">\n                  Suggestions d'amis\n                </h3>\n                <div className=\"space-y-3\">\n                  {friendSuggestions.slice(0, 5).map(suggestion => (\n                    <div key={suggestion.user.id} className=\"flex items-center justify-between p-3 border border-gray-200 rounded-lg\">\n                      <div className=\"flex items-center space-x-3\">\n                        <img\n                          src={suggestion.user.avatar}\n                          alt={`${suggestion.user.firstName} ${suggestion.user.lastName}`}\n                          className=\"w-10 h-10 rounded-full\"\n                        />\n                        <div>\n                          <h4 className=\"font-medium text-gray-900 text-sm\">\n                            {suggestion.user.firstName} {suggestion.user.lastName}\n                          </h4>\n                          <p className=\"text-xs text-gray-600\">{suggestion.reason}</p>\n                        </div>\n                      </div>\n                      <button className=\"p-1 text-blue-600 hover:bg-blue-50 rounded\">\n                        <FiUserPlus className=\"h-4 w-4\" />\n                      </button>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Challenges Tab */}\n        {activeTab === 'challenges' && (\n          <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n            {/* Active Challenges */}\n            <div className=\"lg:col-span-2\">\n              <div className=\"bg-white rounded-lg shadow-lg p-6\">\n                <div className=\"flex items-center justify-between mb-6\">\n                  <h2 className=\"text-xl font-semibold text-gray-900\">\n                    Défis Actifs ({activeChallenges.length})\n                  </h2>\n                  <button\n                    onClick={() => setShowCreateChallenge(true)}\n                    className=\"bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center\"\n                  >\n                    <FiPlus className=\"mr-2\" />\n                    Créer un défi\n                  </button>\n                </div>\n                \n                <div className=\"grid gap-6\">\n                  {activeChallenges.map(challenge => (\n                    <div key={challenge.id} className=\"border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow\">\n                      <div className=\"flex items-start justify-between mb-4\">\n                        <div className=\"flex items-center space-x-3\">\n                          <span className=\"text-2xl\">{getChallengeIcon(challenge.type)}</span>\n                          <div>\n                            <h3 className=\"font-semibold text-gray-900\">{challenge.name}</h3>\n                            <p className=\"text-sm text-gray-600\">{challenge.description}</p>\n                          </div>\n                        </div>\n                        <span className={`px-3 py-1 rounded-full text-xs font-medium ${\n                          challenge.category === 'team' ? 'bg-purple-100 text-purple-700' : 'bg-blue-100 text-blue-700'\n                        }`}>\n                          {challenge.category === 'team' ? 'Équipe' : 'Individuel'}\n                        </span>\n                      </div>\n                      \n                      <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 mb-4\">\n                        <div className=\"text-center\">\n                          <div className=\"text-lg font-bold text-gray-900\">{challenge.participants.length}</div>\n                          <div className=\"text-xs text-gray-600\">Participants</div>\n                        </div>\n                        <div className=\"text-center\">\n                          <div className=\"text-lg font-bold text-gray-900\">{challenge.target}</div>\n                          <div className=\"text-xs text-gray-600\">{challenge.unit}</div>\n                        </div>\n                        <div className=\"text-center\">\n                          <div className=\"text-lg font-bold text-gray-900\">{challenge.duration}</div>\n                          <div className=\"text-xs text-gray-600\">jours</div>\n                        </div>\n                        <div className=\"text-center\">\n                          <div className=\"text-lg font-bold text-gray-900\">\n                            {Math.ceil((challenge.endDate - new Date()) / (24 * 60 * 60 * 1000))}\n                          </div>\n                          <div className=\"text-xs text-gray-600\">jours restants</div>\n                        </div>\n                      </div>\n                      \n                      {/* Barre de progression pour l'utilisateur */}\n                      {challenge.participants.find(p => p.user.id === user?.id) && (\n                        <div className=\"mb-4\">\n                          <div className=\"flex items-center justify-between text-sm mb-2\">\n                            <span>Votre progression</span>\n                            <span>{challenge.participants.find(p => p.user.id === user?.id)?.progress || 0} / {challenge.target}</span>\n                          </div>\n                          <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                            <div\n                              className=\"bg-blue-600 h-2 rounded-full transition-all duration-300\"\n                              style={{\n                                width: `${Math.min(100, ((challenge.participants.find(p => p.user.id === user?.id)?.progress || 0) / challenge.target) * 100)}%`\n                              }}\n                            ></div>\n                          </div>\n                        </div>\n                      )}\n\n                      <div className=\"flex items-center justify-between\">\n                        <div className=\"text-sm text-gray-600\">\n                          <span className=\"font-medium\">Prix :</span> {challenge.prize || 'Badge de réussite'}\n                        </div>\n                        <div className=\"flex items-center space-x-2\">\n                          {challenge.participants.find(p => p.user.id === user?.id) ? (\n                            <>\n                              <input\n                                type=\"number\"\n                                placeholder=\"Progression\"\n                                className=\"w-20 px-2 py-1 border border-gray-300 rounded text-sm\"\n                                onKeyPress={(e) => {\n                                  if (e.key === 'Enter') {\n                                    updateChallengeProgress(challenge.id, parseFloat(e.target.value));\n                                    e.target.value = '';\n                                  }\n                                }}\n                              />\n                              <button\n                                onClick={() => deleteChallenge(challenge.id)}\n                                className=\"text-red-600 hover:text-red-800 transition-colors\"\n                                title=\"Supprimer le défi\"\n                              >\n                                <FiTrash2 className=\"h-4 w-4\" />\n                              </button>\n                            </>\n                          ) : (\n                            <button\n                              onClick={() => handleJoinChallenge(challenge.id)}\n                              className=\"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm\"\n                            >\n                              Rejoindre\n                            </button>\n                          )}\n                        </div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </div>\n\n            {/* Challenge Details Sidebar */}\n            <div className=\"space-y-6\">\n              <div className=\"bg-white rounded-lg shadow-lg p-6\">\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">\n                  Mes Défis en Cours\n                </h3>\n                <div className=\"space-y-3\">\n                  {activeChallenges.slice(0, 3).map(challenge => {\n                    const userParticipation = challenge.participants.find(p => p.user.id === user?.id);\n                    if (!userParticipation) return null;\n                    \n                    const progressPercentage = (userParticipation.progress / challenge.target) * 100;\n                    \n                    return (\n                      <div key={challenge.id} className=\"p-3 border border-gray-200 rounded-lg\">\n                        <div className=\"flex items-center justify-between mb-2\">\n                          <h4 className=\"font-medium text-gray-900 text-sm\">{challenge.name}</h4>\n                          <span className=\"text-xs text-gray-600\">#{userParticipation.rank}</span>\n                        </div>\n                        <div className=\"w-full bg-gray-200 rounded-full h-2 mb-2\">\n                          <div\n                            className=\"bg-blue-600 h-2 rounded-full\"\n                            style={{ width: `${Math.min(progressPercentage, 100)}%` }}\n                          ></div>\n                        </div>\n                        <div className=\"text-xs text-gray-600\">\n                          {userParticipation.progress} / {challenge.target} {challenge.unit}\n                        </div>\n                      </div>\n                    );\n                  })}\n                </div>\n              </div>\n\n              <div className=\"bg-white rounded-lg shadow-lg p-6\">\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">\n                  Défis Terminés\n                </h3>\n                <div className=\"space-y-3\">\n                  {completedChallenges.slice(0, 3).map(challenge => (\n                    <div key={challenge.id} className=\"p-3 border border-gray-200 rounded-lg\">\n                      <h4 className=\"font-medium text-gray-900 text-sm\">{challenge.name}</h4>\n                      <div className=\"flex items-center justify-between mt-2\">\n                        <span className=\"text-xs text-gray-600\">\n                          {challenge.participants.length} participants\n                        </span>\n                        <span className=\"text-xs text-green-600 font-medium\">Terminé</span>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Leaderboards Tab */}\n        {activeTab === 'leaderboards' && (\n          <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n            {/* Segments Leaderboards */}\n            <div className=\"lg:col-span-2\">\n              <div className=\"bg-white rounded-lg shadow-lg p-6\">\n                <h2 className=\"text-xl font-semibold text-gray-900 mb-6\">\n                  Classements des Segments\n                </h2>\n                \n                <div className=\"space-y-6\">\n                  {segments.slice(0, 5).map(segment => (\n                    <div key={segment.id} className=\"border border-gray-200 rounded-lg p-6\">\n                      <div className=\"flex items-start justify-between mb-4\">\n                        <div className=\"flex items-center space-x-3\">\n                          <span className=\"text-xl\">{getTypeIcon(segment.type)}</span>\n                          <div>\n                            <h3 className=\"font-semibold text-gray-900\">{segment.name}</h3>\n                            <p className=\"text-sm text-gray-600\">{segment.description}</p>\n                            <div className=\"flex items-center space-x-4 mt-1\">\n                              <span className=\"text-xs text-gray-500\">{segment.distance.toFixed(1)} km</span>\n                              <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(segment.difficulty)}`}>\n                                {segment.difficulty}\n                              </span>\n                              <span className=\"text-xs text-gray-500\">{segment.totalAttempts} tentatives</span>\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                      \n                      <div className=\"space-y-2\">\n                        <h4 className=\"font-medium text-gray-900 text-sm mb-3\">Top 5</h4>\n                        {segment.leaderboard.slice(0, 5).map((entry, index) => (\n                          <div key={index} className=\"flex items-center justify-between p-2 rounded-lg hover:bg-gray-50\">\n                            <div className=\"flex items-center space-x-3\">\n                              <span className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold ${\n                                index === 0 ? 'bg-yellow-100 text-yellow-800' :\n                                index === 1 ? 'bg-gray-100 text-gray-800' :\n                                index === 2 ? 'bg-orange-100 text-orange-800' :\n                                'bg-blue-100 text-blue-800'\n                              }`}>\n                                {entry.rank}\n                              </span>\n                              <div>\n                                <span className=\"font-medium text-gray-900 text-sm\">{entry.athlete}</span>\n                                {entry.isLocalLegend && (\n                                  <FiAward className=\"inline ml-2 h-4 w-4 text-yellow-500\" />\n                                )}\n                              </div>\n                            </div>\n                            <div className=\"text-right\">\n                              <div className=\"font-medium text-gray-900 text-sm\">\n                                {formatTime(entry.time)}\n                              </div>\n                              <div className=\"text-xs text-gray-500\">\n                                {formatPace(entry.pace)}\n                              </div>\n                            </div>\n                          </div>\n                        ))}\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </div>\n\n            {/* Leaderboard Stats */}\n            <div className=\"space-y-6\">\n              <div className=\"bg-white rounded-lg shadow-lg p-6\">\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">\n                  Mes Performances\n                </h3>\n                <div className=\"space-y-4\">\n                  <div className=\"flex items-center justify-between p-3 bg-yellow-50 rounded-lg\">\n                    <div className=\"flex items-center space-x-2\">\n                      <FiAward className=\"h-5 w-5 text-yellow-600\" />\n                      <span className=\"font-medium text-gray-900\">Légendes Locales</span>\n                    </div>\n                    <span className=\"text-lg font-bold text-yellow-600\">2</span>\n                  </div>\n                  \n                  <div className=\"flex items-center justify-between p-3 bg-blue-50 rounded-lg\">\n                    <div className=\"flex items-center space-x-2\">\n                      <FiTrendingUp className=\"h-5 w-5 text-blue-600\" />\n                      <span className=\"font-medium text-gray-900\">Top 10</span>\n                    </div>\n                    <span className=\"text-lg font-bold text-blue-600\">8</span>\n                  </div>\n                  \n                  <div className=\"flex items-center justify-between p-3 bg-green-50 rounded-lg\">\n                    <div className=\"flex items-center space-x-2\">\n                      <FiAward className=\"h-5 w-5 text-green-600\" />\n                      <span className=\"font-medium text-gray-900\">Records Personnels</span>\n                    </div>\n                    <span className=\"text-lg font-bold text-green-600\">15</span>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"bg-white rounded-lg shadow-lg p-6\">\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">\n                  Segments Populaires\n                </h3>\n                <div className=\"space-y-3\">\n                  {segments.slice(0, 5).map(segment => (\n                    <div key={segment.id} className=\"flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer\">\n                      <div>\n                        <h4 className=\"font-medium text-gray-900 text-sm\">{segment.name}</h4>\n                        <div className=\"flex items-center space-x-2 text-xs text-gray-600\">\n                          <span>{segment.distance.toFixed(1)} km</span>\n                          <span>•</span>\n                          <span>{segment.totalAttempts} tentatives</span>\n                        </div>\n                      </div>\n                      <div className=\"text-right\">\n                        <div className=\"text-sm font-medium text-gray-900\">\n                          {formatTime(segment.leaderboard[0]?.time || 0)}\n                        </div>\n                        <div className=\"text-xs text-gray-500\">Record</div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Modal de création de défi */}\n        {showCreateChallenge && (\n          <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n            <div className=\"bg-white rounded-lg p-6 max-w-md w-full mx-4\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">\n                Créer un nouveau défi\n              </h3>\n\n              <div className=\"space-y-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Nom du défi\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={newChallenge.name}\n                    onChange={(e) => setNewChallenge({...newChallenge, name: e.target.value})}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    placeholder=\"Ex: Défi 100km en janvier\"\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Description\n                  </label>\n                  <textarea\n                    value={newChallenge.description}\n                    onChange={(e) => setNewChallenge({...newChallenge, description: e.target.value})}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    rows=\"3\"\n                    placeholder=\"Décrivez votre défi...\"\n                  />\n                </div>\n\n                <div className=\"grid grid-cols-2 gap-4\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      Type\n                    </label>\n                    <select\n                      value={newChallenge.type}\n                      onChange={(e) => setNewChallenge({...newChallenge, type: e.target.value})}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    >\n                      <option value=\"distance\">Distance (km)</option>\n                      <option value=\"activities\">Nombre d'activités</option>\n                      <option value=\"time\">Temps (minutes)</option>\n                      <option value=\"streak\">Série de jours</option>\n                    </select>\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      Objectif\n                    </label>\n                    <input\n                      type=\"number\"\n                      value={newChallenge.target}\n                      onChange={(e) => setNewChallenge({...newChallenge, target: e.target.value})}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                      placeholder=\"100\"\n                    />\n                  </div>\n                </div>\n\n                <div className=\"grid grid-cols-2 gap-4\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      Durée (jours)\n                    </label>\n                    <input\n                      type=\"number\"\n                      value={newChallenge.duration}\n                      onChange={(e) => setNewChallenge({...newChallenge, duration: parseInt(e.target.value)})}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                      min=\"1\"\n                      max=\"365\"\n                    />\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      Difficulté\n                    </label>\n                    <select\n                      value={newChallenge.difficulty}\n                      onChange={(e) => setNewChallenge({...newChallenge, difficulty: e.target.value})}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    >\n                      <option value=\"facile\">Facile</option>\n                      <option value=\"modéré\">Modéré</option>\n                      <option value=\"difficile\">Difficile</option>\n                    </select>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"flex space-x-3 mt-6\">\n                <button\n                  onClick={createChallenge}\n                  className=\"flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors\"\n                >\n                  Créer le défi\n                </button>\n                <button\n                  onClick={() => setShowCreateChallenge(false)}\n                  className=\"flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-400 transition-colors\"\n                >\n                  Annuler\n                </button>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default Social;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,OAAO,EACPC,YAAY,EACZC,OAAO,EACPC,UAAU,EACVC,eAAe,EACfC,QAAQ,EACRC,OAAO,EACPC,QAAQ,EACRC,OAAO,EACPC,GAAG,EACHC,QAAQ,EACRC,MAAM,EACNC,OAAO,EACPC,QAAQ,EACRC,QAAQ,EACRC,OAAO,EACPC,UAAU,QACL,gBAAgB;AACvB,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SACEC,iBAAiB,EACjBC,kBAAkB,EAClBC,mBAAmB,EACnBC,yBAAyB,EACzBC,UAAU,EACVC,UAAU,QACL,sBAAsB;AAC7B,SAASC,gBAAgB,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErD,MAAMC,MAAM,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnB,MAAM;IAAEC;EAAK,CAAC,GAAGd,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACe,SAAS,EAAEC,YAAY,CAAC,GAAGnC,QAAQ,CAAC,SAAS,CAAC;EAErD,MAAM,CAACoC,UAAU,EAAEC,aAAa,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACsC,QAAQ,EAAEC,WAAW,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACwC,WAAW,EAAEC,cAAc,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC0C,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAAC4C,UAAU,EAAEC,aAAa,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC8C,cAAc,EAAEC,iBAAiB,CAAC,GAAG/C,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACgD,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGjD,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACkD,YAAY,EAAEC,eAAe,CAAC,GAAGnD,QAAQ,CAAC;IAC/CoD,IAAI,EAAE,EAAE;IACRC,WAAW,EAAE,EAAE;IACfC,IAAI,EAAE,UAAU;IAChBC,MAAM,EAAE,EAAE;IACVC,QAAQ,EAAE,CAAC;IACXC,UAAU,EAAE;EACd,CAAC,CAAC;EAGFxD,SAAS,CAAC,MAAM;IACd;IACA,IAAI;MACF,MAAMyD,eAAe,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,kBAAkB,CAAC,IAAI,IAAI,CAAC;MACpF,MAAMC,eAAe,GAAGJ,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC,IAAI,IAAI,CAAC;MAClF,MAAME,gBAAgB,GAAGL,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC;MAEhF,IAAIJ,eAAe,CAACO,MAAM,GAAG,CAAC,EAAE;QAC9B5B,aAAa,CAACqB,eAAe,CAAC;MAChC;MACAX,iBAAiB,CAACgB,eAAe,CAAC;MAClC,IAAIC,gBAAgB,CAACC,MAAM,GAAG,CAAC,EAAE;QAC/BxB,cAAc,CAACuB,gBAAgB,CAAC;MAClC;IACF,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iDAAiD,EAAEA,KAAK,CAAC;IACzE;;IAEA;IACA,MAAME,SAAS,GAAGhD,iBAAiB,CAAC,EAAE,CAAC;IACvC,MAAMiD,cAAc,GAAGhD,kBAAkB,CAAC+C,SAAS,CAAC;IACpD,MAAME,YAAY,GAAG5C,gBAAgB,CAAC;IACpC;IACA;MACE0B,IAAI,EAAE,kBAAkB;MACxBmB,MAAM,EAAEC,KAAK,CAACC,IAAI,CAAC;QAAER,MAAM,EAAE;MAAG,CAAC,EAAE,CAACS,CAAC,EAAEC,CAAC,MAAM;QAC5CC,GAAG,EAAE,OAAO,GAAID,CAAC,GAAG,KAAM;QAC1BE,GAAG,EAAE,MAAM,GAAIF,CAAC,GAAG,KAAM;QACzBG,SAAS,EAAE,GAAG,GAAGC,IAAI,CAACC,GAAG,CAACL,CAAC,CAAC,GAAG;MACjC,CAAC,CAAC,CAAC;MACHrB,IAAI,EAAE;IACR,CAAC,EACD;MACEF,IAAI,EAAE,qBAAqB;MAC3BmB,MAAM,EAAEC,KAAK,CAACC,IAAI,CAAC;QAAER,MAAM,EAAE;MAAG,CAAC,EAAE,CAACS,CAAC,EAAEC,CAAC,MAAM;QAC5CC,GAAG,EAAE,OAAO,GAAID,CAAC,GAAG,MAAO;QAC3BE,GAAG,EAAE,MAAM,GAAIF,CAAC,GAAG,MAAO;QAC1BG,SAAS,EAAE,GAAG,GAAGC,IAAI,CAACE,GAAG,CAACN,CAAC,CAAC,GAAG;MACjC,CAAC,CAAC,CAAC;MACHrB,IAAI,EAAE;IACR,CAAC,CACF,CAAC;;IAEF;IACA,IAAIlB,UAAU,CAAC6B,MAAM,KAAK,CAAC,EAAE;MAC3B5B,aAAa,CAACgC,cAAc,CAAC;IAC/B;IACA9B,WAAW,CAAC+B,YAAY,CAAC;IAEzB,IAAIrC,IAAI,IAAIO,WAAW,CAACyB,MAAM,KAAK,CAAC,EAAE;MACpC,MAAMiB,eAAe,GAAG5D,mBAAmB,CAAC8C,SAAS,EAAEnC,IAAI,CAACkD,EAAE,CAAC;MAC/D,MAAMC,WAAW,GAAG7D,yBAAyB,CAAC6C,SAAS,EAAEnC,IAAI,CAACkD,EAAE,EAAED,eAAe,CAAC;MAClFzC,cAAc,CAACyC,eAAe,CAAC;MAC/BvC,oBAAoB,CAACyC,WAAW,CAAC;IACnC;EACF,CAAC,EAAE,CAACnD,IAAI,CAAC,CAAC;EAEV,MAAMoD,eAAe,GAAG7C,WAAW,CAAC8C,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAK,UAAU,CAAC;EACxE,MAAMC,eAAe,GAAGjD,WAAW,CAAC8C,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAK,SAAS,CAAC;EACvE,MAAME,gBAAgB,GAAGtD,UAAU,CAACkD,MAAM,CAACK,CAAC,IAAIA,CAAC,CAACC,QAAQ,CAAC;EAC3D,MAAMC,mBAAmB,GAAGzD,UAAU,CAACkD,MAAM,CAACK,CAAC,IAAI,CAACA,CAAC,CAACC,QAAQ,CAAC;EAE/D,MAAME,kBAAkB,GAAIC,YAAY,IAAK;IAC3CtD,cAAc,CAACuD,IAAI,IAAIA,IAAI,CAACC,GAAG,CAACV,CAAC,IAC/BA,CAAC,CAACJ,EAAE,KAAKY,YAAY,GAAG;MAAE,GAAGR,CAAC;MAAEC,MAAM,EAAE;IAAW,CAAC,GAAGD,CACzD,CAAC,CAAC;EACJ,CAAC;EAED,MAAMW,kBAAkB,GAAIH,YAAY,IAAK;IAC3CtD,cAAc,CAACuD,IAAI,IAAIA,IAAI,CAACV,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACJ,EAAE,KAAKY,YAAY,CAAC,CAAC;EACjE,CAAC;EAED,MAAMI,mBAAmB,GAAIC,WAAW,IAAK;IAC3C,MAAMC,iBAAiB,GAAGjE,UAAU,CAAC6D,GAAG,CAACK,SAAS,IAAI;MACpD,IAAIA,SAAS,CAACnB,EAAE,KAAKiB,WAAW,EAAE;QAChC,MAAMG,cAAc,GAAG;UACrBtE,IAAI,EAAE;YACJkD,EAAE,EAAElD,IAAI,CAACkD,EAAE;YACXqB,SAAS,EAAEvE,IAAI,CAACuE,SAAS;YACzBC,QAAQ,EAAExE,IAAI,CAACwE,QAAQ;YACvBC,MAAM,EAAEzE,IAAI,CAACyE,MAAM,IAAI,oCAAoCzE,IAAI,CAACuE,SAAS,IAAIvE,IAAI,CAACwE,QAAQ;UAC5F,CAAC;UACDE,QAAQ,EAAE,CAAC;UACXC,IAAI,EAAEN,SAAS,CAACO,YAAY,CAAC5C,MAAM,GAAG,CAAC;UACvC6C,QAAQ,EAAE,IAAIC,IAAI,CAAC,CAAC;UACpBC,WAAW,EAAE,KAAK;UAClBC,YAAY,EAAE,IAAIF,IAAI,CAAC;QACzB,CAAC;QAED,OAAO;UACL,GAAGT,SAAS;UACZO,YAAY,EAAE,CAAC,GAAGP,SAAS,CAACO,YAAY,EAAEN,cAAc;QAC1D,CAAC;MACH;MACA,OAAOD,SAAS;IAClB,CAAC,CAAC;IAEFjE,aAAa,CAACgE,iBAAiB,CAAC;IAChCxC,YAAY,CAACqD,OAAO,CAAC,kBAAkB,EAAEvD,IAAI,CAACwD,SAAS,CAACd,iBAAiB,CAAC,CAAC;EAC7E,CAAC;EAED,MAAMe,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAI,CAAClE,YAAY,CAACE,IAAI,IAAI,CAACF,YAAY,CAACK,MAAM,EAAE;IAEhD,MAAM+C,SAAS,GAAG;MAChBnB,EAAE,EAAE,aAAa4B,IAAI,CAACM,GAAG,CAAC,CAAC,EAAE;MAC7BjE,IAAI,EAAEF,YAAY,CAACE,IAAI;MACvBC,WAAW,EAAEH,YAAY,CAACG,WAAW;MACrCC,IAAI,EAAEJ,YAAY,CAACI,IAAI;MACvBC,MAAM,EAAE+D,UAAU,CAACpE,YAAY,CAACK,MAAM,CAAC;MACvCC,QAAQ,EAAEN,YAAY,CAACM,QAAQ;MAC/BC,UAAU,EAAEP,YAAY,CAACO,UAAU;MACnCmC,QAAQ,EAAE,IAAI;MACd2B,SAAS,EAAE,IAAIR,IAAI,CAAC,CAAC;MACrBS,OAAO,EAAE,IAAIT,IAAI,CAACA,IAAI,CAACM,GAAG,CAAC,CAAC,GAAGnE,YAAY,CAACM,QAAQ,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;MAC3EiE,SAAS,EAAE,CAAAxF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEuE,SAAS,KAAI,aAAa;MAC3CK,YAAY,EAAE,CAAC;QACb5E,IAAI,EAAE;UACJkD,EAAE,EAAE,CAAAlD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkD,EAAE,KAAI,OAAO;UACvBqB,SAAS,EAAE,CAAAvE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEuE,SAAS,KAAI,aAAa;UAC3CC,QAAQ,EAAE,CAAAxE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwE,QAAQ,KAAI,EAAE;UAC9BC,MAAM,EAAE,CAAAzE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyE,MAAM,KAAI,oCAAoCzE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEuE,SAAS,IAAIvE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwE,QAAQ;QAC/F,CAAC;QACDE,QAAQ,EAAE,CAAC;QACXC,IAAI,EAAE,CAAC;QACPE,QAAQ,EAAE,IAAIC,IAAI,CAAC,CAAC;QACpBC,WAAW,EAAE,KAAK;QAClBC,YAAY,EAAE,IAAIF,IAAI,CAAC;MACzB,CAAC,CAAC;MACFW,IAAI,EAAEC,gBAAgB,CAACzE,YAAY,CAACI,IAAI;IAC1C,CAAC;IAED,MAAM+C,iBAAiB,GAAG,CAACC,SAAS,EAAE,GAAGlE,UAAU,CAAC;IACpDC,aAAa,CAACgE,iBAAiB,CAAC;IAChCxC,YAAY,CAACqD,OAAO,CAAC,kBAAkB,EAAEvD,IAAI,CAACwD,SAAS,CAACd,iBAAiB,CAAC,CAAC;IAE3EpD,sBAAsB,CAAC,KAAK,CAAC;IAC7BE,eAAe,CAAC;MACdC,IAAI,EAAE,EAAE;MACRC,WAAW,EAAE,EAAE;MACfC,IAAI,EAAE,UAAU;MAChBC,MAAM,EAAE,EAAE;MACVC,QAAQ,EAAE,CAAC;MACXC,UAAU,EAAE;IACd,CAAC,CAAC;EACJ,CAAC;EAED,MAAMmE,eAAe,GAAIxB,WAAW,IAAK;IACvC,MAAMC,iBAAiB,GAAGjE,UAAU,CAACkD,MAAM,CAACK,CAAC,IAAIA,CAAC,CAACR,EAAE,KAAKiB,WAAW,CAAC;IACtE/D,aAAa,CAACgE,iBAAiB,CAAC;IAChCxC,YAAY,CAACqD,OAAO,CAAC,kBAAkB,EAAEvD,IAAI,CAACwD,SAAS,CAACd,iBAAiB,CAAC,CAAC;EAC7E,CAAC;EAED,MAAMwB,uBAAuB,GAAGA,CAACzB,WAAW,EAAEO,QAAQ,KAAK;IACzD,MAAMN,iBAAiB,GAAGjE,UAAU,CAAC6D,GAAG,CAACK,SAAS,IAAI;MACpD,IAAIA,SAAS,CAACnB,EAAE,KAAKiB,WAAW,EAAE;QAChC,MAAM0B,mBAAmB,GAAGxB,SAAS,CAACO,YAAY,CAACZ,GAAG,CAAC8B,CAAC,IAAI;UAC1D,IAAIA,CAAC,CAAC9F,IAAI,CAACkD,EAAE,MAAKlD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkD,EAAE,GAAE;YAC1B,OAAO;cACL,GAAG4C,CAAC;cACJpB,QAAQ,EAAE5B,IAAI,CAACiD,GAAG,CAACrB,QAAQ,EAAEL,SAAS,CAAC/C,MAAM,CAAC;cAC9CyD,WAAW,EAAEL,QAAQ,IAAIL,SAAS,CAAC/C,MAAM;cACzC0D,YAAY,EAAE,IAAIF,IAAI,CAAC;YACzB,CAAC;UACH;UACA,OAAOgB,CAAC;QACV,CAAC,CAAC;;QAEF;QACA,MAAME,kBAAkB,GAAGH,mBAAmB,CAACI,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACzB,QAAQ,GAAGwB,CAAC,CAACxB,QAAQ,CAAC;QACtFsB,kBAAkB,CAACI,OAAO,CAAC,CAACN,CAAC,EAAEO,KAAK,KAAK;UACvCP,CAAC,CAACnB,IAAI,GAAG0B,KAAK,GAAG,CAAC;QACpB,CAAC,CAAC;QAEF,OAAO;UACL,GAAGhC,SAAS;UACZO,YAAY,EAAEoB;QAChB,CAAC;MACH;MACA,OAAO3B,SAAS;IAClB,CAAC,CAAC;IAEFjE,aAAa,CAACgE,iBAAiB,CAAC;IAChCxC,YAAY,CAACqD,OAAO,CAAC,kBAAkB,EAAEvD,IAAI,CAACwD,SAAS,CAACd,iBAAiB,CAAC,CAAC;EAC7E,CAAC;EAED,MAAMkC,eAAe,GAAGlD,eAAe,CAACC,MAAM,CAACkD,UAAU,IACvDA,UAAU,CAACC,KAAK,CAACjC,SAAS,CAACkC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC/F,UAAU,CAAC8F,WAAW,CAAC,CAAC,CAAC,IAC3EF,UAAU,CAACC,KAAK,CAAChC,QAAQ,CAACiC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC/F,UAAU,CAAC8F,WAAW,CAAC,CAAC,CAC3E,CAAC;EAED,MAAME,kBAAkB,GAAInF,UAAU,IAAK;IACzC,QAAQA,UAAU;MAChB,KAAK,QAAQ;QAAE,OAAO,6BAA6B;MACnD,KAAK,QAAQ;QAAE,OAAO,+BAA+B;MACrD,KAAK,WAAW;QAAE,OAAO,yBAAyB;MAClD;QAAS,OAAO,2BAA2B;IAC7C;EACF,CAAC;EAED,MAAMoF,WAAW,GAAIvF,IAAI,IAAK;IAC5B,QAAQA,IAAI;MACV,KAAK,SAAS;QAAE,OAAO,OAAO;MAC9B,KAAK,SAAS;QAAE,OAAO,OAAO;MAC9B,KAAK,QAAQ;QAAE,OAAO,IAAI;MAC1B,KAAK,SAAS;QAAE,OAAO,OAAO;MAC9B;QAAS,OAAO,IAAI;IACtB;EACF,CAAC;EAED,MAAMqE,gBAAgB,GAAIrE,IAAI,IAAK;IACjC,QAAQA,IAAI;MACV,KAAK,UAAU;QAAE,OAAO,IAAI;MAC5B,KAAK,YAAY;QAAE,OAAO,OAAO;MACjC,KAAK,MAAM;QAAE,OAAO,IAAI;MACxB,KAAK,QAAQ;QAAE,OAAO,IAAI;MAC1B;QAAS,OAAO,IAAI;IACtB;EACF,CAAC;EAED,MAAMwF,WAAW,GAAIC,QAAQ,IAAK;IAChC,MAAMC,WAAW,GAAG;MAClB7D,EAAE,EAAE4B,IAAI,CAACM,GAAG,CAAC,CAAC;MACdpF,IAAI,EAAE;QACJkD,EAAE,EAAE,CAAAlD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkD,EAAE,KAAI,OAAO;QACvBqB,SAAS,EAAE,CAAAvE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEuE,SAAS,KAAI,aAAa;QAC3CC,QAAQ,EAAE,CAAAxE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwE,QAAQ,KAAI,EAAE;QAC9BC,MAAM,EAAE,CAAAzE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyE,MAAM,KAAI,oCAAoCzE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEuE,SAAS,IAAIvE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwE,QAAQ;MAC/F,CAAC;MACD,GAAGsC,QAAQ;MACXE,SAAS,EAAE,IAAIlC,IAAI,CAAC,CAAC;MACrBmC,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE;IACZ,CAAC;IAED,MAAMC,iBAAiB,GAAG,CAACJ,WAAW,EAAE,GAAGlG,cAAc,CAAC;IAC1DC,iBAAiB,CAACqG,iBAAiB,CAAC;IACpCvF,YAAY,CAACqD,OAAO,CAAC,gBAAgB,EAAEvD,IAAI,CAACwD,SAAS,CAACiC,iBAAiB,CAAC,CAAC;EAC3E,CAAC;EAED,MAAMC,YAAY,GAAIC,UAAU,IAAK;IACnC,MAAMF,iBAAiB,GAAGtG,cAAc,CAACmD,GAAG,CAAC8C,QAAQ,IAAI;MACvD,IAAIA,QAAQ,CAAC5D,EAAE,KAAKmE,UAAU,EAAE;QAC9B,MAAMC,QAAQ,GAAGR,QAAQ,CAACG,KAAK,CAACP,QAAQ,CAAC1G,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkD,EAAE,CAAC;QAClD,OAAO;UACL,GAAG4D,QAAQ;UACXG,KAAK,EAAEK,QAAQ,GACXR,QAAQ,CAACG,KAAK,CAAC5D,MAAM,CAACH,EAAE,IAAIA,EAAE,MAAKlD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkD,EAAE,EAAC,GAC5C,CAAC,GAAG4D,QAAQ,CAACG,KAAK,EAAEjH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkD,EAAE;QAClC,CAAC;MACH;MACA,OAAO4D,QAAQ;IACjB,CAAC,CAAC;IAEFhG,iBAAiB,CAACqG,iBAAiB,CAAC;IACpCvF,YAAY,CAACqD,OAAO,CAAC,gBAAgB,EAAEvD,IAAI,CAACwD,SAAS,CAACiC,iBAAiB,CAAC,CAAC;EAC3E,CAAC;EAED,MAAMI,UAAU,GAAGA,CAACF,UAAU,EAAEG,OAAO,KAAK;IAC1C,MAAML,iBAAiB,GAAGtG,cAAc,CAACmD,GAAG,CAAC8C,QAAQ,IAAI;MACvD,IAAIA,QAAQ,CAAC5D,EAAE,KAAKmE,UAAU,EAAE;QAC9B,MAAMI,UAAU,GAAG;UACjBvE,EAAE,EAAE4B,IAAI,CAACM,GAAG,CAAC,CAAC;UACdpF,IAAI,EAAE;YACJkD,EAAE,EAAE,CAAAlD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkD,EAAE,KAAI,OAAO;YACvBqB,SAAS,EAAE,CAAAvE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEuE,SAAS,KAAI,aAAa;YAC3CC,QAAQ,EAAE,CAAAxE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwE,QAAQ,KAAI;UAC9B,CAAC;UACDkD,IAAI,EAAEF,OAAO;UACbR,SAAS,EAAE,IAAIlC,IAAI,CAAC;QACtB,CAAC;QAED,OAAO;UACL,GAAGgC,QAAQ;UACXI,QAAQ,EAAE,CAAC,GAAGJ,QAAQ,CAACI,QAAQ,EAAEO,UAAU;QAC7C,CAAC;MACH;MACA,OAAOX,QAAQ;IACjB,CAAC,CAAC;IAEFhG,iBAAiB,CAACqG,iBAAiB,CAAC;IACpCvF,YAAY,CAACqD,OAAO,CAAC,gBAAgB,EAAEvD,IAAI,CAACwD,SAAS,CAACiC,iBAAiB,CAAC,CAAC;EAC3E,CAAC;EAED,oBACExH,OAAA;IAAKgI,SAAS,EAAC,yBAAyB;IAAAC,QAAA,eACtCjI,OAAA;MAAKgI,SAAS,EAAC,6CAA6C;MAAAC,QAAA,gBAE1DjI,OAAA;QAAKgI,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BjI,OAAA;UAAIgI,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAEtD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLrI,OAAA;UAAGgI,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EAAC;QAGvD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGNrI,OAAA;QAAKgI,SAAS,EAAC,gDAAgD;QAAAC,QAAA,gBAC7DjI,OAAA;UACEsI,OAAO,EAAEA,CAAA,KAAM/H,YAAY,CAAC,SAAS,CAAE;UACvCyH,SAAS,EAAE,qEACT1H,SAAS,KAAK,SAAS,GACnB,kCAAkC,GAClC,mCAAmC,EACtC;UAAA2H,QAAA,gBAEHjI,OAAA,CAAC1B,OAAO;YAAC0J,SAAS,EAAC;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,UAC7B,EAAC5E,eAAe,CAACpB,MAAM,EAAC,GAChC;QAAA;UAAA6F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTrI,OAAA;UACEsI,OAAO,EAAEA,CAAA,KAAM/H,YAAY,CAAC,YAAY,CAAE;UAC1CyH,SAAS,EAAE,qEACT1H,SAAS,KAAK,YAAY,GACtB,kCAAkC,GAClC,mCAAmC,EACtC;UAAA2H,QAAA,gBAEHjI,OAAA,CAACrB,QAAQ;YAACqJ,SAAS,EAAC;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,cAC7B,EAACvE,gBAAgB,CAACzB,MAAM,EAAC,GAClC;QAAA;UAAA6F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTrI,OAAA;UACEsI,OAAO,EAAEA,CAAA,KAAM/H,YAAY,CAAC,cAAc,CAAE;UAC5CyH,SAAS,EAAE,qEACT1H,SAAS,KAAK,cAAc,GACxB,kCAAkC,GAClC,mCAAmC,EACtC;UAAA2H,QAAA,gBAEHjI,OAAA,CAACzB,YAAY;YAACyJ,SAAS,EAAC;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAE1C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAGL/H,SAAS,KAAK,SAAS,iBACtBN,OAAA;QAAKgI,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBAEpDjI,OAAA;UAAKgI,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5BjI,OAAA;YAAKgI,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDjI,OAAA;cAAKgI,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrDjI,OAAA;gBAAIgI,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,GAAC,YACxC,EAACxE,eAAe,CAACpB,MAAM,EAAC,GACpC;cAAA;gBAAA6F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLrI,OAAA;gBAAKgI,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACvBjI,OAAA,CAAChB,QAAQ;kBAACgJ,SAAS,EAAC;gBAA6C;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACpErI,OAAA;kBACE0B,IAAI,EAAC,MAAM;kBACX6G,WAAW,EAAC,sBAAsB;kBAClCC,KAAK,EAAExH,UAAW;kBAClByH,QAAQ,EAAGC,CAAC,IAAKzH,aAAa,CAACyH,CAAC,CAAC/G,MAAM,CAAC6G,KAAK,CAAE;kBAC/CR,SAAS,EAAC;gBAAuG;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENrI,OAAA;cAAKgI,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvBtB,eAAe,CAACtC,GAAG,CAACuC,UAAU,iBAC7B5G,OAAA;gBAAyBgI,SAAS,EAAC,0FAA0F;gBAAAC,QAAA,gBAC3HjI,OAAA;kBAAKgI,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1CjI,OAAA;oBACE2I,GAAG,EAAE/B,UAAU,CAACC,KAAK,CAAC/B,MAAO;oBAC7B8D,GAAG,EAAE,GAAGhC,UAAU,CAACC,KAAK,CAACjC,SAAS,IAAIgC,UAAU,CAACC,KAAK,CAAChC,QAAQ,EAAG;oBAClEmD,SAAS,EAAC;kBAAwB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC,CAAC,eACFrI,OAAA;oBAAAiI,QAAA,gBACEjI,OAAA;sBAAIgI,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,GACtCrB,UAAU,CAACC,KAAK,CAACjC,SAAS,EAAC,GAAC,EAACgC,UAAU,CAACC,KAAK,CAAChC,QAAQ;oBAAA;sBAAAqD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrD,CAAC,eACLrI,OAAA;sBAAKgI,SAAS,EAAC,mDAAmD;sBAAAC,QAAA,gBAChEjI,OAAA;wBAAMgI,SAAS,EAAC,mBAAmB;wBAAAC,QAAA,gBACjCjI,OAAA,CAACnB,QAAQ;0BAACmJ,SAAS,EAAC;wBAAc;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,EACpCzB,UAAU,CAACC,KAAK,CAACgC,IAAI;sBAAA;wBAAAX,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClB,CAAC,eACPrI,OAAA;wBAAMgI,SAAS,EAAC,mBAAmB;wBAAAC,QAAA,gBACjCjI,OAAA,CAACpB,OAAO;0BAACoJ,SAAS,EAAC;wBAAc;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,EACnCzB,UAAU,CAACC,KAAK,CAACiC,QAAQ,GAAG,UAAU,GAAG,YAAY;sBAAA;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClD,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACNrI,OAAA;sBAAKgI,SAAS,EAAC,4BAA4B;sBAAAC,QAAA,GACxCrB,UAAU,CAACmC,aAAa,EAAC,yBAAkB,EAACnC,UAAU,CAACoC,gBAAgB,EAAC,4BAC3E;oBAAA;sBAAAd,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNrI,OAAA;kBAAKgI,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1CjI,OAAA;oBAAQgI,SAAS,EAAC,+CAA+C;oBAAAC,QAAA,eAC/DjI,OAAA,CAACtB,eAAe;sBAACsJ,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjC,CAAC,eACTrI,OAAA;oBAAKgI,SAAS,EAAC,YAAY;oBAAAC,QAAA,gBACzBjI,OAAA;sBAAKgI,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,GAC/CrB,UAAU,CAACC,KAAK,CAACoC,KAAK,CAACC,aAAa,EAAC,KACxC;oBAAA;sBAAAhB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNrI,OAAA;sBAAKgI,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,GACnCrB,UAAU,CAACC,KAAK,CAACoC,KAAK,CAACE,eAAe,EAAC,eAC1C;oBAAA;sBAAAjB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA,GAtCEzB,UAAU,CAACrD,EAAE;gBAAA2E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAuClB,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNrI,OAAA;UAAKgI,SAAS,EAAC,WAAW;UAAAC,QAAA,GAEvBpE,eAAe,CAACxB,MAAM,GAAG,CAAC,iBACzBrC,OAAA;YAAKgI,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDjI,OAAA;cAAIgI,SAAS,EAAC,0CAA0C;cAAAC,QAAA,GAAC,wBACpC,EAACpE,eAAe,CAACxB,MAAM,EAAC,GAC7C;YAAA;cAAA6F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLrI,OAAA;cAAKgI,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvBpE,eAAe,CAACQ,GAAG,CAAC+E,OAAO,iBAC1BpJ,OAAA;gBAAsBgI,SAAS,EAAC,yEAAyE;gBAAAC,QAAA,gBACvGjI,OAAA;kBAAKgI,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1CjI,OAAA;oBACE2I,GAAG,EAAES,OAAO,CAACC,KAAK,CAACvE,MAAO;oBAC1B8D,GAAG,EAAE,GAAGQ,OAAO,CAACC,KAAK,CAACzE,SAAS,IAAIwE,OAAO,CAACC,KAAK,CAACxE,QAAQ,EAAG;oBAC5DmD,SAAS,EAAC;kBAAwB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC,CAAC,eACFrI,OAAA;oBAAAiI,QAAA,gBACEjI,OAAA;sBAAIgI,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,GAC9CmB,OAAO,CAACC,KAAK,CAACzE,SAAS,EAAC,GAAC,EAACwE,OAAO,CAACC,KAAK,CAACxE,QAAQ;oBAAA;sBAAAqD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/C,CAAC,eACLrI,OAAA;sBAAGgI,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAEmB,OAAO,CAACC,KAAK,CAACR;oBAAI;sBAAAX,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1D,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNrI,OAAA;kBAAKgI,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC7BjI,OAAA;oBACEsI,OAAO,EAAEA,CAAA,KAAMpE,kBAAkB,CAACkF,OAAO,CAAC7F,EAAE,CAAE;oBAC9CyE,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,eAExDjI,OAAA,CAAClB,OAAO;sBAACkJ,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzB,CAAC,eACTrI,OAAA;oBACEsI,OAAO,EAAEA,CAAA,KAAMhE,kBAAkB,CAAC8E,OAAO,CAAC7F,EAAE,CAAE;oBAC9CyE,SAAS,EAAC,0CAA0C;oBAAAC,QAAA,eAEpDjI,OAAA,CAACjB,GAAG;sBAACiJ,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA,GA3BEe,OAAO,CAAC7F,EAAE;gBAAA2E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA4Bf,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eAGDrI,OAAA;YAAKgI,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDjI,OAAA;cAAIgI,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAEzD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLrI,OAAA;cAAKgI,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvBnH,iBAAiB,CAACwI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACjF,GAAG,CAACkF,UAAU,iBAC3CvJ,OAAA;gBAA8BgI,SAAS,EAAC,yEAAyE;gBAAAC,QAAA,gBAC/GjI,OAAA;kBAAKgI,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1CjI,OAAA;oBACE2I,GAAG,EAAEY,UAAU,CAAClJ,IAAI,CAACyE,MAAO;oBAC5B8D,GAAG,EAAE,GAAGW,UAAU,CAAClJ,IAAI,CAACuE,SAAS,IAAI2E,UAAU,CAAClJ,IAAI,CAACwE,QAAQ,EAAG;oBAChEmD,SAAS,EAAC;kBAAwB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC,CAAC,eACFrI,OAAA;oBAAAiI,QAAA,gBACEjI,OAAA;sBAAIgI,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,GAC9CsB,UAAU,CAAClJ,IAAI,CAACuE,SAAS,EAAC,GAAC,EAAC2E,UAAU,CAAClJ,IAAI,CAACwE,QAAQ;oBAAA;sBAAAqD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnD,CAAC,eACLrI,OAAA;sBAAGgI,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAEsB,UAAU,CAACC;oBAAM;sBAAAtB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNrI,OAAA;kBAAQgI,SAAS,EAAC,4CAA4C;kBAAAC,QAAA,eAC5DjI,OAAA,CAACvB,UAAU;oBAACuJ,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B,CAAC;cAAA,GAhBDkB,UAAU,CAAClJ,IAAI,CAACkD,EAAE;gBAAA2E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAiBvB,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA/H,SAAS,KAAK,YAAY,iBACzBN,OAAA;QAAKgI,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBAEpDjI,OAAA;UAAKgI,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5BjI,OAAA;YAAKgI,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDjI,OAAA;cAAKgI,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrDjI,OAAA;gBAAIgI,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,GAAC,mBACpC,EAACnE,gBAAgB,CAACzB,MAAM,EAAC,GACzC;cAAA;gBAAA6F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLrI,OAAA;gBACEsI,OAAO,EAAEA,CAAA,KAAMjH,sBAAsB,CAAC,IAAI,CAAE;gBAC5C2G,SAAS,EAAC,mGAAmG;gBAAAC,QAAA,gBAE7GjI,OAAA,CAACf,MAAM;kBAAC+I,SAAS,EAAC;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,uBAE7B;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENrI,OAAA;cAAKgI,SAAS,EAAC,YAAY;cAAAC,QAAA,EACxBnE,gBAAgB,CAACO,GAAG,CAACK,SAAS;gBAAA,IAAA+E,qBAAA,EAAAC,sBAAA;gBAAA,oBAC7B1J,OAAA;kBAAwBgI,SAAS,EAAC,yEAAyE;kBAAAC,QAAA,gBACzGjI,OAAA;oBAAKgI,SAAS,EAAC,uCAAuC;oBAAAC,QAAA,gBACpDjI,OAAA;sBAAKgI,SAAS,EAAC,6BAA6B;sBAAAC,QAAA,gBAC1CjI,OAAA;wBAAMgI,SAAS,EAAC,UAAU;wBAAAC,QAAA,EAAElC,gBAAgB,CAACrB,SAAS,CAAChD,IAAI;sBAAC;wBAAAwG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eACpErI,OAAA;wBAAAiI,QAAA,gBACEjI,OAAA;0BAAIgI,SAAS,EAAC,6BAA6B;0BAAAC,QAAA,EAAEvD,SAAS,CAAClD;wBAAI;0BAAA0G,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACjErI,OAAA;0BAAGgI,SAAS,EAAC,uBAAuB;0BAAAC,QAAA,EAAEvD,SAAS,CAACjD;wBAAW;0BAAAyG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC7D,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNrI,OAAA;sBAAMgI,SAAS,EAAE,8CACftD,SAAS,CAACiF,QAAQ,KAAK,MAAM,GAAG,+BAA+B,GAAG,2BAA2B,EAC5F;sBAAA1B,QAAA,EACAvD,SAAS,CAACiF,QAAQ,KAAK,MAAM,GAAG,QAAQ,GAAG;oBAAY;sBAAAzB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eAENrI,OAAA;oBAAKgI,SAAS,EAAC,4CAA4C;oBAAAC,QAAA,gBACzDjI,OAAA;sBAAKgI,SAAS,EAAC,aAAa;sBAAAC,QAAA,gBAC1BjI,OAAA;wBAAKgI,SAAS,EAAC,iCAAiC;wBAAAC,QAAA,EAAEvD,SAAS,CAACO,YAAY,CAAC5C;sBAAM;wBAAA6F,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACtFrI,OAAA;wBAAKgI,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EAAC;sBAAY;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtD,CAAC,eACNrI,OAAA;sBAAKgI,SAAS,EAAC,aAAa;sBAAAC,QAAA,gBAC1BjI,OAAA;wBAAKgI,SAAS,EAAC,iCAAiC;wBAAAC,QAAA,EAAEvD,SAAS,CAAC/C;sBAAM;wBAAAuG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACzErI,OAAA;wBAAKgI,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EAAEvD,SAAS,CAACkF;sBAAI;wBAAA1B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1D,CAAC,eACNrI,OAAA;sBAAKgI,SAAS,EAAC,aAAa;sBAAAC,QAAA,gBAC1BjI,OAAA;wBAAKgI,SAAS,EAAC,iCAAiC;wBAAAC,QAAA,EAAEvD,SAAS,CAAC9C;sBAAQ;wBAAAsG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAC3ErI,OAAA;wBAAKgI,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EAAC;sBAAK;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/C,CAAC,eACNrI,OAAA;sBAAKgI,SAAS,EAAC,aAAa;sBAAAC,QAAA,gBAC1BjI,OAAA;wBAAKgI,SAAS,EAAC,iCAAiC;wBAAAC,QAAA,EAC7C9E,IAAI,CAAC0G,IAAI,CAAC,CAACnF,SAAS,CAACkB,OAAO,GAAG,IAAIT,IAAI,CAAC,CAAC,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;sBAAC;wBAAA+C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjE,CAAC,eACNrI,OAAA;wBAAKgI,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EAAC;sBAAc;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,EAGL3D,SAAS,CAACO,YAAY,CAAC6E,IAAI,CAAC3D,CAAC,IAAIA,CAAC,CAAC9F,IAAI,CAACkD,EAAE,MAAKlD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkD,EAAE,EAAC,iBACvDvD,OAAA;oBAAKgI,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnBjI,OAAA;sBAAKgI,SAAS,EAAC,gDAAgD;sBAAAC,QAAA,gBAC7DjI,OAAA;wBAAAiI,QAAA,EAAM;sBAAiB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAC9BrI,OAAA;wBAAAiI,QAAA,GAAO,EAAAwB,qBAAA,GAAA/E,SAAS,CAACO,YAAY,CAAC6E,IAAI,CAAC3D,CAAC,IAAIA,CAAC,CAAC9F,IAAI,CAACkD,EAAE,MAAKlD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkD,EAAE,EAAC,cAAAkG,qBAAA,uBAAxDA,qBAAA,CAA0D1E,QAAQ,KAAI,CAAC,EAAC,KAAG,EAACL,SAAS,CAAC/C,MAAM;sBAAA;wBAAAuG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxG,CAAC,eACNrI,OAAA;sBAAKgI,SAAS,EAAC,qCAAqC;sBAAAC,QAAA,eAClDjI,OAAA;wBACEgI,SAAS,EAAC,0DAA0D;wBACpE+B,KAAK,EAAE;0BACLC,KAAK,EAAE,GAAG7G,IAAI,CAACiD,GAAG,CAAC,GAAG,EAAG,CAAC,EAAAsD,sBAAA,GAAAhF,SAAS,CAACO,YAAY,CAAC6E,IAAI,CAAC3D,CAAC,IAAIA,CAAC,CAAC9F,IAAI,CAACkD,EAAE,MAAKlD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkD,EAAE,EAAC,cAAAmG,sBAAA,uBAAxDA,sBAAA,CAA0D3E,QAAQ,KAAI,CAAC,IAAIL,SAAS,CAAC/C,MAAM,GAAI,GAAG,CAAC;wBAC/H;sBAAE;wBAAAuG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACN,eAEDrI,OAAA;oBAAKgI,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,gBAChDjI,OAAA;sBAAKgI,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,gBACpCjI,OAAA;wBAAMgI,SAAS,EAAC,aAAa;wBAAAC,QAAA,EAAC;sBAAM;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,KAAC,EAAC3D,SAAS,CAACuF,KAAK,IAAI,mBAAmB;oBAAA;sBAAA/B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChF,CAAC,eACNrI,OAAA;sBAAKgI,SAAS,EAAC,6BAA6B;sBAAAC,QAAA,EACzCvD,SAAS,CAACO,YAAY,CAAC6E,IAAI,CAAC3D,CAAC,IAAIA,CAAC,CAAC9F,IAAI,CAACkD,EAAE,MAAKlD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkD,EAAE,EAAC,gBACvDvD,OAAA,CAAAE,SAAA;wBAAA+H,QAAA,gBACEjI,OAAA;0BACE0B,IAAI,EAAC,QAAQ;0BACb6G,WAAW,EAAC,aAAa;0BACzBP,SAAS,EAAC,uDAAuD;0BACjEkC,UAAU,EAAGxB,CAAC,IAAK;4BACjB,IAAIA,CAAC,CAACyB,GAAG,KAAK,OAAO,EAAE;8BACrBlE,uBAAuB,CAACvB,SAAS,CAACnB,EAAE,EAAEmC,UAAU,CAACgD,CAAC,CAAC/G,MAAM,CAAC6G,KAAK,CAAC,CAAC;8BACjEE,CAAC,CAAC/G,MAAM,CAAC6G,KAAK,GAAG,EAAE;4BACrB;0BACF;wBAAE;0BAAAN,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eACFrI,OAAA;0BACEsI,OAAO,EAAEA,CAAA,KAAMtC,eAAe,CAACtB,SAAS,CAACnB,EAAE,CAAE;0BAC7CyE,SAAS,EAAC,mDAAmD;0BAC7DoC,KAAK,EAAC,sBAAmB;0BAAAnC,QAAA,eAEzBjI,OAAA,CAACb,QAAQ;4BAAC6I,SAAS,EAAC;0BAAS;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC1B,CAAC;sBAAA,eACT,CAAC,gBAEHrI,OAAA;wBACEsI,OAAO,EAAEA,CAAA,KAAM/D,mBAAmB,CAACG,SAAS,CAACnB,EAAE,CAAE;wBACjDyE,SAAS,EAAC,yFAAyF;wBAAAC,QAAA,EACpG;sBAED;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ;oBACT;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA,GA1FE3D,SAAS,CAACnB,EAAE;kBAAA2E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA2FjB,CAAC;cAAA,CACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNrI,OAAA;UAAKgI,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBjI,OAAA;YAAKgI,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDjI,OAAA;cAAIgI,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAEzD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLrI,OAAA;cAAKgI,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvBnE,gBAAgB,CAACwF,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACjF,GAAG,CAACK,SAAS,IAAI;gBAC7C,MAAM2F,iBAAiB,GAAG3F,SAAS,CAACO,YAAY,CAAC6E,IAAI,CAAC3D,CAAC,IAAIA,CAAC,CAAC9F,IAAI,CAACkD,EAAE,MAAKlD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkD,EAAE,EAAC;gBAClF,IAAI,CAAC8G,iBAAiB,EAAE,OAAO,IAAI;gBAEnC,MAAMC,kBAAkB,GAAID,iBAAiB,CAACtF,QAAQ,GAAGL,SAAS,CAAC/C,MAAM,GAAI,GAAG;gBAEhF,oBACE3B,OAAA;kBAAwBgI,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,gBACvEjI,OAAA;oBAAKgI,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,gBACrDjI,OAAA;sBAAIgI,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAEvD,SAAS,CAAClD;oBAAI;sBAAA0G,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACvErI,OAAA;sBAAMgI,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,GAAC,GAAC,EAACoC,iBAAiB,CAACrF,IAAI;oBAAA;sBAAAkD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrE,CAAC,eACNrI,OAAA;oBAAKgI,SAAS,EAAC,0CAA0C;oBAAAC,QAAA,eACvDjI,OAAA;sBACEgI,SAAS,EAAC,8BAA8B;sBACxC+B,KAAK,EAAE;wBAAEC,KAAK,EAAE,GAAG7G,IAAI,CAACiD,GAAG,CAACkE,kBAAkB,EAAE,GAAG,CAAC;sBAAI;oBAAE;sBAAApC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACNrI,OAAA;oBAAKgI,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,GACnCoC,iBAAiB,CAACtF,QAAQ,EAAC,KAAG,EAACL,SAAS,CAAC/C,MAAM,EAAC,GAAC,EAAC+C,SAAS,CAACkF,IAAI;kBAAA;oBAAA1B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9D,CAAC;gBAAA,GAbE3D,SAAS,CAACnB,EAAE;kBAAA2E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAcjB,CAAC;cAEV,CAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENrI,OAAA;YAAKgI,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDjI,OAAA;cAAIgI,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAEzD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLrI,OAAA;cAAKgI,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvBhE,mBAAmB,CAACqF,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACjF,GAAG,CAACK,SAAS,iBAC5C1E,OAAA;gBAAwBgI,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,gBACvEjI,OAAA;kBAAIgI,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAEvD,SAAS,CAAClD;gBAAI;kBAAA0G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACvErI,OAAA;kBAAKgI,SAAS,EAAC,wCAAwC;kBAAAC,QAAA,gBACrDjI,OAAA;oBAAMgI,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,GACpCvD,SAAS,CAACO,YAAY,CAAC5C,MAAM,EAAC,eACjC;kBAAA;oBAAA6F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACPrI,OAAA;oBAAMgI,SAAS,EAAC,oCAAoC;oBAAAC,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChE,CAAC;cAAA,GAPE3D,SAAS,CAACnB,EAAE;gBAAA2E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAQjB,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA/H,SAAS,KAAK,cAAc,iBAC3BN,OAAA;QAAKgI,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBAEpDjI,OAAA;UAAKgI,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5BjI,OAAA;YAAKgI,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDjI,OAAA;cAAIgI,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAEzD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAELrI,OAAA;cAAKgI,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvBvH,QAAQ,CAAC4I,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACjF,GAAG,CAACkG,OAAO,iBAC/BvK,OAAA;gBAAsBgI,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,gBACrEjI,OAAA;kBAAKgI,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,eACpDjI,OAAA;oBAAKgI,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,gBAC1CjI,OAAA;sBAAMgI,SAAS,EAAC,SAAS;sBAAAC,QAAA,EAAEhB,WAAW,CAACsD,OAAO,CAAC7I,IAAI;oBAAC;sBAAAwG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAC5DrI,OAAA;sBAAAiI,QAAA,gBACEjI,OAAA;wBAAIgI,SAAS,EAAC,6BAA6B;wBAAAC,QAAA,EAAEsC,OAAO,CAAC/I;sBAAI;wBAAA0G,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAC/DrI,OAAA;wBAAGgI,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EAAEsC,OAAO,CAAC9I;sBAAW;wBAAAyG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAC9DrI,OAAA;wBAAKgI,SAAS,EAAC,kCAAkC;wBAAAC,QAAA,gBAC/CjI,OAAA;0BAAMgI,SAAS,EAAC,uBAAuB;0BAAAC,QAAA,GAAEsC,OAAO,CAACC,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC,EAAC,KAAG;wBAAA;0BAAAvC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eAC/ErI,OAAA;0BAAMgI,SAAS,EAAE,8CAA8ChB,kBAAkB,CAACuD,OAAO,CAAC1I,UAAU,CAAC,EAAG;0BAAAoG,QAAA,EACrGsC,OAAO,CAAC1I;wBAAU;0BAAAqG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACf,CAAC,eACPrI,OAAA;0BAAMgI,SAAS,EAAC,uBAAuB;0BAAAC,QAAA,GAAEsC,OAAO,CAACG,aAAa,EAAC,aAAW;wBAAA;0BAAAxC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9E,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENrI,OAAA;kBAAKgI,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBACxBjI,OAAA;oBAAIgI,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EAChEkC,OAAO,CAACI,WAAW,CAACrB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACjF,GAAG,CAAC,CAACuG,KAAK,EAAElE,KAAK,kBAChD1G,OAAA;oBAAiBgI,SAAS,EAAC,mEAAmE;oBAAAC,QAAA,gBAC5FjI,OAAA;sBAAKgI,SAAS,EAAC,6BAA6B;sBAAAC,QAAA,gBAC1CjI,OAAA;wBAAMgI,SAAS,EAAE,2EACftB,KAAK,KAAK,CAAC,GAAG,+BAA+B,GAC7CA,KAAK,KAAK,CAAC,GAAG,2BAA2B,GACzCA,KAAK,KAAK,CAAC,GAAG,+BAA+B,GAC7C,2BAA2B,EAC1B;wBAAAuB,QAAA,EACA2C,KAAK,CAAC5F;sBAAI;wBAAAkD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACP,CAAC,eACPrI,OAAA;wBAAAiI,QAAA,gBACEjI,OAAA;0BAAMgI,SAAS,EAAC,mCAAmC;0BAAAC,QAAA,EAAE2C,KAAK,CAACC;wBAAO;0BAAA3C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC,EACzEuC,KAAK,CAACE,aAAa,iBAClB9K,OAAA,CAACxB,OAAO;0BAACwJ,SAAS,EAAC;wBAAqC;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAC3D;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNrI,OAAA;sBAAKgI,SAAS,EAAC,YAAY;sBAAAC,QAAA,gBACzBjI,OAAA;wBAAKgI,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,EAC/CrI,UAAU,CAACgL,KAAK,CAACG,IAAI;sBAAC;wBAAA7C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpB,CAAC,eACNrI,OAAA;wBAAKgI,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EACnCpI,UAAU,CAAC+K,KAAK,CAACI,IAAI;sBAAC;wBAAA9C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA,GAxBE3B,KAAK;oBAAAwB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAyBV,CACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA,GAhDEkC,OAAO,CAAChH,EAAE;gBAAA2E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAiDf,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNrI,OAAA;UAAKgI,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBjI,OAAA;YAAKgI,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDjI,OAAA;cAAIgI,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAEzD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLrI,OAAA;cAAKgI,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBjI,OAAA;gBAAKgI,SAAS,EAAC,+DAA+D;gBAAAC,QAAA,gBAC5EjI,OAAA;kBAAKgI,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1CjI,OAAA,CAACxB,OAAO;oBAACwJ,SAAS,EAAC;kBAAyB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC/CrI,OAAA;oBAAMgI,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,EAAC;kBAAgB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChE,CAAC,eACNrI,OAAA;kBAAMgI,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC,eAENrI,OAAA;gBAAKgI,SAAS,EAAC,6DAA6D;gBAAAC,QAAA,gBAC1EjI,OAAA;kBAAKgI,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1CjI,OAAA,CAACzB,YAAY;oBAACyJ,SAAS,EAAC;kBAAuB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAClDrI,OAAA;oBAAMgI,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD,CAAC,eACNrI,OAAA;kBAAMgI,SAAS,EAAC,iCAAiC;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD,CAAC,eAENrI,OAAA;gBAAKgI,SAAS,EAAC,8DAA8D;gBAAAC,QAAA,gBAC3EjI,OAAA;kBAAKgI,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1CjI,OAAA,CAACxB,OAAO;oBAACwJ,SAAS,EAAC;kBAAwB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC9CrI,OAAA;oBAAMgI,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,EAAC;kBAAkB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClE,CAAC,eACNrI,OAAA;kBAAMgI,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENrI,OAAA;YAAKgI,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDjI,OAAA;cAAIgI,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAEzD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLrI,OAAA;cAAKgI,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvBvH,QAAQ,CAAC4I,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACjF,GAAG,CAACkG,OAAO;gBAAA,IAAAU,qBAAA;gBAAA,oBAC/BjL,OAAA;kBAAsBgI,SAAS,EAAC,yGAAyG;kBAAAC,QAAA,gBACvIjI,OAAA;oBAAAiI,QAAA,gBACEjI,OAAA;sBAAIgI,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAEsC,OAAO,CAAC/I;oBAAI;sBAAA0G,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACrErI,OAAA;sBAAKgI,SAAS,EAAC,mDAAmD;sBAAAC,QAAA,gBAChEjI,OAAA;wBAAAiI,QAAA,GAAOsC,OAAO,CAACC,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC,EAAC,KAAG;sBAAA;wBAAAvC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAC7CrI,OAAA;wBAAAiI,QAAA,EAAM;sBAAC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACdrI,OAAA;wBAAAiI,QAAA,GAAOsC,OAAO,CAACG,aAAa,EAAC,aAAW;sBAAA;wBAAAxC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5C,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNrI,OAAA;oBAAKgI,SAAS,EAAC,YAAY;oBAAAC,QAAA,gBACzBjI,OAAA;sBAAKgI,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAC/CrI,UAAU,CAAC,EAAAqL,qBAAA,GAAAV,OAAO,CAACI,WAAW,CAAC,CAAC,CAAC,cAAAM,qBAAA,uBAAtBA,qBAAA,CAAwBF,IAAI,KAAI,CAAC;oBAAC;sBAAA7C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3C,CAAC,eACNrI,OAAA;sBAAKgI,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAAM;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChD,CAAC;gBAAA,GAdEkC,OAAO,CAAChH,EAAE;kBAAA2E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAef,CAAC;cAAA,CACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGAjH,mBAAmB,iBAClBpB,OAAA;QAAKgI,SAAS,EAAC,4EAA4E;QAAAC,QAAA,eACzFjI,OAAA;UAAKgI,SAAS,EAAC,8CAA8C;UAAAC,QAAA,gBAC3DjI,OAAA;YAAIgI,SAAS,EAAC,0CAA0C;YAAAC,QAAA,EAAC;UAEzD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAELrI,OAAA;YAAKgI,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBjI,OAAA;cAAAiI,QAAA,gBACEjI,OAAA;gBAAOgI,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRrI,OAAA;gBACE0B,IAAI,EAAC,MAAM;gBACX8G,KAAK,EAAElH,YAAY,CAACE,IAAK;gBACzBiH,QAAQ,EAAGC,CAAC,IAAKnH,eAAe,CAAC;kBAAC,GAAGD,YAAY;kBAAEE,IAAI,EAAEkH,CAAC,CAAC/G,MAAM,CAAC6G;gBAAK,CAAC,CAAE;gBAC1ER,SAAS,EAAC,wGAAwG;gBAClHO,WAAW,EAAC;cAA2B;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENrI,OAAA;cAAAiI,QAAA,gBACEjI,OAAA;gBAAOgI,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRrI,OAAA;gBACEwI,KAAK,EAAElH,YAAY,CAACG,WAAY;gBAChCgH,QAAQ,EAAGC,CAAC,IAAKnH,eAAe,CAAC;kBAAC,GAAGD,YAAY;kBAAEG,WAAW,EAAEiH,CAAC,CAAC/G,MAAM,CAAC6G;gBAAK,CAAC,CAAE;gBACjFR,SAAS,EAAC,wGAAwG;gBAClHkD,IAAI,EAAC,GAAG;gBACR3C,WAAW,EAAC;cAAwB;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENrI,OAAA;cAAKgI,SAAS,EAAC,wBAAwB;cAAAC,QAAA,gBACrCjI,OAAA;gBAAAiI,QAAA,gBACEjI,OAAA;kBAAOgI,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRrI,OAAA;kBACEwI,KAAK,EAAElH,YAAY,CAACI,IAAK;kBACzB+G,QAAQ,EAAGC,CAAC,IAAKnH,eAAe,CAAC;oBAAC,GAAGD,YAAY;oBAAEI,IAAI,EAAEgH,CAAC,CAAC/G,MAAM,CAAC6G;kBAAK,CAAC,CAAE;kBAC1ER,SAAS,EAAC,wGAAwG;kBAAAC,QAAA,gBAElHjI,OAAA;oBAAQwI,KAAK,EAAC,UAAU;oBAAAP,QAAA,EAAC;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC/CrI,OAAA;oBAAQwI,KAAK,EAAC,YAAY;oBAAAP,QAAA,EAAC;kBAAkB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACtDrI,OAAA;oBAAQwI,KAAK,EAAC,MAAM;oBAAAP,QAAA,EAAC;kBAAe;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC7CrI,OAAA;oBAAQwI,KAAK,EAAC,QAAQ;oBAAAP,QAAA,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eAENrI,OAAA;gBAAAiI,QAAA,gBACEjI,OAAA;kBAAOgI,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRrI,OAAA;kBACE0B,IAAI,EAAC,QAAQ;kBACb8G,KAAK,EAAElH,YAAY,CAACK,MAAO;kBAC3B8G,QAAQ,EAAGC,CAAC,IAAKnH,eAAe,CAAC;oBAAC,GAAGD,YAAY;oBAAEK,MAAM,EAAE+G,CAAC,CAAC/G,MAAM,CAAC6G;kBAAK,CAAC,CAAE;kBAC5ER,SAAS,EAAC,wGAAwG;kBAClHO,WAAW,EAAC;gBAAK;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENrI,OAAA;cAAKgI,SAAS,EAAC,wBAAwB;cAAAC,QAAA,gBACrCjI,OAAA;gBAAAiI,QAAA,gBACEjI,OAAA;kBAAOgI,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRrI,OAAA;kBACE0B,IAAI,EAAC,QAAQ;kBACb8G,KAAK,EAAElH,YAAY,CAACM,QAAS;kBAC7B6G,QAAQ,EAAGC,CAAC,IAAKnH,eAAe,CAAC;oBAAC,GAAGD,YAAY;oBAAEM,QAAQ,EAAEuJ,QAAQ,CAACzC,CAAC,CAAC/G,MAAM,CAAC6G,KAAK;kBAAC,CAAC,CAAE;kBACxFR,SAAS,EAAC,wGAAwG;kBAClH5B,GAAG,EAAC,GAAG;kBACPgF,GAAG,EAAC;gBAAK;kBAAAlD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENrI,OAAA;gBAAAiI,QAAA,gBACEjI,OAAA;kBAAOgI,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRrI,OAAA;kBACEwI,KAAK,EAAElH,YAAY,CAACO,UAAW;kBAC/B4G,QAAQ,EAAGC,CAAC,IAAKnH,eAAe,CAAC;oBAAC,GAAGD,YAAY;oBAAEO,UAAU,EAAE6G,CAAC,CAAC/G,MAAM,CAAC6G;kBAAK,CAAC,CAAE;kBAChFR,SAAS,EAAC,wGAAwG;kBAAAC,QAAA,gBAElHjI,OAAA;oBAAQwI,KAAK,EAAC,QAAQ;oBAAAP,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACtCrI,OAAA;oBAAQwI,KAAK,EAAC,cAAQ;oBAAAP,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACtCrI,OAAA;oBAAQwI,KAAK,EAAC,WAAW;oBAAAP,QAAA,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENrI,OAAA;YAAKgI,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAClCjI,OAAA;cACEsI,OAAO,EAAE9C,eAAgB;cACzBwC,SAAS,EAAC,wFAAwF;cAAAC,QAAA,EACnG;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTrI,OAAA;cACEsI,OAAO,EAAEA,CAAA,KAAMjH,sBAAsB,CAAC,KAAK,CAAE;cAC7C2G,SAAS,EAAC,2FAA2F;cAAAC,QAAA,EACtG;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjI,EAAA,CA15BID,MAAM;EAAA,QACOZ,OAAO;AAAA;AAAA8L,EAAA,GADpBlL,MAAM;AA45BZ,eAAeA,MAAM;AAAC,IAAAkL,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}