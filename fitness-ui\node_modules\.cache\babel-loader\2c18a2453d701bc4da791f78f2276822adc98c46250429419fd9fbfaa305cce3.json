{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projet fitness\\\\DATA\\\\fitness-ui\\\\src\\\\pages\\\\Exercises.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { FiPlay, FiClock, FiZap, FiFilter, FiSearch, FiTarget, FiHeart, FiStar, FiInfo, FiPlus, FiTrendingUp, FiBookmark, FiVideo, FiBarChart3, FiCalendar, FiX, FiEdit3, FiSave } from 'react-icons/fi';\nimport { useAuth } from '../contexts/AuthContext';\nimport { exercises, exerciseCategories, muscleGroups, equipmentTypes, getDifficultyColor, getRecommendedExercises, searchExercises, createWorkoutPlan, toggleFavorite, loadFavorites, addToHistory, getExerciseHistory, calculateProgressStats, createCustomProgram, getExerciseVideo, getExerciseAnimation } from '../utils/exerciseUtils';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Exercises = () => {\n  _s();\n  var _exerciseCategories$s, _exerciseCategories$s2, _equipmentTypes$selec;\n  const {\n    user\n  } = useAuth();\n  const [selectedExercise, setSelectedExercise] = useState(null);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [showFilters, setShowFilters] = useState(false);\n  const [filters, setFilters] = useState({\n    category: 'all',\n    muscleGroup: 'all',\n    equipment: 'all',\n    difficulty: 'all',\n    maxDuration: null\n  });\n  const [activeTab, setActiveTab] = useState('all');\n  const [recommendedExercises, setRecommendedExercises] = useState([]);\n  const [favoriteExercises, setFavoriteExercises] = useState([]);\n  const [filteredExercises, setFilteredExercises] = useState(exercises);\n\n  // Nouveaux états pour les fonctionnalités avancées\n  const [exerciseHistory, setExerciseHistory] = useState([]);\n  const [showCreateProgram, setShowCreateProgram] = useState(false);\n  const [selectedExercisesForProgram, setSelectedExercisesForProgram] = useState([]);\n  const [showProgressModal, setShowProgressModal] = useState(false);\n  const [progressData, setProgressData] = useState(null);\n  const [showPerformanceModal, setShowPerformanceModal] = useState(false);\n  const [performanceData, setPerformanceData] = useState({\n    sets: 3,\n    reps: 12,\n    weight: '',\n    duration: '',\n    notes: ''\n  });\n  useEffect(() => {\n    // Générer des recommandations basées sur le profil utilisateur\n    if (user) {\n      const recommendations = getRecommendedExercises(user, user.primaryGoal || 'general_fitness');\n      setRecommendedExercises(recommendations);\n    }\n\n    // Charger les favoris depuis localStorage\n    const savedFavorites = loadFavorites();\n    const favoriteExerciseObjects = exercises.filter(ex => savedFavorites.includes(ex.id));\n    setFavoriteExercises(favoriteExerciseObjects);\n\n    // Charger l'historique des exercices\n    const history = getExerciseHistory();\n    setExerciseHistory(history);\n  }, [user]);\n  useEffect(() => {\n    // Appliquer les filtres et la recherche\n    let results = exercises;\n    if (activeTab === 'recommended') {\n      results = recommendedExercises;\n    } else if (activeTab === 'favorites') {\n      results = favoriteExercises;\n    }\n    if (searchQuery || Object.values(filters).some(f => f !== 'all' && f !== null)) {\n      results = searchExercises(searchQuery, filters);\n    }\n    setFilteredExercises(results);\n  }, [searchQuery, filters, activeTab, recommendedExercises, favoriteExercises]);\n  const handleFilterChange = (filterType, value) => {\n    setFilters(prev => ({\n      ...prev,\n      [filterType]: value\n    }));\n  };\n  const clearFilters = () => {\n    setFilters({\n      category: 'all',\n      muscleGroup: 'all',\n      equipment: 'all',\n      difficulty: 'all',\n      maxDuration: null\n    });\n    setSearchQuery('');\n  };\n  const generateQuickWorkout = () => {\n    const selectedExercises = filteredExercises.slice(0, 4);\n    const workout = createWorkoutPlan(selectedExercises, 30);\n\n    // Ici on pourrait ouvrir une modal avec le programme généré\n    alert(`Programme généré !\\nDurée: ${workout.totalDuration} min\\nCalories estimées: ${workout.estimatedCalories}\\nExercices: ${workout.exercises.length}`);\n  };\n  const toggleFavorite = exerciseId => {\n    setFavoriteExercises(prev => {\n      const isFavorite = prev.some(ex => ex.id === exerciseId);\n      if (isFavorite) {\n        return prev.filter(ex => ex.id !== exerciseId);\n      } else {\n        const exercise = exercises.find(ex => ex.id === exerciseId);\n        return exercise ? [...prev, exercise] : prev;\n      }\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold text-gray-900 mb-4\",\n          children: \"Biblioth\\xE8que d'Exercices\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg text-gray-600 max-w-3xl mx-auto\",\n          children: \"D\\xE9couvrez notre collection compl\\xE8te d'exercices avec instructions d\\xE9taill\\xE9es, recommandations personnalis\\xE9es et programmes adapt\\xE9s \\xE0 vos objectifs.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-lg p-6 mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col lg:flex-row gap-4 items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 relative\",\n            children: [/*#__PURE__*/_jsxDEV(FiSearch, {\n              className: \"absolute left-3 top-3 h-5 w-5 text-gray-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"Rechercher un exercice, groupe musculaire...\",\n              value: searchQuery,\n              onChange: e => setSearchQuery(e.target.value),\n              className: \"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex gap-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowFilters(!showFilters),\n              className: `px-4 py-3 rounded-lg font-medium transition-colors flex items-center ${showFilters ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`,\n              children: [/*#__PURE__*/_jsxDEV(FiFilter, {\n                className: \"h-4 w-4 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 17\n              }, this), \"Filtres\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: generateQuickWorkout,\n              className: \"px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(FiTarget, {\n                className: \"h-4 w-4 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 17\n              }, this), \"Programme rapide\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 11\n        }, this), showFilters && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-6 pt-6 border-t border-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Cat\\xE9gorie\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 199,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: filters.category,\n                onChange: e => handleFilterChange('category', e.target.value),\n                className: \"w-full p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"all\",\n                  children: \"Toutes les cat\\xE9gories\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 205,\n                  columnNumber: 21\n                }, this), Object.entries(exerciseCategories).map(([key, category]) => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: key,\n                  children: category.name\n                }, key, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 207,\n                  columnNumber: 23\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Groupe musculaire\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 213,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: filters.muscleGroup,\n                onChange: e => handleFilterChange('muscleGroup', e.target.value),\n                className: \"w-full p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"all\",\n                  children: \"Tous les groupes\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 219,\n                  columnNumber: 21\n                }, this), Object.entries(muscleGroups).map(([key, group]) => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: key,\n                  children: group.name\n                }, key, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 221,\n                  columnNumber: 23\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 214,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"\\xC9quipement\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 227,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: filters.equipment,\n                onChange: e => handleFilterChange('equipment', e.target.value),\n                className: \"w-full p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"all\",\n                  children: \"Tout \\xE9quipement\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 233,\n                  columnNumber: 21\n                }, this), Object.entries(equipmentTypes).map(([key, equipment]) => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: key,\n                  children: equipment.name\n                }, key, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 235,\n                  columnNumber: 23\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Difficult\\xE9\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: filters.difficulty,\n                onChange: e => handleFilterChange('difficulty', e.target.value),\n                className: \"w-full p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"all\",\n                  children: \"Toutes difficult\\xE9s\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 247,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Facile\",\n                  children: \"Facile\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 248,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Mod\\xE9r\\xE9\",\n                  children: \"Mod\\xE9r\\xE9\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 249,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Difficile\",\n                  children: \"Difficile\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 250,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 242,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-4 flex justify-between items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700\",\n                children: \"Dur\\xE9e max (min):\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                value: filters.maxDuration || '',\n                onChange: e => handleFilterChange('maxDuration', e.target.value ? parseInt(e.target.value) : null),\n                placeholder: \"60\",\n                className: \"w-20 p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 258,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: clearFilters,\n              className: \"px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors\",\n              children: \"Effacer les filtres\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 162,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex space-x-1 bg-gray-100 p-1 rounded-lg mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setActiveTab('all'),\n          className: `flex-1 py-3 px-4 rounded-md text-sm font-medium transition-colors ${activeTab === 'all' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'}`,\n          children: [\"Tous (\", exercises.length, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 11\n        }, this), user && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setActiveTab('recommended'),\n            className: `flex-1 py-3 px-4 rounded-md text-sm font-medium transition-colors ${activeTab === 'recommended' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'}`,\n            children: [/*#__PURE__*/_jsxDEV(FiStar, {\n              className: \"inline mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 17\n            }, this), \"Recommand\\xE9s (\", recommendedExercises.length, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setActiveTab('favorites'),\n            className: `flex-1 py-3 px-4 rounded-md text-sm font-medium transition-colors ${activeTab === 'favorites' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'}`,\n            children: [/*#__PURE__*/_jsxDEV(FiHeart, {\n              className: \"inline mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 17\n            }, this), \"Favoris (\", favoriteExercises.length, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 302,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 278,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-6\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: [filteredExercises.length, \" exercice\", filteredExercises.length > 1 ? 's' : '', \" trouv\\xE9\", filteredExercises.length > 1 ? 's' : '', searchQuery && ` pour \"${searchQuery}\"`]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 318,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8\",\n        children: filteredExercises.map(exercise => {\n          var _exerciseCategories$e, _exerciseCategories$e2, _equipmentTypes$exerc, _equipmentTypes$exerc2;\n          const isFavorite = favoriteExercises.some(fav => fav.id === exercise.id);\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start justify-between mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-xl font-semibold text-gray-900 mb-2\",\n                    children: exercise.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 338,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-2 mb-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `px-3 py-1 rounded-full text-sm font-medium ${getDifficultyColor(exercise.difficulty)}`,\n                      children: exercise.difficulty\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 342,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-xs text-gray-500\",\n                      children: [(_exerciseCategories$e = exerciseCategories[exercise.category]) === null || _exerciseCategories$e === void 0 ? void 0 : _exerciseCategories$e.icon, \" \", (_exerciseCategories$e2 = exerciseCategories[exercise.category]) === null || _exerciseCategories$e2 === void 0 ? void 0 : _exerciseCategories$e2.name]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 345,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 341,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 337,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: e => {\n                    e.stopPropagation();\n                    toggleFavorite(exercise.id);\n                  },\n                  className: `p-2 rounded-full transition-colors ${isFavorite ? 'text-red-500 hover:text-red-600' : 'text-gray-400 hover:text-red-500'}`,\n                  children: /*#__PURE__*/_jsxDEV(FiHeart, {\n                    className: `h-5 w-5 ${isFavorite ? 'fill-current' : ''}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 361,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 350,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 336,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 mb-4 line-clamp-2\",\n                children: exercise.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 365,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-wrap gap-1 mb-4\",\n                children: exercise.muscleGroups.map(mg => {\n                  var _muscleGroups$mg;\n                  return /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full\",\n                    children: (_muscleGroups$mg = muscleGroups[mg]) === null || _muscleGroups$mg === void 0 ? void 0 : _muscleGroups$mg.name\n                  }, mg, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 372,\n                    columnNumber: 23\n                  }, this);\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 370,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center mb-4 text-sm text-gray-600\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"mr-2\",\n                  children: (_equipmentTypes$exerc = equipmentTypes[exercise.equipment]) === null || _equipmentTypes$exerc === void 0 ? void 0 : _equipmentTypes$exerc.icon\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 380,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: (_equipmentTypes$exerc2 = equipmentTypes[exercise.equipment]) === null || _equipmentTypes$exerc2 === void 0 ? void 0 : _equipmentTypes$exerc2.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 381,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 379,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between text-sm text-gray-500 mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(FiClock, {\n                    className: \"h-4 w-4 mr-1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 386,\n                    columnNumber: 23\n                  }, this), exercise.duration, \" min\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 385,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(FiZap, {\n                    className: \"h-4 w-4 mr-1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 390,\n                    columnNumber: 23\n                  }, this), exercise.calories, \" cal\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 389,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 384,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex gap-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setSelectedExercise(exercise),\n                  className: \"flex-1 bg-gray-100 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-200 transition-colors flex items-center justify-center text-sm\",\n                  children: [/*#__PURE__*/_jsxDEV(FiInfo, {\n                    className: \"h-4 w-4 mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 400,\n                    columnNumber: 23\n                  }, this), \"D\\xE9tails\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 396,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center text-sm\",\n                  children: [/*#__PURE__*/_jsxDEV(FiPlay, {\n                    className: \"h-4 w-4 mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 404,\n                    columnNumber: 23\n                  }, this), \"Commencer\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 403,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 395,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 335,\n              columnNumber: 17\n            }, this)\n          }, exercise.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 331,\n            columnNumber: 15\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 326,\n        columnNumber: 9\n      }, this), filteredExercises.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-gray-400 mb-4\",\n          children: /*#__PURE__*/_jsxDEV(FiSearch, {\n            className: \"h-16 w-16 mx-auto\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 418,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 417,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium text-gray-900 mb-2\",\n          children: \"Aucun exercice trouv\\xE9\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 420,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mb-4\",\n          children: \"Essayez de modifier vos crit\\xE8res de recherche ou vos filtres.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 423,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: clearFilters,\n          className: \"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors\",\n          children: \"Effacer les filtres\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 426,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 416,\n        columnNumber: 11\n      }, this), selectedExercise && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"text-3xl font-bold text-gray-900 mb-2\",\n                  children: selectedExercise.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 442,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `px-3 py-1 rounded-full text-sm font-medium ${getDifficultyColor(selectedExercise.difficulty)}`,\n                    children: selectedExercise.difficulty\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 446,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-600\",\n                    children: [(_exerciseCategories$s = exerciseCategories[selectedExercise.category]) === null || _exerciseCategories$s === void 0 ? void 0 : _exerciseCategories$s.icon, \" \", (_exerciseCategories$s2 = exerciseCategories[selectedExercise.category]) === null || _exerciseCategories$s2 === void 0 ? void 0 : _exerciseCategories$s2.name]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 449,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 445,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 441,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setSelectedExercise(null),\n                className: \"text-gray-400 hover:text-gray-600 text-2xl\",\n                children: \"\\u2715\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 454,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 440,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"lg:col-span-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-xl font-semibold text-gray-900 mb-3\",\n                    children: \"Description\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 466,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-gray-600 leading-relaxed\",\n                    children: selectedExercise.description\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 467,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 465,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-xl font-semibold text-gray-900 mb-4\",\n                    children: \"Instructions d\\xE9taill\\xE9es\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 471,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"ol\", {\n                    className: \"list-decimal list-inside space-y-3\",\n                    children: selectedExercise.instructions.map((instruction, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n                      className: \"text-gray-600 leading-relaxed pl-2\",\n                      children: instruction\n                    }, index, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 474,\n                      columnNumber: 27\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 472,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 470,\n                  columnNumber: 21\n                }, this), selectedExercise.tips && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-xl font-semibold text-gray-900 mb-4\",\n                    children: \"Conseils\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 483,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                    className: \"space-y-2\",\n                    children: selectedExercise.tips.map((tip, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n                      className: \"flex items-start text-gray-600\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-blue-500 mr-2 mt-1\",\n                        children: \"\\u2022\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 487,\n                        columnNumber: 31\n                      }, this), tip]\n                    }, index, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 486,\n                      columnNumber: 29\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 484,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 482,\n                  columnNumber: 23\n                }, this), selectedExercise.variations && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-xl font-semibold text-gray-900 mb-4\",\n                    children: \"Variations\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 497,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                    className: \"space-y-2\",\n                    children: selectedExercise.variations.map((variation, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n                      className: \"flex items-start text-gray-600\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-green-500 mr-2 mt-1\",\n                        children: \"\\u2022\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 501,\n                        columnNumber: 31\n                      }, this), variation]\n                    }, index, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 500,\n                      columnNumber: 29\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 498,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 496,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 464,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"lg:col-span-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-gray-50 rounded-lg p-6 mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-lg font-semibold text-gray-900 mb-4\",\n                    children: \"Informations\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 513,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"space-y-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex justify-between\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-gray-600\",\n                        children: \"Dur\\xE9e\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 517,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"font-medium\",\n                        children: [selectedExercise.duration, \" min\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 518,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 516,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex justify-between\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-gray-600\",\n                        children: \"Calories\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 521,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"font-medium\",\n                        children: [selectedExercise.calories, \" cal\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 522,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 520,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex justify-between\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-gray-600\",\n                        children: \"\\xC9quipement\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 525,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"font-medium\",\n                        children: (_equipmentTypes$selec = equipmentTypes[selectedExercise.equipment]) === null || _equipmentTypes$selec === void 0 ? void 0 : _equipmentTypes$selec.name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 526,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 524,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 515,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 512,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-gray-50 rounded-lg p-6 mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-lg font-semibold text-gray-900 mb-4\",\n                    children: \"Groupes musculaires\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 532,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex flex-wrap gap-2\",\n                    children: selectedExercise.muscleGroups.map(mg => {\n                      var _muscleGroups$mg2, _muscleGroups$mg3;\n                      return /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"px-3 py-1 bg-blue-100 text-blue-700 text-sm rounded-full\",\n                        children: [(_muscleGroups$mg2 = muscleGroups[mg]) === null || _muscleGroups$mg2 === void 0 ? void 0 : _muscleGroups$mg2.icon, \" \", (_muscleGroups$mg3 = muscleGroups[mg]) === null || _muscleGroups$mg3 === void 0 ? void 0 : _muscleGroups$mg3.name]\n                      }, mg, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 535,\n                        columnNumber: 27\n                      }, this);\n                    })\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 533,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 531,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => toggleFavorite(selectedExercise.id),\n                    className: `w-full py-3 px-6 rounded-lg font-medium transition-colors flex items-center justify-center ${favoriteExercises.some(fav => fav.id === selectedExercise.id) ? 'bg-red-100 text-red-700 hover:bg-red-200' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`,\n                    children: [/*#__PURE__*/_jsxDEV(FiHeart, {\n                      className: \"h-5 w-5 mr-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 551,\n                      columnNumber: 25\n                    }, this), favoriteExercises.some(fav => fav.id === selectedExercise.id) ? 'Retirer des favoris' : 'Ajouter aux favoris']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 543,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"w-full bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center font-medium\",\n                    children: [/*#__PURE__*/_jsxDEV(FiPlay, {\n                      className: \"h-5 w-5 mr-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 556,\n                      columnNumber: 25\n                    }, this), \"Commencer l'exercice\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 555,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 542,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 511,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 462,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 439,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 438,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 437,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 149,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 148,\n    columnNumber: 5\n  }, this);\n};\n_s(Exercises, \"8mInFApJY8LT+1SCG/Obu5+1mGw=\", false, function () {\n  return [useAuth];\n});\n_c = Exercises;\nexport default Exercises;\nvar _c;\n$RefreshReg$(_c, \"Exercises\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "FiPlay", "<PERSON><PERSON><PERSON>", "FiZap", "<PERSON><PERSON><PERSON><PERSON>", "FiSearch", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "FiStar", "FiInfo", "FiPlus", "FiTrendingUp", "FiBookmark", "FiVideo", "FiBarChart3", "FiCalendar", "FiX", "FiEdit3", "FiSave", "useAuth", "exercises", "exerciseCategories", "muscleGroups", "equipmentTypes", "getDifficultyColor", "getRecommendedExercises", "searchExercises", "createWorkoutPlan", "toggleFavorite", "loadFavorites", "addToHistory", "getExerciseHistory", "calculateProgressStats", "createCustomProgram", "getExerciseVideo", "getExerciseAnimation", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Exercises", "_s", "_exerciseCategories$s", "_exerciseCategories$s2", "_equipmentTypes$selec", "user", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "setSelectedExercise", "searchQuery", "setSearch<PERSON>uery", "showFilters", "setShowFilters", "filters", "setFilters", "category", "muscleGroup", "equipment", "difficulty", "maxDuration", "activeTab", "setActiveTab", "recommendedExercises", "setRecommendedExercises", "favoriteExercises", "setFavoriteExercises", "filteredExercises", "setFilteredExercises", "exerciseHistory", "setExerciseHistory", "showCreateProgram", "setShowCreateProgram", "selectedExercisesForProgram", "setSelectedExercisesForProgram", "showProgressModal", "setShowProgressModal", "progressData", "setProgressData", "showPerformanceModal", "setShowPerformanceModal", "performanceData", "setPerformanceData", "sets", "reps", "weight", "duration", "notes", "recommendations", "primaryGoal", "savedFavorites", "favoriteExerciseObjects", "filter", "ex", "includes", "id", "history", "results", "Object", "values", "some", "f", "handleFilterChange", "filterType", "value", "prev", "clearFilters", "generateQuickWorkout", "selectedExercises", "slice", "workout", "alert", "totalDuration", "estimatedCalories", "length", "exerciseId", "isFavorite", "exercise", "find", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "placeholder", "onChange", "e", "target", "onClick", "entries", "map", "key", "name", "group", "parseInt", "_exerciseCategories$e", "_exerciseCategories$e2", "_equipmentTypes$exerc", "_equipmentTypes$exerc2", "fav", "icon", "stopPropagation", "description", "mg", "_muscleGroups$mg", "calories", "instructions", "instruction", "index", "tips", "tip", "variations", "variation", "_muscleGroups$mg2", "_muscleGroups$mg3", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/projet fitness/DATA/fitness-ui/src/pages/Exercises.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  <PERSON><PERSON>lay,\n  Fi<PERSON>lock,\n  FiZap,\n  FiFilter,\n  FiSearch,\n  FiTarget,\n  FiHeart,\n  FiStar,\n  FiInfo,\n  FiPlus,\n  FiTrendingUp,\n  FiBookmark,\n  FiVideo,\n  FiBarChart3,\n  FiCalendar,\n  FiX,\n  FiEdit3,\n  FiSave\n} from 'react-icons/fi';\nimport { useAuth } from '../contexts/AuthContext';\nimport {\n  exercises,\n  exerciseCategories,\n  muscleGroups,\n  equipmentTypes,\n  getDifficultyColor,\n  getRecommendedExercises,\n  searchExercises,\n  createWorkoutPlan,\n  toggleFavorite,\n  loadFavorites,\n  addToHistory,\n  getExerciseHistory,\n  calculateProgressStats,\n  createCustomProgram,\n  getExerciseVideo,\n  getExerciseAnimation\n} from '../utils/exerciseUtils';\n\nconst Exercises = () => {\n  const { user } = useAuth();\n\n  const [selectedExercise, setSelectedExercise] = useState(null);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [showFilters, setShowFilters] = useState(false);\n  const [filters, setFilters] = useState({\n    category: 'all',\n    muscleGroup: 'all',\n    equipment: 'all',\n    difficulty: 'all',\n    maxDuration: null\n  });\n  const [activeTab, setActiveTab] = useState('all');\n  const [recommendedExercises, setRecommendedExercises] = useState([]);\n  const [favoriteExercises, setFavoriteExercises] = useState([]);\n  const [filteredExercises, setFilteredExercises] = useState(exercises);\n\n  // Nouveaux états pour les fonctionnalités avancées\n  const [exerciseHistory, setExerciseHistory] = useState([]);\n  const [showCreateProgram, setShowCreateProgram] = useState(false);\n  const [selectedExercisesForProgram, setSelectedExercisesForProgram] = useState([]);\n  const [showProgressModal, setShowProgressModal] = useState(false);\n  const [progressData, setProgressData] = useState(null);\n  const [showPerformanceModal, setShowPerformanceModal] = useState(false);\n  const [performanceData, setPerformanceData] = useState({\n    sets: 3,\n    reps: 12,\n    weight: '',\n    duration: '',\n    notes: ''\n  });\n\n  useEffect(() => {\n    // Générer des recommandations basées sur le profil utilisateur\n    if (user) {\n      const recommendations = getRecommendedExercises(user, user.primaryGoal || 'general_fitness');\n      setRecommendedExercises(recommendations);\n    }\n\n    // Charger les favoris depuis localStorage\n    const savedFavorites = loadFavorites();\n    const favoriteExerciseObjects = exercises.filter(ex => savedFavorites.includes(ex.id));\n    setFavoriteExercises(favoriteExerciseObjects);\n\n    // Charger l'historique des exercices\n    const history = getExerciseHistory();\n    setExerciseHistory(history);\n  }, [user]);\n\n  useEffect(() => {\n    // Appliquer les filtres et la recherche\n    let results = exercises;\n    \n    if (activeTab === 'recommended') {\n      results = recommendedExercises;\n    } else if (activeTab === 'favorites') {\n      results = favoriteExercises;\n    }\n    \n    if (searchQuery || Object.values(filters).some(f => f !== 'all' && f !== null)) {\n      results = searchExercises(searchQuery, filters);\n    }\n    \n    setFilteredExercises(results);\n  }, [searchQuery, filters, activeTab, recommendedExercises, favoriteExercises]);\n\n  const handleFilterChange = (filterType, value) => {\n    setFilters(prev => ({\n      ...prev,\n      [filterType]: value\n    }));\n  };\n\n  const clearFilters = () => {\n    setFilters({\n      category: 'all',\n      muscleGroup: 'all',\n      equipment: 'all',\n      difficulty: 'all',\n      maxDuration: null\n    });\n    setSearchQuery('');\n  };\n\n  const generateQuickWorkout = () => {\n    const selectedExercises = filteredExercises.slice(0, 4);\n    const workout = createWorkoutPlan(selectedExercises, 30);\n    \n    // Ici on pourrait ouvrir une modal avec le programme généré\n    alert(`Programme généré !\\nDurée: ${workout.totalDuration} min\\nCalories estimées: ${workout.estimatedCalories}\\nExercices: ${workout.exercises.length}`);\n  };\n\n  const toggleFavorite = (exerciseId) => {\n    setFavoriteExercises(prev => {\n      const isFavorite = prev.some(ex => ex.id === exerciseId);\n      if (isFavorite) {\n        return prev.filter(ex => ex.id !== exerciseId);\n      } else {\n        const exercise = exercises.find(ex => ex.id === exerciseId);\n        return exercise ? [...prev, exercise] : prev;\n      }\n    });\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Header */}\n        <div className=\"text-center mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-4\">\n            Bibliothèque d'Exercices\n          </h1>\n          <p className=\"text-lg text-gray-600 max-w-3xl mx-auto\">\n            Découvrez notre collection complète d'exercices avec instructions détaillées, \n            recommandations personnalisées et programmes adaptés à vos objectifs.\n          </p>\n        </div>\n\n        {/* Search and Quick Actions */}\n        <div className=\"bg-white rounded-lg shadow-lg p-6 mb-8\">\n          <div className=\"flex flex-col lg:flex-row gap-4 items-center\">\n            <div className=\"flex-1 relative\">\n              <FiSearch className=\"absolute left-3 top-3 h-5 w-5 text-gray-400\" />\n              <input\n                type=\"text\"\n                placeholder=\"Rechercher un exercice, groupe musculaire...\"\n                value={searchQuery}\n                onChange={(e) => setSearchQuery(e.target.value)}\n                className=\"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              />\n            </div>\n            <div className=\"flex gap-3\">\n              <button\n                onClick={() => setShowFilters(!showFilters)}\n                className={`px-4 py-3 rounded-lg font-medium transition-colors flex items-center ${\n                  showFilters ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'\n                }`}\n              >\n                <FiFilter className=\"h-4 w-4 mr-2\" />\n                Filtres\n              </button>\n              <button\n                onClick={generateQuickWorkout}\n                className=\"px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center\"\n              >\n                <FiTarget className=\"h-4 w-4 mr-2\" />\n                Programme rapide\n              </button>\n            </div>\n          </div>\n\n          {/* Advanced Filters */}\n          {showFilters && (\n            <div className=\"mt-6 pt-6 border-t border-gray-200\">\n              <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Catégorie</label>\n                  <select\n                    value={filters.category}\n                    onChange={(e) => handleFilterChange('category', e.target.value)}\n                    className=\"w-full p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  >\n                    <option value=\"all\">Toutes les catégories</option>\n                    {Object.entries(exerciseCategories).map(([key, category]) => (\n                      <option key={key} value={key}>{category.name}</option>\n                    ))}\n                  </select>\n                </div>\n                \n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Groupe musculaire</label>\n                  <select\n                    value={filters.muscleGroup}\n                    onChange={(e) => handleFilterChange('muscleGroup', e.target.value)}\n                    className=\"w-full p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  >\n                    <option value=\"all\">Tous les groupes</option>\n                    {Object.entries(muscleGroups).map(([key, group]) => (\n                      <option key={key} value={key}>{group.name}</option>\n                    ))}\n                  </select>\n                </div>\n                \n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Équipement</label>\n                  <select\n                    value={filters.equipment}\n                    onChange={(e) => handleFilterChange('equipment', e.target.value)}\n                    className=\"w-full p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  >\n                    <option value=\"all\">Tout équipement</option>\n                    {Object.entries(equipmentTypes).map(([key, equipment]) => (\n                      <option key={key} value={key}>{equipment.name}</option>\n                    ))}\n                  </select>\n                </div>\n                \n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Difficulté</label>\n                  <select\n                    value={filters.difficulty}\n                    onChange={(e) => handleFilterChange('difficulty', e.target.value)}\n                    className=\"w-full p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  >\n                    <option value=\"all\">Toutes difficultés</option>\n                    <option value=\"Facile\">Facile</option>\n                    <option value=\"Modéré\">Modéré</option>\n                    <option value=\"Difficile\">Difficile</option>\n                  </select>\n                </div>\n              </div>\n              \n              <div className=\"mt-4 flex justify-between items-center\">\n                <div className=\"flex items-center space-x-4\">\n                  <label className=\"block text-sm font-medium text-gray-700\">Durée max (min):</label>\n                  <input\n                    type=\"number\"\n                    value={filters.maxDuration || ''}\n                    onChange={(e) => handleFilterChange('maxDuration', e.target.value ? parseInt(e.target.value) : null)}\n                    placeholder=\"60\"\n                    className=\"w-20 p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  />\n                </div>\n                <button\n                  onClick={clearFilters}\n                  className=\"px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors\"\n                >\n                  Effacer les filtres\n                </button>\n              </div>\n            </div>\n          )}\n        </div>\n\n        {/* Tabs */}\n        <div className=\"flex space-x-1 bg-gray-100 p-1 rounded-lg mb-8\">\n          <button\n            onClick={() => setActiveTab('all')}\n            className={`flex-1 py-3 px-4 rounded-md text-sm font-medium transition-colors ${\n              activeTab === 'all'\n                ? 'bg-white text-blue-600 shadow-sm'\n                : 'text-gray-600 hover:text-gray-900'\n            }`}\n          >\n            Tous ({exercises.length})\n          </button>\n          {user && (\n            <>\n              <button\n                onClick={() => setActiveTab('recommended')}\n                className={`flex-1 py-3 px-4 rounded-md text-sm font-medium transition-colors ${\n                  activeTab === 'recommended'\n                    ? 'bg-white text-blue-600 shadow-sm'\n                    : 'text-gray-600 hover:text-gray-900'\n                }`}\n              >\n                <FiStar className=\"inline mr-2\" />\n                Recommandés ({recommendedExercises.length})\n              </button>\n              <button\n                onClick={() => setActiveTab('favorites')}\n                className={`flex-1 py-3 px-4 rounded-md text-sm font-medium transition-colors ${\n                  activeTab === 'favorites'\n                    ? 'bg-white text-blue-600 shadow-sm'\n                    : 'text-gray-600 hover:text-gray-900'\n                }`}\n              >\n                <FiHeart className=\"inline mr-2\" />\n                Favoris ({favoriteExercises.length})\n              </button>\n            </>\n          )}\n        </div>\n\n        {/* Results Summary */}\n        <div className=\"mb-6\">\n          <p className=\"text-gray-600\">\n            {filteredExercises.length} exercice{filteredExercises.length > 1 ? 's' : ''} trouvé{filteredExercises.length > 1 ? 's' : ''}\n            {searchQuery && ` pour \"${searchQuery}\"`}\n          </p>\n        </div>\n\n        {/* Exercise Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8\">\n          {filteredExercises.map(exercise => {\n            const isFavorite = favoriteExercises.some(fav => fav.id === exercise.id);\n\n            return (\n              <div\n                key={exercise.id}\n                className=\"bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow\"\n              >\n                <div className=\"p-6\">\n                  <div className=\"flex items-start justify-between mb-4\">\n                    <div className=\"flex-1\">\n                      <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">\n                        {exercise.name}\n                      </h3>\n                      <div className=\"flex items-center space-x-2 mb-3\">\n                        <span className={`px-3 py-1 rounded-full text-sm font-medium ${getDifficultyColor(exercise.difficulty)}`}>\n                          {exercise.difficulty}\n                        </span>\n                        <span className=\"text-xs text-gray-500\">\n                          {exerciseCategories[exercise.category]?.icon} {exerciseCategories[exercise.category]?.name}\n                        </span>\n                      </div>\n                    </div>\n                    <button\n                      onClick={(e) => {\n                        e.stopPropagation();\n                        toggleFavorite(exercise.id);\n                      }}\n                      className={`p-2 rounded-full transition-colors ${\n                        isFavorite\n                          ? 'text-red-500 hover:text-red-600'\n                          : 'text-gray-400 hover:text-red-500'\n                      }`}\n                    >\n                      <FiHeart className={`h-5 w-5 ${isFavorite ? 'fill-current' : ''}`} />\n                    </button>\n                  </div>\n\n                  <p className=\"text-gray-600 mb-4 line-clamp-2\">\n                    {exercise.description}\n                  </p>\n\n                  {/* Muscle Groups */}\n                  <div className=\"flex flex-wrap gap-1 mb-4\">\n                    {exercise.muscleGroups.map(mg => (\n                      <span key={mg} className=\"px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full\">\n                        {muscleGroups[mg]?.name}\n                      </span>\n                    ))}\n                  </div>\n\n                  {/* Equipment */}\n                  <div className=\"flex items-center mb-4 text-sm text-gray-600\">\n                    <span className=\"mr-2\">{equipmentTypes[exercise.equipment]?.icon}</span>\n                    <span>{equipmentTypes[exercise.equipment]?.name}</span>\n                  </div>\n\n                  <div className=\"flex items-center justify-between text-sm text-gray-500 mb-4\">\n                    <div className=\"flex items-center\">\n                      <FiClock className=\"h-4 w-4 mr-1\" />\n                      {exercise.duration} min\n                    </div>\n                    <div className=\"flex items-center\">\n                      <FiZap className=\"h-4 w-4 mr-1\" />\n                      {exercise.calories} cal\n                    </div>\n                  </div>\n\n                  <div className=\"flex gap-2\">\n                    <button\n                      onClick={() => setSelectedExercise(exercise)}\n                      className=\"flex-1 bg-gray-100 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-200 transition-colors flex items-center justify-center text-sm\"\n                    >\n                      <FiInfo className=\"h-4 w-4 mr-2\" />\n                      Détails\n                    </button>\n                    <button className=\"flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center text-sm\">\n                      <FiPlay className=\"h-4 w-4 mr-2\" />\n                      Commencer\n                    </button>\n                  </div>\n                </div>\n              </div>\n            );\n          })}\n        </div>\n\n        {/* Empty State */}\n        {filteredExercises.length === 0 && (\n          <div className=\"text-center py-12\">\n            <div className=\"text-gray-400 mb-4\">\n              <FiSearch className=\"h-16 w-16 mx-auto\" />\n            </div>\n            <h3 className=\"text-lg font-medium text-gray-900 mb-2\">\n              Aucun exercice trouvé\n            </h3>\n            <p className=\"text-gray-600 mb-4\">\n              Essayez de modifier vos critères de recherche ou vos filtres.\n            </p>\n            <button\n              onClick={clearFilters}\n              className=\"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors\"\n            >\n              Effacer les filtres\n            </button>\n          </div>\n        )}\n\n        {/* Exercise Detail Modal */}\n        {selectedExercise && (\n          <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\">\n            <div className=\"bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto\">\n              <div className=\"p-6\">\n                <div className=\"flex items-center justify-between mb-6\">\n                  <div>\n                    <h2 className=\"text-3xl font-bold text-gray-900 mb-2\">\n                      {selectedExercise.name}\n                    </h2>\n                    <div className=\"flex items-center space-x-3\">\n                      <span className={`px-3 py-1 rounded-full text-sm font-medium ${getDifficultyColor(selectedExercise.difficulty)}`}>\n                        {selectedExercise.difficulty}\n                      </span>\n                      <span className=\"text-gray-600\">\n                        {exerciseCategories[selectedExercise.category]?.icon} {exerciseCategories[selectedExercise.category]?.name}\n                      </span>\n                    </div>\n                  </div>\n                  <button\n                    onClick={() => setSelectedExercise(null)}\n                    className=\"text-gray-400 hover:text-gray-600 text-2xl\"\n                  >\n                    ✕\n                  </button>\n                </div>\n\n                <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n                  {/* Main Content */}\n                  <div className=\"lg:col-span-2\">\n                    <div className=\"mb-6\">\n                      <h3 className=\"text-xl font-semibold text-gray-900 mb-3\">Description</h3>\n                      <p className=\"text-gray-600 leading-relaxed\">{selectedExercise.description}</p>\n                    </div>\n\n                    <div className=\"mb-6\">\n                      <h3 className=\"text-xl font-semibold text-gray-900 mb-4\">Instructions détaillées</h3>\n                      <ol className=\"list-decimal list-inside space-y-3\">\n                        {selectedExercise.instructions.map((instruction, index) => (\n                          <li key={index} className=\"text-gray-600 leading-relaxed pl-2\">\n                            {instruction}\n                          </li>\n                        ))}\n                      </ol>\n                    </div>\n\n                    {selectedExercise.tips && (\n                      <div className=\"mb-6\">\n                        <h3 className=\"text-xl font-semibold text-gray-900 mb-4\">Conseils</h3>\n                        <ul className=\"space-y-2\">\n                          {selectedExercise.tips.map((tip, index) => (\n                            <li key={index} className=\"flex items-start text-gray-600\">\n                              <span className=\"text-blue-500 mr-2 mt-1\">•</span>\n                              {tip}\n                            </li>\n                          ))}\n                        </ul>\n                      </div>\n                    )}\n\n                    {selectedExercise.variations && (\n                      <div className=\"mb-6\">\n                        <h3 className=\"text-xl font-semibold text-gray-900 mb-4\">Variations</h3>\n                        <ul className=\"space-y-2\">\n                          {selectedExercise.variations.map((variation, index) => (\n                            <li key={index} className=\"flex items-start text-gray-600\">\n                              <span className=\"text-green-500 mr-2 mt-1\">•</span>\n                              {variation}\n                            </li>\n                          ))}\n                        </ul>\n                      </div>\n                    )}\n                  </div>\n\n                  {/* Sidebar */}\n                  <div className=\"lg:col-span-1\">\n                    <div className=\"bg-gray-50 rounded-lg p-6 mb-6\">\n                      <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Informations</h3>\n\n                      <div className=\"space-y-4\">\n                        <div className=\"flex justify-between\">\n                          <span className=\"text-gray-600\">Durée</span>\n                          <span className=\"font-medium\">{selectedExercise.duration} min</span>\n                        </div>\n                        <div className=\"flex justify-between\">\n                          <span className=\"text-gray-600\">Calories</span>\n                          <span className=\"font-medium\">{selectedExercise.calories} cal</span>\n                        </div>\n                        <div className=\"flex justify-between\">\n                          <span className=\"text-gray-600\">Équipement</span>\n                          <span className=\"font-medium\">{equipmentTypes[selectedExercise.equipment]?.name}</span>\n                        </div>\n                      </div>\n                    </div>\n\n                    <div className=\"bg-gray-50 rounded-lg p-6 mb-6\">\n                      <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Groupes musculaires</h3>\n                      <div className=\"flex flex-wrap gap-2\">\n                        {selectedExercise.muscleGroups.map(mg => (\n                          <span key={mg} className=\"px-3 py-1 bg-blue-100 text-blue-700 text-sm rounded-full\">\n                            {muscleGroups[mg]?.icon} {muscleGroups[mg]?.name}\n                          </span>\n                        ))}\n                      </div>\n                    </div>\n\n                    <div className=\"space-y-3\">\n                      <button\n                        onClick={() => toggleFavorite(selectedExercise.id)}\n                        className={`w-full py-3 px-6 rounded-lg font-medium transition-colors flex items-center justify-center ${\n                          favoriteExercises.some(fav => fav.id === selectedExercise.id)\n                            ? 'bg-red-100 text-red-700 hover:bg-red-200'\n                            : 'bg-gray-100 text-gray-700 hover:bg-gray-200'\n                        }`}\n                      >\n                        <FiHeart className=\"h-5 w-5 mr-2\" />\n                        {favoriteExercises.some(fav => fav.id === selectedExercise.id) ? 'Retirer des favoris' : 'Ajouter aux favoris'}\n                      </button>\n\n                      <button className=\"w-full bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center font-medium\">\n                        <FiPlay className=\"h-5 w-5 mr-2\" />\n                        Commencer l'exercice\n                      </button>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default Exercises;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,MAAM,EACNC,OAAO,EACPC,KAAK,EACLC,QAAQ,EACRC,QAAQ,EACRC,QAAQ,EACRC,OAAO,EACPC,MAAM,EACNC,MAAM,EACNC,MAAM,EACNC,YAAY,EACZC,UAAU,EACVC,OAAO,EACPC,WAAW,EACXC,UAAU,EACVC,GAAG,EACHC,OAAO,EACPC,MAAM,QACD,gBAAgB;AACvB,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SACEC,SAAS,EACTC,kBAAkB,EAClBC,YAAY,EACZC,cAAc,EACdC,kBAAkB,EAClBC,uBAAuB,EACvBC,eAAe,EACfC,iBAAiB,EACjBC,cAAc,EACdC,aAAa,EACbC,YAAY,EACZC,kBAAkB,EAClBC,sBAAsB,EACtBC,mBAAmB,EACnBC,gBAAgB,EAChBC,oBAAoB,QACf,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEhC,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA;EACtB,MAAM;IAAEC;EAAK,CAAC,GAAG1B,OAAO,CAAC,CAAC;EAE1B,MAAM,CAAC2B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGhD,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACiD,WAAW,EAAEC,cAAc,CAAC,GAAGlD,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACmD,WAAW,EAAEC,cAAc,CAAC,GAAGpD,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACqD,OAAO,EAAEC,UAAU,CAAC,GAAGtD,QAAQ,CAAC;IACrCuD,QAAQ,EAAE,KAAK;IACfC,WAAW,EAAE,KAAK;IAClBC,SAAS,EAAE,KAAK;IAChBC,UAAU,EAAE,KAAK;IACjBC,WAAW,EAAE;EACf,CAAC,CAAC;EACF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG7D,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC8D,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG/D,QAAQ,CAAC,EAAE,CAAC;EACpE,MAAM,CAACgE,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGjE,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACkE,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGnE,QAAQ,CAACqB,SAAS,CAAC;;EAErE;EACA,MAAM,CAAC+C,eAAe,EAAEC,kBAAkB,CAAC,GAAGrE,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACsE,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGvE,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACwE,2BAA2B,EAAEC,8BAA8B,CAAC,GAAGzE,QAAQ,CAAC,EAAE,CAAC;EAClF,MAAM,CAAC0E,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG3E,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC4E,YAAY,EAAEC,eAAe,CAAC,GAAG7E,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC8E,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG/E,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAACgF,eAAe,EAAEC,kBAAkB,CAAC,GAAGjF,QAAQ,CAAC;IACrDkF,IAAI,EAAE,CAAC;IACPC,IAAI,EAAE,EAAE;IACRC,MAAM,EAAE,EAAE;IACVC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE;EACT,CAAC,CAAC;EAEFrF,SAAS,CAAC,MAAM;IACd;IACA,IAAI6C,IAAI,EAAE;MACR,MAAMyC,eAAe,GAAG7D,uBAAuB,CAACoB,IAAI,EAAEA,IAAI,CAAC0C,WAAW,IAAI,iBAAiB,CAAC;MAC5FzB,uBAAuB,CAACwB,eAAe,CAAC;IAC1C;;IAEA;IACA,MAAME,cAAc,GAAG3D,aAAa,CAAC,CAAC;IACtC,MAAM4D,uBAAuB,GAAGrE,SAAS,CAACsE,MAAM,CAACC,EAAE,IAAIH,cAAc,CAACI,QAAQ,CAACD,EAAE,CAACE,EAAE,CAAC,CAAC;IACtF7B,oBAAoB,CAACyB,uBAAuB,CAAC;;IAE7C;IACA,MAAMK,OAAO,GAAG/D,kBAAkB,CAAC,CAAC;IACpCqC,kBAAkB,CAAC0B,OAAO,CAAC;EAC7B,CAAC,EAAE,CAACjD,IAAI,CAAC,CAAC;EAEV7C,SAAS,CAAC,MAAM;IACd;IACA,IAAI+F,OAAO,GAAG3E,SAAS;IAEvB,IAAIuC,SAAS,KAAK,aAAa,EAAE;MAC/BoC,OAAO,GAAGlC,oBAAoB;IAChC,CAAC,MAAM,IAAIF,SAAS,KAAK,WAAW,EAAE;MACpCoC,OAAO,GAAGhC,iBAAiB;IAC7B;IAEA,IAAIf,WAAW,IAAIgD,MAAM,CAACC,MAAM,CAAC7C,OAAO,CAAC,CAAC8C,IAAI,CAACC,CAAC,IAAIA,CAAC,KAAK,KAAK,IAAIA,CAAC,KAAK,IAAI,CAAC,EAAE;MAC9EJ,OAAO,GAAGrE,eAAe,CAACsB,WAAW,EAAEI,OAAO,CAAC;IACjD;IAEAc,oBAAoB,CAAC6B,OAAO,CAAC;EAC/B,CAAC,EAAE,CAAC/C,WAAW,EAAEI,OAAO,EAAEO,SAAS,EAAEE,oBAAoB,EAAEE,iBAAiB,CAAC,CAAC;EAE9E,MAAMqC,kBAAkB,GAAGA,CAACC,UAAU,EAAEC,KAAK,KAAK;IAChDjD,UAAU,CAACkD,IAAI,KAAK;MAClB,GAAGA,IAAI;MACP,CAACF,UAAU,GAAGC;IAChB,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAME,YAAY,GAAGA,CAAA,KAAM;IACzBnD,UAAU,CAAC;MACTC,QAAQ,EAAE,KAAK;MACfC,WAAW,EAAE,KAAK;MAClBC,SAAS,EAAE,KAAK;MAChBC,UAAU,EAAE,KAAK;MACjBC,WAAW,EAAE;IACf,CAAC,CAAC;IACFT,cAAc,CAAC,EAAE,CAAC;EACpB,CAAC;EAED,MAAMwD,oBAAoB,GAAGA,CAAA,KAAM;IACjC,MAAMC,iBAAiB,GAAGzC,iBAAiB,CAAC0C,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;IACvD,MAAMC,OAAO,GAAGjF,iBAAiB,CAAC+E,iBAAiB,EAAE,EAAE,CAAC;;IAExD;IACAG,KAAK,CAAC,8BAA8BD,OAAO,CAACE,aAAa,4BAA4BF,OAAO,CAACG,iBAAiB,gBAAgBH,OAAO,CAACxF,SAAS,CAAC4F,MAAM,EAAE,CAAC;EAC3J,CAAC;EAED,MAAMpF,cAAc,GAAIqF,UAAU,IAAK;IACrCjD,oBAAoB,CAACuC,IAAI,IAAI;MAC3B,MAAMW,UAAU,GAAGX,IAAI,CAACL,IAAI,CAACP,EAAE,IAAIA,EAAE,CAACE,EAAE,KAAKoB,UAAU,CAAC;MACxD,IAAIC,UAAU,EAAE;QACd,OAAOX,IAAI,CAACb,MAAM,CAACC,EAAE,IAAIA,EAAE,CAACE,EAAE,KAAKoB,UAAU,CAAC;MAChD,CAAC,MAAM;QACL,MAAME,QAAQ,GAAG/F,SAAS,CAACgG,IAAI,CAACzB,EAAE,IAAIA,EAAE,CAACE,EAAE,KAAKoB,UAAU,CAAC;QAC3D,OAAOE,QAAQ,GAAG,CAAC,GAAGZ,IAAI,EAAEY,QAAQ,CAAC,GAAGZ,IAAI;MAC9C;IACF,CAAC,CAAC;EACJ,CAAC;EAED,oBACElE,OAAA;IAAKgF,SAAS,EAAC,yBAAyB;IAAAC,QAAA,eACtCjF,OAAA;MAAKgF,SAAS,EAAC,6CAA6C;MAAAC,QAAA,gBAE1DjF,OAAA;QAAKgF,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BjF,OAAA;UAAIgF,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAEtD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLrF,OAAA;UAAGgF,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EAAC;QAGvD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGNrF,OAAA;QAAKgF,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDjF,OAAA;UAAKgF,SAAS,EAAC,8CAA8C;UAAAC,QAAA,gBAC3DjF,OAAA;YAAKgF,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9BjF,OAAA,CAAChC,QAAQ;cAACgH,SAAS,EAAC;YAA6C;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpErF,OAAA;cACEsF,IAAI,EAAC,MAAM;cACXC,WAAW,EAAC,8CAA8C;cAC1DtB,KAAK,EAAEtD,WAAY;cACnB6E,QAAQ,EAAGC,CAAC,IAAK7E,cAAc,CAAC6E,CAAC,CAACC,MAAM,CAACzB,KAAK,CAAE;cAChDe,SAAS,EAAC;YAA8G;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNrF,OAAA;YAAKgF,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBjF,OAAA;cACE2F,OAAO,EAAEA,CAAA,KAAM7E,cAAc,CAAC,CAACD,WAAW,CAAE;cAC5CmE,SAAS,EAAE,wEACTnE,WAAW,GAAG,wBAAwB,GAAG,6CAA6C,EACrF;cAAAoE,QAAA,gBAEHjF,OAAA,CAACjC,QAAQ;gBAACiH,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,WAEvC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTrF,OAAA;cACE2F,OAAO,EAAEvB,oBAAqB;cAC9BY,SAAS,EAAC,qGAAqG;cAAAC,QAAA,gBAE/GjF,OAAA,CAAC/B,QAAQ;gBAAC+G,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,oBAEvC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGLxE,WAAW,iBACVb,OAAA;UAAKgF,SAAS,EAAC,oCAAoC;UAAAC,QAAA,gBACjDjF,OAAA;YAAKgF,SAAS,EAAC,sDAAsD;YAAAC,QAAA,gBACnEjF,OAAA;cAAAiF,QAAA,gBACEjF,OAAA;gBAAOgF,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACjFrF,OAAA;gBACEiE,KAAK,EAAElD,OAAO,CAACE,QAAS;gBACxBuE,QAAQ,EAAGC,CAAC,IAAK1B,kBAAkB,CAAC,UAAU,EAAE0B,CAAC,CAACC,MAAM,CAACzB,KAAK,CAAE;gBAChEe,SAAS,EAAC,kGAAkG;gBAAAC,QAAA,gBAE5GjF,OAAA;kBAAQiE,KAAK,EAAC,KAAK;kBAAAgB,QAAA,EAAC;gBAAqB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EACjD1B,MAAM,CAACiC,OAAO,CAAC5G,kBAAkB,CAAC,CAAC6G,GAAG,CAAC,CAAC,CAACC,GAAG,EAAE7E,QAAQ,CAAC,kBACtDjB,OAAA;kBAAkBiE,KAAK,EAAE6B,GAAI;kBAAAb,QAAA,EAAEhE,QAAQ,CAAC8E;gBAAI,GAA/BD,GAAG;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAqC,CACtD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENrF,OAAA;cAAAiF,QAAA,gBACEjF,OAAA;gBAAOgF,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACzFrF,OAAA;gBACEiE,KAAK,EAAElD,OAAO,CAACG,WAAY;gBAC3BsE,QAAQ,EAAGC,CAAC,IAAK1B,kBAAkB,CAAC,aAAa,EAAE0B,CAAC,CAACC,MAAM,CAACzB,KAAK,CAAE;gBACnEe,SAAS,EAAC,kGAAkG;gBAAAC,QAAA,gBAE5GjF,OAAA;kBAAQiE,KAAK,EAAC,KAAK;kBAAAgB,QAAA,EAAC;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EAC5C1B,MAAM,CAACiC,OAAO,CAAC3G,YAAY,CAAC,CAAC4G,GAAG,CAAC,CAAC,CAACC,GAAG,EAAEE,KAAK,CAAC,kBAC7ChG,OAAA;kBAAkBiE,KAAK,EAAE6B,GAAI;kBAAAb,QAAA,EAAEe,KAAK,CAACD;gBAAI,GAA5BD,GAAG;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAkC,CACnD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENrF,OAAA;cAAAiF,QAAA,gBACEjF,OAAA;gBAAOgF,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAClFrF,OAAA;gBACEiE,KAAK,EAAElD,OAAO,CAACI,SAAU;gBACzBqE,QAAQ,EAAGC,CAAC,IAAK1B,kBAAkB,CAAC,WAAW,EAAE0B,CAAC,CAACC,MAAM,CAACzB,KAAK,CAAE;gBACjEe,SAAS,EAAC,kGAAkG;gBAAAC,QAAA,gBAE5GjF,OAAA;kBAAQiE,KAAK,EAAC,KAAK;kBAAAgB,QAAA,EAAC;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EAC3C1B,MAAM,CAACiC,OAAO,CAAC1G,cAAc,CAAC,CAAC2G,GAAG,CAAC,CAAC,CAACC,GAAG,EAAE3E,SAAS,CAAC,kBACnDnB,OAAA;kBAAkBiE,KAAK,EAAE6B,GAAI;kBAAAb,QAAA,EAAE9D,SAAS,CAAC4E;gBAAI,GAAhCD,GAAG;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAsC,CACvD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENrF,OAAA;cAAAiF,QAAA,gBACEjF,OAAA;gBAAOgF,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAClFrF,OAAA;gBACEiE,KAAK,EAAElD,OAAO,CAACK,UAAW;gBAC1BoE,QAAQ,EAAGC,CAAC,IAAK1B,kBAAkB,CAAC,YAAY,EAAE0B,CAAC,CAACC,MAAM,CAACzB,KAAK,CAAE;gBAClEe,SAAS,EAAC,kGAAkG;gBAAAC,QAAA,gBAE5GjF,OAAA;kBAAQiE,KAAK,EAAC,KAAK;kBAAAgB,QAAA,EAAC;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC/CrF,OAAA;kBAAQiE,KAAK,EAAC,QAAQ;kBAAAgB,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtCrF,OAAA;kBAAQiE,KAAK,EAAC,cAAQ;kBAAAgB,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtCrF,OAAA;kBAAQiE,KAAK,EAAC,WAAW;kBAAAgB,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENrF,OAAA;YAAKgF,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACrDjF,OAAA;cAAKgF,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1CjF,OAAA;gBAAOgF,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACnFrF,OAAA;gBACEsF,IAAI,EAAC,QAAQ;gBACbrB,KAAK,EAAElD,OAAO,CAACM,WAAW,IAAI,EAAG;gBACjCmE,QAAQ,EAAGC,CAAC,IAAK1B,kBAAkB,CAAC,aAAa,EAAE0B,CAAC,CAACC,MAAM,CAACzB,KAAK,GAAGgC,QAAQ,CAACR,CAAC,CAACC,MAAM,CAACzB,KAAK,CAAC,GAAG,IAAI,CAAE;gBACrGsB,WAAW,EAAC,IAAI;gBAChBP,SAAS,EAAC;cAAgG;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3G,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNrF,OAAA;cACE2F,OAAO,EAAExB,YAAa;cACtBa,SAAS,EAAC,+DAA+D;cAAAC,QAAA,EAC1E;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNrF,OAAA;QAAKgF,SAAS,EAAC,gDAAgD;QAAAC,QAAA,gBAC7DjF,OAAA;UACE2F,OAAO,EAAEA,CAAA,KAAMpE,YAAY,CAAC,KAAK,CAAE;UACnCyD,SAAS,EAAE,qEACT1D,SAAS,KAAK,KAAK,GACf,kCAAkC,GAClC,mCAAmC,EACtC;UAAA2D,QAAA,GACJ,QACO,EAAClG,SAAS,CAAC4F,MAAM,EAAC,GAC1B;QAAA;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EACR7E,IAAI,iBACHR,OAAA,CAAAE,SAAA;UAAA+E,QAAA,gBACEjF,OAAA;YACE2F,OAAO,EAAEA,CAAA,KAAMpE,YAAY,CAAC,aAAa,CAAE;YAC3CyD,SAAS,EAAE,qEACT1D,SAAS,KAAK,aAAa,GACvB,kCAAkC,GAClC,mCAAmC,EACtC;YAAA2D,QAAA,gBAEHjF,OAAA,CAAC7B,MAAM;cAAC6G,SAAS,EAAC;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,oBACrB,EAAC7D,oBAAoB,CAACmD,MAAM,EAAC,GAC5C;UAAA;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTrF,OAAA;YACE2F,OAAO,EAAEA,CAAA,KAAMpE,YAAY,CAAC,WAAW,CAAE;YACzCyD,SAAS,EAAE,qEACT1D,SAAS,KAAK,WAAW,GACrB,kCAAkC,GAClC,mCAAmC,EACtC;YAAA2D,QAAA,gBAEHjF,OAAA,CAAC9B,OAAO;cAAC8G,SAAS,EAAC;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,aAC1B,EAAC3D,iBAAiB,CAACiD,MAAM,EAAC,GACrC;UAAA;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA,eACT,CACH;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNrF,OAAA;QAAKgF,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnBjF,OAAA;UAAGgF,SAAS,EAAC,eAAe;UAAAC,QAAA,GACzBrD,iBAAiB,CAAC+C,MAAM,EAAC,WAAS,EAAC/C,iBAAiB,CAAC+C,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAC,YAAO,EAAC/C,iBAAiB,CAAC+C,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAC1HhE,WAAW,IAAI,UAAUA,WAAW,GAAG;QAAA;UAAAuE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGNrF,OAAA;QAAKgF,SAAS,EAAC,2DAA2D;QAAAC,QAAA,EACvErD,iBAAiB,CAACiE,GAAG,CAACf,QAAQ,IAAI;UAAA,IAAAoB,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA;UACjC,MAAMxB,UAAU,GAAGnD,iBAAiB,CAACmC,IAAI,CAACyC,GAAG,IAAIA,GAAG,CAAC9C,EAAE,KAAKsB,QAAQ,CAACtB,EAAE,CAAC;UAExE,oBACExD,OAAA;YAEEgF,SAAS,EAAC,iFAAiF;YAAAC,QAAA,eAE3FjF,OAAA;cAAKgF,SAAS,EAAC,KAAK;cAAAC,QAAA,gBAClBjF,OAAA;gBAAKgF,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,gBACpDjF,OAAA;kBAAKgF,SAAS,EAAC,QAAQ;kBAAAC,QAAA,gBACrBjF,OAAA;oBAAIgF,SAAS,EAAC,0CAA0C;oBAAAC,QAAA,EACrDH,QAAQ,CAACiB;kBAAI;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACZ,CAAC,eACLrF,OAAA;oBAAKgF,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,gBAC/CjF,OAAA;sBAAMgF,SAAS,EAAE,8CAA8C7F,kBAAkB,CAAC2F,QAAQ,CAAC1D,UAAU,CAAC,EAAG;sBAAA6D,QAAA,EACtGH,QAAQ,CAAC1D;oBAAU;sBAAA8D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChB,CAAC,eACPrF,OAAA;sBAAMgF,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,IAAAiB,qBAAA,GACpClH,kBAAkB,CAAC8F,QAAQ,CAAC7D,QAAQ,CAAC,cAAAiF,qBAAA,uBAArCA,qBAAA,CAAuCK,IAAI,EAAC,GAAC,GAAAJ,sBAAA,GAACnH,kBAAkB,CAAC8F,QAAQ,CAAC7D,QAAQ,CAAC,cAAAkF,sBAAA,uBAArCA,sBAAA,CAAuCJ,IAAI;oBAAA;sBAAAb,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNrF,OAAA;kBACE2F,OAAO,EAAGF,CAAC,IAAK;oBACdA,CAAC,CAACe,eAAe,CAAC,CAAC;oBACnBjH,cAAc,CAACuF,QAAQ,CAACtB,EAAE,CAAC;kBAC7B,CAAE;kBACFwB,SAAS,EAAE,sCACTH,UAAU,GACN,iCAAiC,GACjC,kCAAkC,EACrC;kBAAAI,QAAA,eAEHjF,OAAA,CAAC9B,OAAO;oBAAC8G,SAAS,EAAE,WAAWH,UAAU,GAAG,cAAc,GAAG,EAAE;kBAAG;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eAENrF,OAAA;gBAAGgF,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,EAC3CH,QAAQ,CAAC2B;cAAW;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC,eAGJrF,OAAA;gBAAKgF,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EACvCH,QAAQ,CAAC7F,YAAY,CAAC4G,GAAG,CAACa,EAAE;kBAAA,IAAAC,gBAAA;kBAAA,oBAC3B3G,OAAA;oBAAegF,SAAS,EAAC,0DAA0D;oBAAAC,QAAA,GAAA0B,gBAAA,GAChF1H,YAAY,CAACyH,EAAE,CAAC,cAAAC,gBAAA,uBAAhBA,gBAAA,CAAkBZ;kBAAI,GADdW,EAAE;oBAAAxB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEP,CAAC;gBAAA,CACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAGNrF,OAAA;gBAAKgF,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,gBAC3DjF,OAAA;kBAAMgF,SAAS,EAAC,MAAM;kBAAAC,QAAA,GAAAmB,qBAAA,GAAElH,cAAc,CAAC4F,QAAQ,CAAC3D,SAAS,CAAC,cAAAiF,qBAAA,uBAAlCA,qBAAA,CAAoCG;gBAAI;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACxErF,OAAA;kBAAAiF,QAAA,GAAAoB,sBAAA,GAAOnH,cAAc,CAAC4F,QAAQ,CAAC3D,SAAS,CAAC,cAAAkF,sBAAA,uBAAlCA,sBAAA,CAAoCN;gBAAI;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC,eAENrF,OAAA;gBAAKgF,SAAS,EAAC,8DAA8D;gBAAAC,QAAA,gBAC3EjF,OAAA;kBAAKgF,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChCjF,OAAA,CAACnC,OAAO;oBAACmH,SAAS,EAAC;kBAAc;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EACnCP,QAAQ,CAAC/B,QAAQ,EAAC,MACrB;gBAAA;kBAAAmC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNrF,OAAA;kBAAKgF,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChCjF,OAAA,CAAClC,KAAK;oBAACkH,SAAS,EAAC;kBAAc;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EACjCP,QAAQ,CAAC8B,QAAQ,EAAC,MACrB;gBAAA;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENrF,OAAA;gBAAKgF,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjF,OAAA;kBACE2F,OAAO,EAAEA,CAAA,KAAMjF,mBAAmB,CAACoE,QAAQ,CAAE;kBAC7CE,SAAS,EAAC,oIAAoI;kBAAAC,QAAA,gBAE9IjF,OAAA,CAAC5B,MAAM;oBAAC4G,SAAS,EAAC;kBAAc;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,cAErC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTrF,OAAA;kBAAQgF,SAAS,EAAC,iIAAiI;kBAAAC,QAAA,gBACjJjF,OAAA,CAACpC,MAAM;oBAACoH,SAAS,EAAC;kBAAc;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,aAErC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC,GA5EDP,QAAQ,CAACtB,EAAE;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA6Eb,CAAC;QAEV,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,EAGLzD,iBAAiB,CAAC+C,MAAM,KAAK,CAAC,iBAC7B3E,OAAA;QAAKgF,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCjF,OAAA;UAAKgF,SAAS,EAAC,oBAAoB;UAAAC,QAAA,eACjCjF,OAAA,CAAChC,QAAQ;YAACgH,SAAS,EAAC;UAAmB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC,eACNrF,OAAA;UAAIgF,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAEvD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLrF,OAAA;UAAGgF,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAElC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJrF,OAAA;UACE2F,OAAO,EAAExB,YAAa;UACtBa,SAAS,EAAC,iFAAiF;UAAAC,QAAA,EAC5F;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN,EAGA5E,gBAAgB,iBACfT,OAAA;QAAKgF,SAAS,EAAC,gFAAgF;QAAAC,QAAA,eAC7FjF,OAAA;UAAKgF,SAAS,EAAC,mEAAmE;UAAAC,QAAA,eAChFjF,OAAA;YAAKgF,SAAS,EAAC,KAAK;YAAAC,QAAA,gBAClBjF,OAAA;cAAKgF,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrDjF,OAAA;gBAAAiF,QAAA,gBACEjF,OAAA;kBAAIgF,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,EAClDxE,gBAAgB,CAACsF;gBAAI;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB,CAAC,eACLrF,OAAA;kBAAKgF,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1CjF,OAAA;oBAAMgF,SAAS,EAAE,8CAA8C7F,kBAAkB,CAACsB,gBAAgB,CAACW,UAAU,CAAC,EAAG;oBAAA6D,QAAA,EAC9GxE,gBAAgB,CAACW;kBAAU;oBAAA8D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB,CAAC,eACPrF,OAAA;oBAAMgF,SAAS,EAAC,eAAe;oBAAAC,QAAA,IAAA5E,qBAAA,GAC5BrB,kBAAkB,CAACyB,gBAAgB,CAACQ,QAAQ,CAAC,cAAAZ,qBAAA,uBAA7CA,qBAAA,CAA+CkG,IAAI,EAAC,GAAC,GAAAjG,sBAAA,GAACtB,kBAAkB,CAACyB,gBAAgB,CAACQ,QAAQ,CAAC,cAAAX,sBAAA,uBAA7CA,sBAAA,CAA+CyF,IAAI;kBAAA;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNrF,OAAA;gBACE2F,OAAO,EAAEA,CAAA,KAAMjF,mBAAmB,CAAC,IAAI,CAAE;gBACzCsE,SAAS,EAAC,4CAA4C;gBAAAC,QAAA,EACvD;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENrF,OAAA;cAAKgF,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBAEpDjF,OAAA;gBAAKgF,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5BjF,OAAA;kBAAKgF,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBACnBjF,OAAA;oBAAIgF,SAAS,EAAC,0CAA0C;oBAAAC,QAAA,EAAC;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACzErF,OAAA;oBAAGgF,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,EAAExE,gBAAgB,CAACgG;kBAAW;oBAAAvB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5E,CAAC,eAENrF,OAAA;kBAAKgF,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBACnBjF,OAAA;oBAAIgF,SAAS,EAAC,0CAA0C;oBAAAC,QAAA,EAAC;kBAAuB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACrFrF,OAAA;oBAAIgF,SAAS,EAAC,oCAAoC;oBAAAC,QAAA,EAC/CxE,gBAAgB,CAACoG,YAAY,CAAChB,GAAG,CAAC,CAACiB,WAAW,EAAEC,KAAK,kBACpD/G,OAAA;sBAAgBgF,SAAS,EAAC,oCAAoC;sBAAAC,QAAA,EAC3D6B;oBAAW,GADLC,KAAK;sBAAA7B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEV,CACL;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,EAEL5E,gBAAgB,CAACuG,IAAI,iBACpBhH,OAAA;kBAAKgF,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBACnBjF,OAAA;oBAAIgF,SAAS,EAAC,0CAA0C;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACtErF,OAAA;oBAAIgF,SAAS,EAAC,WAAW;oBAAAC,QAAA,EACtBxE,gBAAgB,CAACuG,IAAI,CAACnB,GAAG,CAAC,CAACoB,GAAG,EAAEF,KAAK,kBACpC/G,OAAA;sBAAgBgF,SAAS,EAAC,gCAAgC;sBAAAC,QAAA,gBACxDjF,OAAA;wBAAMgF,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,EAAC;sBAAC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,EACjD4B,GAAG;oBAAA,GAFGF,KAAK;sBAAA7B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAGV,CACL;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CACN,EAEA5E,gBAAgB,CAACyG,UAAU,iBAC1BlH,OAAA;kBAAKgF,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBACnBjF,OAAA;oBAAIgF,SAAS,EAAC,0CAA0C;oBAAAC,QAAA,EAAC;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACxErF,OAAA;oBAAIgF,SAAS,EAAC,WAAW;oBAAAC,QAAA,EACtBxE,gBAAgB,CAACyG,UAAU,CAACrB,GAAG,CAAC,CAACsB,SAAS,EAAEJ,KAAK,kBAChD/G,OAAA;sBAAgBgF,SAAS,EAAC,gCAAgC;sBAAAC,QAAA,gBACxDjF,OAAA;wBAAMgF,SAAS,EAAC,0BAA0B;wBAAAC,QAAA,EAAC;sBAAC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,EAClD8B,SAAS;oBAAA,GAFHJ,KAAK;sBAAA7B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAGV,CACL;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAGNrF,OAAA;gBAAKgF,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5BjF,OAAA;kBAAKgF,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,gBAC7CjF,OAAA;oBAAIgF,SAAS,EAAC,0CAA0C;oBAAAC,QAAA,EAAC;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAE1ErF,OAAA;oBAAKgF,SAAS,EAAC,WAAW;oBAAAC,QAAA,gBACxBjF,OAAA;sBAAKgF,SAAS,EAAC,sBAAsB;sBAAAC,QAAA,gBACnCjF,OAAA;wBAAMgF,SAAS,EAAC,eAAe;wBAAAC,QAAA,EAAC;sBAAK;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAC5CrF,OAAA;wBAAMgF,SAAS,EAAC,aAAa;wBAAAC,QAAA,GAAExE,gBAAgB,CAACsC,QAAQ,EAAC,MAAI;sBAAA;wBAAAmC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjE,CAAC,eACNrF,OAAA;sBAAKgF,SAAS,EAAC,sBAAsB;sBAAAC,QAAA,gBACnCjF,OAAA;wBAAMgF,SAAS,EAAC,eAAe;wBAAAC,QAAA,EAAC;sBAAQ;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAC/CrF,OAAA;wBAAMgF,SAAS,EAAC,aAAa;wBAAAC,QAAA,GAAExE,gBAAgB,CAACmG,QAAQ,EAAC,MAAI;sBAAA;wBAAA1B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjE,CAAC,eACNrF,OAAA;sBAAKgF,SAAS,EAAC,sBAAsB;sBAAAC,QAAA,gBACnCjF,OAAA;wBAAMgF,SAAS,EAAC,eAAe;wBAAAC,QAAA,EAAC;sBAAU;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACjDrF,OAAA;wBAAMgF,SAAS,EAAC,aAAa;wBAAAC,QAAA,GAAA1E,qBAAA,GAAErB,cAAc,CAACuB,gBAAgB,CAACU,SAAS,CAAC,cAAAZ,qBAAA,uBAA1CA,qBAAA,CAA4CwF;sBAAI;wBAAAb,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENrF,OAAA;kBAAKgF,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,gBAC7CjF,OAAA;oBAAIgF,SAAS,EAAC,0CAA0C;oBAAAC,QAAA,EAAC;kBAAmB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACjFrF,OAAA;oBAAKgF,SAAS,EAAC,sBAAsB;oBAAAC,QAAA,EAClCxE,gBAAgB,CAACxB,YAAY,CAAC4G,GAAG,CAACa,EAAE;sBAAA,IAAAU,iBAAA,EAAAC,iBAAA;sBAAA,oBACnCrH,OAAA;wBAAegF,SAAS,EAAC,0DAA0D;wBAAAC,QAAA,IAAAmC,iBAAA,GAChFnI,YAAY,CAACyH,EAAE,CAAC,cAAAU,iBAAA,uBAAhBA,iBAAA,CAAkBb,IAAI,EAAC,GAAC,GAAAc,iBAAA,GAACpI,YAAY,CAACyH,EAAE,CAAC,cAAAW,iBAAA,uBAAhBA,iBAAA,CAAkBtB,IAAI;sBAAA,GADvCW,EAAE;wBAAAxB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAEP,CAAC;oBAAA,CACR;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENrF,OAAA;kBAAKgF,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBACxBjF,OAAA;oBACE2F,OAAO,EAAEA,CAAA,KAAMpG,cAAc,CAACkB,gBAAgB,CAAC+C,EAAE,CAAE;oBACnDwB,SAAS,EAAE,8FACTtD,iBAAiB,CAACmC,IAAI,CAACyC,GAAG,IAAIA,GAAG,CAAC9C,EAAE,KAAK/C,gBAAgB,CAAC+C,EAAE,CAAC,GACzD,0CAA0C,GAC1C,6CAA6C,EAChD;oBAAAyB,QAAA,gBAEHjF,OAAA,CAAC9B,OAAO;sBAAC8G,SAAS,EAAC;oBAAc;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EACnC3D,iBAAiB,CAACmC,IAAI,CAACyC,GAAG,IAAIA,GAAG,CAAC9C,EAAE,KAAK/C,gBAAgB,CAAC+C,EAAE,CAAC,GAAG,qBAAqB,GAAG,qBAAqB;kBAAA;oBAAA0B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxG,CAAC,eAETrF,OAAA;oBAAQgF,SAAS,EAAC,qIAAqI;oBAAAC,QAAA,gBACrJjF,OAAA,CAACpC,MAAM;sBAACoH,SAAS,EAAC;oBAAc;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,wBAErC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjF,EAAA,CA/gBID,SAAS;EAAA,QACIrB,OAAO;AAAA;AAAAwI,EAAA,GADpBnH,SAAS;AAihBf,eAAeA,SAAS;AAAC,IAAAmH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}