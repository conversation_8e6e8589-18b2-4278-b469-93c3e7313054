{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projet fitness\\\\DATA\\\\fitness-ui\\\\src\\\\pages\\\\Social.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { FiUsers, FiTrendingUp, FiAward, FiUserPlus, FiMessageCircle, FiTarget, FiClock, FiMapPin, FiCheck, FiX, FiSearch, FiPlus, FiEdit3, FiTrash2, FiShare2, FiHeart, FiThumbsUp } from 'react-icons/fi';\nimport { useAuth } from '../contexts/AuthContext';\nimport { generateMockUsers, generateChallenges, generateFriendships, generateFriendSuggestions, formatTime, formatPace } from '../utils/socialUtils';\nimport { generateSegments } from '../utils/mapUtils';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Social = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const [activeTab, setActiveTab] = useState('friends');\n  const [challenges, setChallenges] = useState([]);\n  const [segments, setSegments] = useState([]);\n  const [friendships, setFriendships] = useState([]);\n  const [friendSuggestions, setFriendSuggestions] = useState([]);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [userActivities, setUserActivities] = useState([]);\n  const [showCreateChallenge, setShowCreateChallenge] = useState(false);\n  const [newChallenge, setNewChallenge] = useState({\n    name: '',\n    description: '',\n    type: 'distance',\n    target: '',\n    duration: 7,\n    difficulty: 'modéré'\n  });\n  useEffect(() => {\n    // Charger les données sauvegardées\n    try {\n      const savedChallenges = JSON.parse(localStorage.getItem('socialChallenges') || '[]');\n      const savedActivities = JSON.parse(localStorage.getItem('userActivities') || '[]');\n      const savedFriendships = JSON.parse(localStorage.getItem('friendships') || '[]');\n      if (savedChallenges.length > 0) {\n        setChallenges(savedChallenges);\n      }\n      setUserActivities(savedActivities);\n      if (savedFriendships.length > 0) {\n        setFriendships(savedFriendships);\n      }\n    } catch (error) {\n      console.error('Erreur lors du chargement des données sociales:', error);\n    }\n\n    // Générer des données fictives si aucune donnée sauvegardée\n    const mockUsers = generateMockUsers(50);\n    const mockChallenges = generateChallenges(mockUsers);\n    const mockSegments = generateSegments([\n    // Quelques routes fictives pour générer des segments\n    {\n      name: 'Parcours du Parc',\n      points: Array.from({\n        length: 20\n      }, (_, i) => ({\n        lat: 48.8566 + i * 0.001,\n        lng: 2.3522 + i * 0.001,\n        elevation: 100 + Math.sin(i) * 50\n      })),\n      type: 'running'\n    }, {\n      name: 'Circuit de la Forêt',\n      points: Array.from({\n        length: 15\n      }, (_, i) => ({\n        lat: 48.8566 + i * 0.0015,\n        lng: 2.3522 - i * 0.0008,\n        elevation: 120 + Math.cos(i) * 40\n      })),\n      type: 'cycling'\n    }]);\n\n    // Utiliser les données fictives si pas de données sauvegardées\n    if (challenges.length === 0) {\n      setChallenges(mockChallenges);\n    }\n    setSegments(mockSegments);\n    if (user && friendships.length === 0) {\n      const userFriendships = generateFriendships(mockUsers, user.id);\n      const suggestions = generateFriendSuggestions(mockUsers, user.id, userFriendships);\n      setFriendships(userFriendships);\n      setFriendSuggestions(suggestions);\n    }\n  }, [user]);\n  const acceptedFriends = friendships.filter(f => f.status === 'accepted');\n  const pendingRequests = friendships.filter(f => f.status === 'pending');\n  const activeChallenges = challenges.filter(c => c.isActive);\n  const completedChallenges = challenges.filter(c => !c.isActive);\n  const handleAcceptFriend = friendshipId => {\n    setFriendships(prev => prev.map(f => f.id === friendshipId ? {\n      ...f,\n      status: 'accepted'\n    } : f));\n  };\n  const handleRejectFriend = friendshipId => {\n    setFriendships(prev => prev.filter(f => f.id !== friendshipId));\n  };\n  const handleJoinChallenge = challengeId => {\n    setChallenges(prev => prev.map(challenge => {\n      if (challenge.id === challengeId) {\n        const newParticipant = {\n          user: {\n            id: user.id,\n            firstName: user.firstName,\n            lastName: user.lastName,\n            avatar: user.avatar || `https://ui-avatars.com/api/?name=${user.firstName}+${user.lastName}`\n          },\n          progress: 0,\n          rank: challenge.participants.length + 1,\n          joinedAt: new Date(),\n          isCompleted: false,\n          lastActivity: new Date()\n        };\n        return {\n          ...challenge,\n          participants: [...challenge.participants, newParticipant]\n        };\n      }\n      return challenge;\n    }));\n  };\n  const filteredFriends = acceptedFriends.filter(friendship => friendship.user2.firstName.toLowerCase().includes(searchTerm.toLowerCase()) || friendship.user2.lastName.toLowerCase().includes(searchTerm.toLowerCase()));\n  const getDifficultyColor = difficulty => {\n    switch (difficulty) {\n      case 'facile':\n        return 'text-green-600 bg-green-100';\n      case 'modéré':\n        return 'text-yellow-600 bg-yellow-100';\n      case 'difficile':\n        return 'text-red-600 bg-red-100';\n      default:\n        return 'text-gray-600 bg-gray-100';\n    }\n  };\n  const getTypeIcon = type => {\n    switch (type) {\n      case 'running':\n        return '🏃‍♂️';\n      case 'cycling':\n        return '🚴‍♂️';\n      case 'hiking':\n        return '🥾';\n      case 'walking':\n        return '🚶‍♂️';\n      default:\n        return '📍';\n    }\n  };\n  const getChallengeIcon = type => {\n    switch (type) {\n      case 'distance':\n        return '📏';\n      case 'activities':\n        return '🏃‍♂️';\n      case 'time':\n        return '⏱️';\n      case 'streak':\n        return '🔥';\n      default:\n        return '🎯';\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold text-gray-900 mb-4\",\n          children: \"Communaut\\xE9 FitTracker\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg text-gray-600 max-w-3xl mx-auto\",\n          children: \"Connectez-vous avec d'autres athl\\xE8tes, participez \\xE0 des d\\xE9fis et d\\xE9couvrez les classements des segments populaires.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 189,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex space-x-1 bg-gray-100 p-1 rounded-lg mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setActiveTab('friends'),\n          className: `flex-1 py-3 px-4 rounded-md text-sm font-medium transition-colors ${activeTab === 'friends' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'}`,\n          children: [/*#__PURE__*/_jsxDEV(FiUsers, {\n            className: \"inline mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 13\n          }, this), \"Amis (\", acceptedFriends.length, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setActiveTab('challenges'),\n          className: `flex-1 py-3 px-4 rounded-md text-sm font-medium transition-colors ${activeTab === 'challenges' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'}`,\n          children: [/*#__PURE__*/_jsxDEV(FiTarget, {\n            className: \"inline mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 13\n          }, this), \"D\\xE9fis (\", activeChallenges.length, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setActiveTab('leaderboards'),\n          className: `flex-1 py-3 px-4 rounded-md text-sm font-medium transition-colors ${activeTab === 'leaderboards' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'}`,\n          children: [/*#__PURE__*/_jsxDEV(FiTrendingUp, {\n            className: \"inline mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 13\n          }, this), \"Classements\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 200,\n        columnNumber: 9\n      }, this), activeTab === 'friends' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:col-span-2\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-xl font-semibold text-gray-900\",\n                children: [\"Mes Amis (\", acceptedFriends.length, \")\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative\",\n                children: [/*#__PURE__*/_jsxDEV(FiSearch, {\n                  className: \"absolute left-3 top-3 h-4 w-4 text-gray-400\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 247,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  placeholder: \"Rechercher un ami...\",\n                  value: searchTerm,\n                  onChange: e => setSearchTerm(e.target.value),\n                  className: \"pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 248,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 246,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: filteredFriends.map(friendship => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    src: friendship.user2.avatar,\n                    alt: `${friendship.user2.firstName} ${friendship.user2.lastName}`,\n                    className: \"w-12 h-12 rounded-full\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 262,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"font-medium text-gray-900\",\n                      children: [friendship.user2.firstName, \" \", friendship.user2.lastName]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 268,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center space-x-4 text-sm text-gray-600\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"flex items-center\",\n                        children: [/*#__PURE__*/_jsxDEV(FiMapPin, {\n                          className: \"h-4 w-4 mr-1\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 273,\n                          columnNumber: 31\n                        }, this), friendship.user2.city]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 272,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"flex items-center\",\n                        children: [/*#__PURE__*/_jsxDEV(FiClock, {\n                          className: \"h-4 w-4 mr-1\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 277,\n                          columnNumber: 31\n                        }, this), friendship.user2.isOnline ? 'En ligne' : 'Hors ligne']\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 276,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 271,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-gray-500 mt-1\",\n                      children: [friendship.mutualFriends, \" amis en commun \\u2022 \", friendship.sharedActivities, \" activit\\xE9s partag\\xE9es\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 281,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 267,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 261,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"p-2 text-blue-600 hover:bg-blue-50 rounded-lg\",\n                    children: /*#__PURE__*/_jsxDEV(FiMessageCircle, {\n                      className: \"h-5 w-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 288,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 287,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-right\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm font-medium text-gray-900\",\n                      children: [friendship.user2.stats.totalDistance, \" km\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 291,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-gray-500\",\n                      children: [friendship.user2.stats.totalActivities, \" activit\\xE9s\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 294,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 290,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 286,\n                  columnNumber: 23\n                }, this)]\n              }, friendship.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 260,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-6\",\n          children: [pendingRequests.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-4\",\n              children: [\"Demandes d'amiti\\xE9 (\", pendingRequests.length, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3\",\n              children: pendingRequests.map(request => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between p-3 border border-gray-200 rounded-lg\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    src: request.user1.avatar,\n                    alt: `${request.user1.firstName} ${request.user1.lastName}`,\n                    className: \"w-10 h-10 rounded-full\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 317,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"font-medium text-gray-900 text-sm\",\n                      children: [request.user1.firstName, \" \", request.user1.lastName]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 323,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-xs text-gray-600\",\n                      children: request.user1.city\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 326,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 322,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 316,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex space-x-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleAcceptFriend(request.id),\n                    className: \"p-1 text-green-600 hover:bg-green-50 rounded\",\n                    children: /*#__PURE__*/_jsxDEV(FiCheck, {\n                      className: \"h-4 w-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 334,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 330,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleRejectFriend(request.id),\n                    className: \"p-1 text-red-600 hover:bg-red-50 rounded\",\n                    children: /*#__PURE__*/_jsxDEV(FiX, {\n                      className: \"h-4 w-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 340,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 336,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 329,\n                  columnNumber: 25\n                }, this)]\n              }, request.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 315,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 313,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-4\",\n              children: \"Suggestions d'amis\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 351,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3\",\n              children: friendSuggestions.slice(0, 5).map(suggestion => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between p-3 border border-gray-200 rounded-lg\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    src: suggestion.user.avatar,\n                    alt: `${suggestion.user.firstName} ${suggestion.user.lastName}`,\n                    className: \"w-10 h-10 rounded-full\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 358,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"font-medium text-gray-900 text-sm\",\n                      children: [suggestion.user.firstName, \" \", suggestion.user.lastName]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 364,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-xs text-gray-600\",\n                      children: suggestion.reason\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 367,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 363,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 357,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"p-1 text-blue-600 hover:bg-blue-50 rounded\",\n                  children: /*#__PURE__*/_jsxDEV(FiUserPlus, {\n                    className: \"h-4 w-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 371,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 370,\n                  columnNumber: 23\n                }, this)]\n              }, suggestion.user.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 356,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 354,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 350,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 306,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 238,\n        columnNumber: 11\n      }, this), activeTab === 'challenges' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:col-span-2\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-xl font-semibold text-gray-900 mb-6\",\n              children: [\"D\\xE9fis Actifs (\", activeChallenges.length, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 387,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid gap-6\",\n              children: activeChallenges.map(challenge => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-start justify-between mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-2xl\",\n                      children: getChallengeIcon(challenge.type)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 396,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"font-semibold text-gray-900\",\n                        children: challenge.name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 398,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm text-gray-600\",\n                        children: challenge.description\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 399,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 397,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 395,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `px-3 py-1 rounded-full text-xs font-medium ${challenge.category === 'team' ? 'bg-purple-100 text-purple-700' : 'bg-blue-100 text-blue-700'}`,\n                    children: challenge.category === 'team' ? 'Équipe' : 'Individuel'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 402,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 394,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid grid-cols-2 md:grid-cols-4 gap-4 mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-lg font-bold text-gray-900\",\n                      children: challenge.participants.length\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 411,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-gray-600\",\n                      children: \"Participants\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 412,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 410,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-lg font-bold text-gray-900\",\n                      children: challenge.target\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 415,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-gray-600\",\n                      children: challenge.unit\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 416,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 414,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-lg font-bold text-gray-900\",\n                      children: challenge.duration\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 419,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-gray-600\",\n                      children: \"jours\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 420,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 418,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-lg font-bold text-gray-900\",\n                      children: Math.ceil((challenge.endDate - new Date()) / (24 * 60 * 60 * 1000))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 423,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-gray-600\",\n                      children: \"jours restants\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 426,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 422,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 409,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm text-gray-600\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"Prix :\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 432,\n                      columnNumber: 27\n                    }, this), \" \", challenge.prize]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 431,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleJoinChallenge(challenge.id),\n                    className: \"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm\",\n                    children: \"Rejoindre\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 434,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 430,\n                  columnNumber: 23\n                }, this)]\n              }, challenge.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 393,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 391,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 386,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 385,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-4\",\n              children: \"Mes D\\xE9fis en Cours\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 450,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3\",\n              children: activeChallenges.slice(0, 3).map(challenge => {\n                const userParticipation = challenge.participants.find(p => p.user.id === (user === null || user === void 0 ? void 0 : user.id));\n                if (!userParticipation) return null;\n                const progressPercentage = userParticipation.progress / challenge.target * 100;\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"p-3 border border-gray-200 rounded-lg\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center justify-between mb-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"font-medium text-gray-900 text-sm\",\n                      children: challenge.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 463,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-xs text-gray-600\",\n                      children: [\"#\", userParticipation.rank]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 464,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 462,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full bg-gray-200 rounded-full h-2 mb-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-blue-600 h-2 rounded-full\",\n                      style: {\n                        width: `${Math.min(progressPercentage, 100)}%`\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 467,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 466,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-xs text-gray-600\",\n                    children: [userParticipation.progress, \" / \", challenge.target, \" \", challenge.unit]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 472,\n                    columnNumber: 25\n                  }, this)]\n                }, challenge.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 461,\n                  columnNumber: 23\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 453,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 449,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-4\",\n              children: \"D\\xE9fis Termin\\xE9s\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 482,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3\",\n              children: completedChallenges.slice(0, 3).map(challenge => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-3 border border-gray-200 rounded-lg\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"font-medium text-gray-900 text-sm\",\n                  children: challenge.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 488,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between mt-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-xs text-gray-600\",\n                    children: [challenge.participants.length, \" participants\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 490,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-xs text-green-600 font-medium\",\n                    children: \"Termin\\xE9\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 493,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 489,\n                  columnNumber: 23\n                }, this)]\n              }, challenge.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 487,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 485,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 481,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 448,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 383,\n        columnNumber: 11\n      }, this), activeTab === 'leaderboards' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:col-span-2\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-xl font-semibold text-gray-900 mb-6\",\n              children: \"Classements des Segments\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 509,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-6\",\n              children: segments.slice(0, 5).map(segment => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"border border-gray-200 rounded-lg p-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-start justify-between mb-4\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-xl\",\n                      children: getTypeIcon(segment.type)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 518,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"font-semibold text-gray-900\",\n                        children: segment.name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 520,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm text-gray-600\",\n                        children: segment.description\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 521,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center space-x-4 mt-1\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-xs text-gray-500\",\n                          children: [segment.distance.toFixed(1), \" km\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 523,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: `px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(segment.difficulty)}`,\n                          children: segment.difficulty\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 524,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-xs text-gray-500\",\n                          children: [segment.totalAttempts, \" tentatives\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 527,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 522,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 519,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 517,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 516,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"font-medium text-gray-900 text-sm mb-3\",\n                    children: \"Top 5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 534,\n                    columnNumber: 25\n                  }, this), segment.leaderboard.slice(0, 5).map((entry, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center justify-between p-2 rounded-lg hover:bg-gray-50\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center space-x-3\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: `w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold ${index === 0 ? 'bg-yellow-100 text-yellow-800' : index === 1 ? 'bg-gray-100 text-gray-800' : index === 2 ? 'bg-orange-100 text-orange-800' : 'bg-blue-100 text-blue-800'}`,\n                        children: entry.rank\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 538,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"font-medium text-gray-900 text-sm\",\n                          children: entry.athlete\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 547,\n                          columnNumber: 33\n                        }, this), entry.isLocalLegend && /*#__PURE__*/_jsxDEV(FiAward, {\n                          className: \"inline ml-2 h-4 w-4 text-yellow-500\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 549,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 546,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 537,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-right\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"font-medium text-gray-900 text-sm\",\n                        children: formatTime(entry.time)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 554,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-xs text-gray-500\",\n                        children: formatPace(entry.pace)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 557,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 553,\n                      columnNumber: 29\n                    }, this)]\n                  }, index, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 536,\n                    columnNumber: 27\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 533,\n                  columnNumber: 23\n                }, this)]\n              }, segment.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 515,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 513,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 508,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 507,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-4\",\n              children: \"Mes Performances\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 573,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between p-3 bg-yellow-50 rounded-lg\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(FiAward, {\n                    className: \"h-5 w-5 text-yellow-600\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 579,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-medium text-gray-900\",\n                    children: \"L\\xE9gendes Locales\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 580,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 578,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-lg font-bold text-yellow-600\",\n                  children: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 582,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 577,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between p-3 bg-blue-50 rounded-lg\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(FiTrendingUp, {\n                    className: \"h-5 w-5 text-blue-600\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 587,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-medium text-gray-900\",\n                    children: \"Top 10\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 588,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 586,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-lg font-bold text-blue-600\",\n                  children: \"8\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 590,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 585,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between p-3 bg-green-50 rounded-lg\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(FiAward, {\n                    className: \"h-5 w-5 text-green-600\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 595,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-medium text-gray-900\",\n                    children: \"Records Personnels\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 596,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 594,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-lg font-bold text-green-600\",\n                  children: \"15\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 598,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 593,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 576,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 572,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-4\",\n              children: \"Segments Populaires\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 604,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3\",\n              children: segments.slice(0, 5).map(segment => {\n                var _segment$leaderboard$;\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"font-medium text-gray-900 text-sm\",\n                      children: segment.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 611,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center space-x-2 text-xs text-gray-600\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        children: [segment.distance.toFixed(1), \" km\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 613,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"\\u2022\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 614,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: [segment.totalAttempts, \" tentatives\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 615,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 612,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 610,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-right\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm font-medium text-gray-900\",\n                      children: formatTime(((_segment$leaderboard$ = segment.leaderboard[0]) === null || _segment$leaderboard$ === void 0 ? void 0 : _segment$leaderboard$.time) || 0)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 619,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-gray-500\",\n                      children: \"Record\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 622,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 618,\n                    columnNumber: 23\n                  }, this)]\n                }, segment.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 609,\n                  columnNumber: 21\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 607,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 603,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 571,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 505,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 187,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 186,\n    columnNumber: 5\n  }, this);\n};\n_s(Social, \"0IvbzaJtPgLFneQD1wWFCK9yYKM=\", false, function () {\n  return [useAuth];\n});\n_c = Social;\nexport default Social;\nvar _c;\n$RefreshReg$(_c, \"Social\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "FiUsers", "FiTrendingUp", "FiAward", "FiUserPlus", "FiMessageCircle", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "FiMapPin", "<PERSON><PERSON><PERSON><PERSON>", "FiX", "FiSearch", "FiPlus", "FiEdit3", "FiTrash2", "FiShare2", "<PERSON><PERSON><PERSON><PERSON>", "FiThumbsUp", "useAuth", "generateMockUsers", "generateChallenges", "generateFriendships", "generateFriendSuggestions", "formatTime", "formatPace", "generateSegments", "jsxDEV", "_jsxDEV", "Social", "_s", "user", "activeTab", "setActiveTab", "challenges", "setChallenges", "segments", "setSegments", "friendships", "setFriendships", "friendSuggestions", "setFriendSuggestions", "searchTerm", "setSearchTerm", "userActivities", "setUserActivities", "showCreateChallenge", "setShowCreateChallenge", "newChallenge", "setNewChallenge", "name", "description", "type", "target", "duration", "difficulty", "savedChallenges", "JSON", "parse", "localStorage", "getItem", "savedActivities", "savedFriendships", "length", "error", "console", "mockUsers", "mockChallenges", "mockSegments", "points", "Array", "from", "_", "i", "lat", "lng", "elevation", "Math", "sin", "cos", "userFriendships", "id", "suggestions", "acceptedFriends", "filter", "f", "status", "pendingRequests", "activeChallenges", "c", "isActive", "completedChallenges", "handleAcceptFriend", "friendshipId", "prev", "map", "handleRejectFriend", "handleJoinChallenge", "challengeId", "challenge", "newParticipant", "firstName", "lastName", "avatar", "progress", "rank", "participants", "joinedAt", "Date", "isCompleted", "lastActivity", "filteredFriends", "friendship", "user2", "toLowerCase", "includes", "getDifficultyColor", "getTypeIcon", "getChallengeIcon", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "placeholder", "value", "onChange", "e", "src", "alt", "city", "isOnline", "mutualFriends", "sharedActivities", "stats", "totalDistance", "totalActivities", "request", "user1", "slice", "suggestion", "reason", "category", "unit", "ceil", "endDate", "prize", "userParticipation", "find", "p", "progressPercentage", "style", "width", "min", "segment", "distance", "toFixed", "totalAttempts", "leaderboard", "entry", "index", "athlete", "isLocalLegend", "time", "pace", "_segment$leaderboard$", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/projet fitness/DATA/fitness-ui/src/pages/Social.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  FiUsers,\n  FiTrendingUp,\n  FiAward,\n  FiUserPlus,\n  FiMessageCircle,\n  FiTarget,\n  FiClock,\n  FiMapPin,\n  FiCheck,\n  FiX,\n  FiSearch,\n  FiPlus,\n  FiEdit3,\n  FiTrash2,\n  FiShare2,\n  FiHeart,\n  FiThumbsUp\n} from 'react-icons/fi';\nimport { useAuth } from '../contexts/AuthContext';\nimport {\n  generateMockUsers,\n  generateChallenges,\n  generateFriendships,\n  generateFriendSuggestions,\n  formatTime,\n  formatPace\n} from '../utils/socialUtils';\nimport { generateSegments } from '../utils/mapUtils';\n\nconst Social = () => {\n  const { user } = useAuth();\n  const [activeTab, setActiveTab] = useState('friends');\n\n  const [challenges, setChallenges] = useState([]);\n  const [segments, setSegments] = useState([]);\n  const [friendships, setFriendships] = useState([]);\n  const [friendSuggestions, setFriendSuggestions] = useState([]);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [userActivities, setUserActivities] = useState([]);\n  const [showCreateChallenge, setShowCreateChallenge] = useState(false);\n  const [newChallenge, setNewChallenge] = useState({\n    name: '',\n    description: '',\n    type: 'distance',\n    target: '',\n    duration: 7,\n    difficulty: 'modéré'\n  });\n\n\n  useEffect(() => {\n    // Charger les données sauvegardées\n    try {\n      const savedChallenges = JSON.parse(localStorage.getItem('socialChallenges') || '[]');\n      const savedActivities = JSON.parse(localStorage.getItem('userActivities') || '[]');\n      const savedFriendships = JSON.parse(localStorage.getItem('friendships') || '[]');\n\n      if (savedChallenges.length > 0) {\n        setChallenges(savedChallenges);\n      }\n      setUserActivities(savedActivities);\n      if (savedFriendships.length > 0) {\n        setFriendships(savedFriendships);\n      }\n    } catch (error) {\n      console.error('Erreur lors du chargement des données sociales:', error);\n    }\n\n    // Générer des données fictives si aucune donnée sauvegardée\n    const mockUsers = generateMockUsers(50);\n    const mockChallenges = generateChallenges(mockUsers);\n    const mockSegments = generateSegments([\n      // Quelques routes fictives pour générer des segments\n      {\n        name: 'Parcours du Parc',\n        points: Array.from({ length: 20 }, (_, i) => ({\n          lat: 48.8566 + (i * 0.001),\n          lng: 2.3522 + (i * 0.001),\n          elevation: 100 + Math.sin(i) * 50\n        })),\n        type: 'running'\n      },\n      {\n        name: 'Circuit de la Forêt',\n        points: Array.from({ length: 15 }, (_, i) => ({\n          lat: 48.8566 + (i * 0.0015),\n          lng: 2.3522 - (i * 0.0008),\n          elevation: 120 + Math.cos(i) * 40\n        })),\n        type: 'cycling'\n      }\n    ]);\n\n    // Utiliser les données fictives si pas de données sauvegardées\n    if (challenges.length === 0) {\n      setChallenges(mockChallenges);\n    }\n    setSegments(mockSegments);\n\n    if (user && friendships.length === 0) {\n      const userFriendships = generateFriendships(mockUsers, user.id);\n      const suggestions = generateFriendSuggestions(mockUsers, user.id, userFriendships);\n      setFriendships(userFriendships);\n      setFriendSuggestions(suggestions);\n    }\n  }, [user]);\n\n  const acceptedFriends = friendships.filter(f => f.status === 'accepted');\n  const pendingRequests = friendships.filter(f => f.status === 'pending');\n  const activeChallenges = challenges.filter(c => c.isActive);\n  const completedChallenges = challenges.filter(c => !c.isActive);\n\n  const handleAcceptFriend = (friendshipId) => {\n    setFriendships(prev => prev.map(f => \n      f.id === friendshipId ? { ...f, status: 'accepted' } : f\n    ));\n  };\n\n  const handleRejectFriend = (friendshipId) => {\n    setFriendships(prev => prev.filter(f => f.id !== friendshipId));\n  };\n\n  const handleJoinChallenge = (challengeId) => {\n    setChallenges(prev => prev.map(challenge => {\n      if (challenge.id === challengeId) {\n        const newParticipant = {\n          user: {\n            id: user.id,\n            firstName: user.firstName,\n            lastName: user.lastName,\n            avatar: user.avatar || `https://ui-avatars.com/api/?name=${user.firstName}+${user.lastName}`\n          },\n          progress: 0,\n          rank: challenge.participants.length + 1,\n          joinedAt: new Date(),\n          isCompleted: false,\n          lastActivity: new Date()\n        };\n        \n        return {\n          ...challenge,\n          participants: [...challenge.participants, newParticipant]\n        };\n      }\n      return challenge;\n    }));\n  };\n\n  const filteredFriends = acceptedFriends.filter(friendship =>\n    friendship.user2.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    friendship.user2.lastName.toLowerCase().includes(searchTerm.toLowerCase())\n  );\n\n  const getDifficultyColor = (difficulty) => {\n    switch (difficulty) {\n      case 'facile': return 'text-green-600 bg-green-100';\n      case 'modéré': return 'text-yellow-600 bg-yellow-100';\n      case 'difficile': return 'text-red-600 bg-red-100';\n      default: return 'text-gray-600 bg-gray-100';\n    }\n  };\n\n  const getTypeIcon = (type) => {\n    switch (type) {\n      case 'running': return '🏃‍♂️';\n      case 'cycling': return '🚴‍♂️';\n      case 'hiking': return '🥾';\n      case 'walking': return '🚶‍♂️';\n      default: return '📍';\n    }\n  };\n\n  const getChallengeIcon = (type) => {\n    switch (type) {\n      case 'distance': return '📏';\n      case 'activities': return '🏃‍♂️';\n      case 'time': return '⏱️';\n      case 'streak': return '🔥';\n      default: return '🎯';\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Header */}\n        <div className=\"text-center mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-4\">\n            Communauté FitTracker\n          </h1>\n          <p className=\"text-lg text-gray-600 max-w-3xl mx-auto\">\n            Connectez-vous avec d'autres athlètes, participez à des défis et \n            découvrez les classements des segments populaires.\n          </p>\n        </div>\n\n        {/* Tabs */}\n        <div className=\"flex space-x-1 bg-gray-100 p-1 rounded-lg mb-8\">\n          <button\n            onClick={() => setActiveTab('friends')}\n            className={`flex-1 py-3 px-4 rounded-md text-sm font-medium transition-colors ${\n              activeTab === 'friends'\n                ? 'bg-white text-blue-600 shadow-sm'\n                : 'text-gray-600 hover:text-gray-900'\n            }`}\n          >\n            <FiUsers className=\"inline mr-2\" />\n            Amis ({acceptedFriends.length})\n          </button>\n          <button\n            onClick={() => setActiveTab('challenges')}\n            className={`flex-1 py-3 px-4 rounded-md text-sm font-medium transition-colors ${\n              activeTab === 'challenges'\n                ? 'bg-white text-blue-600 shadow-sm'\n                : 'text-gray-600 hover:text-gray-900'\n            }`}\n          >\n            <FiTarget className=\"inline mr-2\" />\n            Défis ({activeChallenges.length})\n          </button>\n          <button\n            onClick={() => setActiveTab('leaderboards')}\n            className={`flex-1 py-3 px-4 rounded-md text-sm font-medium transition-colors ${\n              activeTab === 'leaderboards'\n                ? 'bg-white text-blue-600 shadow-sm'\n                : 'text-gray-600 hover:text-gray-900'\n            }`}\n          >\n            <FiTrendingUp className=\"inline mr-2\" />\n            Classements\n          </button>\n        </div>\n\n        {/* Friends Tab */}\n        {activeTab === 'friends' && (\n          <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n            {/* Friends List */}\n            <div className=\"lg:col-span-2\">\n              <div className=\"bg-white rounded-lg shadow-lg p-6\">\n                <div className=\"flex items-center justify-between mb-6\">\n                  <h2 className=\"text-xl font-semibold text-gray-900\">\n                    Mes Amis ({acceptedFriends.length})\n                  </h2>\n                  <div className=\"relative\">\n                    <FiSearch className=\"absolute left-3 top-3 h-4 w-4 text-gray-400\" />\n                    <input\n                      type=\"text\"\n                      placeholder=\"Rechercher un ami...\"\n                      value={searchTerm}\n                      onChange={(e) => setSearchTerm(e.target.value)}\n                      className=\"pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    />\n                  </div>\n                </div>\n\n                <div className=\"space-y-4\">\n                  {filteredFriends.map(friendship => (\n                    <div key={friendship.id} className=\"flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50\">\n                      <div className=\"flex items-center space-x-4\">\n                        <img\n                          src={friendship.user2.avatar}\n                          alt={`${friendship.user2.firstName} ${friendship.user2.lastName}`}\n                          className=\"w-12 h-12 rounded-full\"\n                        />\n                        <div>\n                          <h3 className=\"font-medium text-gray-900\">\n                            {friendship.user2.firstName} {friendship.user2.lastName}\n                          </h3>\n                          <div className=\"flex items-center space-x-4 text-sm text-gray-600\">\n                            <span className=\"flex items-center\">\n                              <FiMapPin className=\"h-4 w-4 mr-1\" />\n                              {friendship.user2.city}\n                            </span>\n                            <span className=\"flex items-center\">\n                              <FiClock className=\"h-4 w-4 mr-1\" />\n                              {friendship.user2.isOnline ? 'En ligne' : 'Hors ligne'}\n                            </span>\n                          </div>\n                          <div className=\"text-xs text-gray-500 mt-1\">\n                            {friendship.mutualFriends} amis en commun • {friendship.sharedActivities} activités partagées\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"flex items-center space-x-2\">\n                        <button className=\"p-2 text-blue-600 hover:bg-blue-50 rounded-lg\">\n                          <FiMessageCircle className=\"h-5 w-5\" />\n                        </button>\n                        <div className=\"text-right\">\n                          <div className=\"text-sm font-medium text-gray-900\">\n                            {friendship.user2.stats.totalDistance} km\n                          </div>\n                          <div className=\"text-xs text-gray-500\">\n                            {friendship.user2.stats.totalActivities} activités\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </div>\n\n            {/* Sidebar */}\n            <div className=\"space-y-6\">\n              {/* Pending Requests */}\n              {pendingRequests.length > 0 && (\n                <div className=\"bg-white rounded-lg shadow-lg p-6\">\n                  <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">\n                    Demandes d'amitié ({pendingRequests.length})\n                  </h3>\n                  <div className=\"space-y-3\">\n                    {pendingRequests.map(request => (\n                      <div key={request.id} className=\"flex items-center justify-between p-3 border border-gray-200 rounded-lg\">\n                        <div className=\"flex items-center space-x-3\">\n                          <img\n                            src={request.user1.avatar}\n                            alt={`${request.user1.firstName} ${request.user1.lastName}`}\n                            className=\"w-10 h-10 rounded-full\"\n                          />\n                          <div>\n                            <h4 className=\"font-medium text-gray-900 text-sm\">\n                              {request.user1.firstName} {request.user1.lastName}\n                            </h4>\n                            <p className=\"text-xs text-gray-600\">{request.user1.city}</p>\n                          </div>\n                        </div>\n                        <div className=\"flex space-x-1\">\n                          <button\n                            onClick={() => handleAcceptFriend(request.id)}\n                            className=\"p-1 text-green-600 hover:bg-green-50 rounded\"\n                          >\n                            <FiCheck className=\"h-4 w-4\" />\n                          </button>\n                          <button\n                            onClick={() => handleRejectFriend(request.id)}\n                            className=\"p-1 text-red-600 hover:bg-red-50 rounded\"\n                          >\n                            <FiX className=\"h-4 w-4\" />\n                          </button>\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              )}\n\n              {/* Friend Suggestions */}\n              <div className=\"bg-white rounded-lg shadow-lg p-6\">\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">\n                  Suggestions d'amis\n                </h3>\n                <div className=\"space-y-3\">\n                  {friendSuggestions.slice(0, 5).map(suggestion => (\n                    <div key={suggestion.user.id} className=\"flex items-center justify-between p-3 border border-gray-200 rounded-lg\">\n                      <div className=\"flex items-center space-x-3\">\n                        <img\n                          src={suggestion.user.avatar}\n                          alt={`${suggestion.user.firstName} ${suggestion.user.lastName}`}\n                          className=\"w-10 h-10 rounded-full\"\n                        />\n                        <div>\n                          <h4 className=\"font-medium text-gray-900 text-sm\">\n                            {suggestion.user.firstName} {suggestion.user.lastName}\n                          </h4>\n                          <p className=\"text-xs text-gray-600\">{suggestion.reason}</p>\n                        </div>\n                      </div>\n                      <button className=\"p-1 text-blue-600 hover:bg-blue-50 rounded\">\n                        <FiUserPlus className=\"h-4 w-4\" />\n                      </button>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Challenges Tab */}\n        {activeTab === 'challenges' && (\n          <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n            {/* Active Challenges */}\n            <div className=\"lg:col-span-2\">\n              <div className=\"bg-white rounded-lg shadow-lg p-6\">\n                <h2 className=\"text-xl font-semibold text-gray-900 mb-6\">\n                  Défis Actifs ({activeChallenges.length})\n                </h2>\n                \n                <div className=\"grid gap-6\">\n                  {activeChallenges.map(challenge => (\n                    <div key={challenge.id} className=\"border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow\">\n                      <div className=\"flex items-start justify-between mb-4\">\n                        <div className=\"flex items-center space-x-3\">\n                          <span className=\"text-2xl\">{getChallengeIcon(challenge.type)}</span>\n                          <div>\n                            <h3 className=\"font-semibold text-gray-900\">{challenge.name}</h3>\n                            <p className=\"text-sm text-gray-600\">{challenge.description}</p>\n                          </div>\n                        </div>\n                        <span className={`px-3 py-1 rounded-full text-xs font-medium ${\n                          challenge.category === 'team' ? 'bg-purple-100 text-purple-700' : 'bg-blue-100 text-blue-700'\n                        }`}>\n                          {challenge.category === 'team' ? 'Équipe' : 'Individuel'}\n                        </span>\n                      </div>\n                      \n                      <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 mb-4\">\n                        <div className=\"text-center\">\n                          <div className=\"text-lg font-bold text-gray-900\">{challenge.participants.length}</div>\n                          <div className=\"text-xs text-gray-600\">Participants</div>\n                        </div>\n                        <div className=\"text-center\">\n                          <div className=\"text-lg font-bold text-gray-900\">{challenge.target}</div>\n                          <div className=\"text-xs text-gray-600\">{challenge.unit}</div>\n                        </div>\n                        <div className=\"text-center\">\n                          <div className=\"text-lg font-bold text-gray-900\">{challenge.duration}</div>\n                          <div className=\"text-xs text-gray-600\">jours</div>\n                        </div>\n                        <div className=\"text-center\">\n                          <div className=\"text-lg font-bold text-gray-900\">\n                            {Math.ceil((challenge.endDate - new Date()) / (24 * 60 * 60 * 1000))}\n                          </div>\n                          <div className=\"text-xs text-gray-600\">jours restants</div>\n                        </div>\n                      </div>\n                      \n                      <div className=\"flex items-center justify-between\">\n                        <div className=\"text-sm text-gray-600\">\n                          <span className=\"font-medium\">Prix :</span> {challenge.prize}\n                        </div>\n                        <button\n                          onClick={() => handleJoinChallenge(challenge.id)}\n                          className=\"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm\"\n                        >\n                          Rejoindre\n                        </button>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </div>\n\n            {/* Challenge Details Sidebar */}\n            <div className=\"space-y-6\">\n              <div className=\"bg-white rounded-lg shadow-lg p-6\">\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">\n                  Mes Défis en Cours\n                </h3>\n                <div className=\"space-y-3\">\n                  {activeChallenges.slice(0, 3).map(challenge => {\n                    const userParticipation = challenge.participants.find(p => p.user.id === user?.id);\n                    if (!userParticipation) return null;\n                    \n                    const progressPercentage = (userParticipation.progress / challenge.target) * 100;\n                    \n                    return (\n                      <div key={challenge.id} className=\"p-3 border border-gray-200 rounded-lg\">\n                        <div className=\"flex items-center justify-between mb-2\">\n                          <h4 className=\"font-medium text-gray-900 text-sm\">{challenge.name}</h4>\n                          <span className=\"text-xs text-gray-600\">#{userParticipation.rank}</span>\n                        </div>\n                        <div className=\"w-full bg-gray-200 rounded-full h-2 mb-2\">\n                          <div\n                            className=\"bg-blue-600 h-2 rounded-full\"\n                            style={{ width: `${Math.min(progressPercentage, 100)}%` }}\n                          ></div>\n                        </div>\n                        <div className=\"text-xs text-gray-600\">\n                          {userParticipation.progress} / {challenge.target} {challenge.unit}\n                        </div>\n                      </div>\n                    );\n                  })}\n                </div>\n              </div>\n\n              <div className=\"bg-white rounded-lg shadow-lg p-6\">\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">\n                  Défis Terminés\n                </h3>\n                <div className=\"space-y-3\">\n                  {completedChallenges.slice(0, 3).map(challenge => (\n                    <div key={challenge.id} className=\"p-3 border border-gray-200 rounded-lg\">\n                      <h4 className=\"font-medium text-gray-900 text-sm\">{challenge.name}</h4>\n                      <div className=\"flex items-center justify-between mt-2\">\n                        <span className=\"text-xs text-gray-600\">\n                          {challenge.participants.length} participants\n                        </span>\n                        <span className=\"text-xs text-green-600 font-medium\">Terminé</span>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Leaderboards Tab */}\n        {activeTab === 'leaderboards' && (\n          <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n            {/* Segments Leaderboards */}\n            <div className=\"lg:col-span-2\">\n              <div className=\"bg-white rounded-lg shadow-lg p-6\">\n                <h2 className=\"text-xl font-semibold text-gray-900 mb-6\">\n                  Classements des Segments\n                </h2>\n                \n                <div className=\"space-y-6\">\n                  {segments.slice(0, 5).map(segment => (\n                    <div key={segment.id} className=\"border border-gray-200 rounded-lg p-6\">\n                      <div className=\"flex items-start justify-between mb-4\">\n                        <div className=\"flex items-center space-x-3\">\n                          <span className=\"text-xl\">{getTypeIcon(segment.type)}</span>\n                          <div>\n                            <h3 className=\"font-semibold text-gray-900\">{segment.name}</h3>\n                            <p className=\"text-sm text-gray-600\">{segment.description}</p>\n                            <div className=\"flex items-center space-x-4 mt-1\">\n                              <span className=\"text-xs text-gray-500\">{segment.distance.toFixed(1)} km</span>\n                              <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(segment.difficulty)}`}>\n                                {segment.difficulty}\n                              </span>\n                              <span className=\"text-xs text-gray-500\">{segment.totalAttempts} tentatives</span>\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                      \n                      <div className=\"space-y-2\">\n                        <h4 className=\"font-medium text-gray-900 text-sm mb-3\">Top 5</h4>\n                        {segment.leaderboard.slice(0, 5).map((entry, index) => (\n                          <div key={index} className=\"flex items-center justify-between p-2 rounded-lg hover:bg-gray-50\">\n                            <div className=\"flex items-center space-x-3\">\n                              <span className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold ${\n                                index === 0 ? 'bg-yellow-100 text-yellow-800' :\n                                index === 1 ? 'bg-gray-100 text-gray-800' :\n                                index === 2 ? 'bg-orange-100 text-orange-800' :\n                                'bg-blue-100 text-blue-800'\n                              }`}>\n                                {entry.rank}\n                              </span>\n                              <div>\n                                <span className=\"font-medium text-gray-900 text-sm\">{entry.athlete}</span>\n                                {entry.isLocalLegend && (\n                                  <FiAward className=\"inline ml-2 h-4 w-4 text-yellow-500\" />\n                                )}\n                              </div>\n                            </div>\n                            <div className=\"text-right\">\n                              <div className=\"font-medium text-gray-900 text-sm\">\n                                {formatTime(entry.time)}\n                              </div>\n                              <div className=\"text-xs text-gray-500\">\n                                {formatPace(entry.pace)}\n                              </div>\n                            </div>\n                          </div>\n                        ))}\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </div>\n\n            {/* Leaderboard Stats */}\n            <div className=\"space-y-6\">\n              <div className=\"bg-white rounded-lg shadow-lg p-6\">\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">\n                  Mes Performances\n                </h3>\n                <div className=\"space-y-4\">\n                  <div className=\"flex items-center justify-between p-3 bg-yellow-50 rounded-lg\">\n                    <div className=\"flex items-center space-x-2\">\n                      <FiAward className=\"h-5 w-5 text-yellow-600\" />\n                      <span className=\"font-medium text-gray-900\">Légendes Locales</span>\n                    </div>\n                    <span className=\"text-lg font-bold text-yellow-600\">2</span>\n                  </div>\n                  \n                  <div className=\"flex items-center justify-between p-3 bg-blue-50 rounded-lg\">\n                    <div className=\"flex items-center space-x-2\">\n                      <FiTrendingUp className=\"h-5 w-5 text-blue-600\" />\n                      <span className=\"font-medium text-gray-900\">Top 10</span>\n                    </div>\n                    <span className=\"text-lg font-bold text-blue-600\">8</span>\n                  </div>\n                  \n                  <div className=\"flex items-center justify-between p-3 bg-green-50 rounded-lg\">\n                    <div className=\"flex items-center space-x-2\">\n                      <FiAward className=\"h-5 w-5 text-green-600\" />\n                      <span className=\"font-medium text-gray-900\">Records Personnels</span>\n                    </div>\n                    <span className=\"text-lg font-bold text-green-600\">15</span>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"bg-white rounded-lg shadow-lg p-6\">\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">\n                  Segments Populaires\n                </h3>\n                <div className=\"space-y-3\">\n                  {segments.slice(0, 5).map(segment => (\n                    <div key={segment.id} className=\"flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer\">\n                      <div>\n                        <h4 className=\"font-medium text-gray-900 text-sm\">{segment.name}</h4>\n                        <div className=\"flex items-center space-x-2 text-xs text-gray-600\">\n                          <span>{segment.distance.toFixed(1)} km</span>\n                          <span>•</span>\n                          <span>{segment.totalAttempts} tentatives</span>\n                        </div>\n                      </div>\n                      <div className=\"text-right\">\n                        <div className=\"text-sm font-medium text-gray-900\">\n                          {formatTime(segment.leaderboard[0]?.time || 0)}\n                        </div>\n                        <div className=\"text-xs text-gray-500\">Record</div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default Social;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,OAAO,EACPC,YAAY,EACZC,OAAO,EACPC,UAAU,EACVC,eAAe,EACfC,QAAQ,EACRC,OAAO,EACPC,QAAQ,EACRC,OAAO,EACPC,GAAG,EACHC,QAAQ,EACRC,MAAM,EACNC,OAAO,EACPC,QAAQ,EACRC,QAAQ,EACRC,OAAO,EACPC,UAAU,QACL,gBAAgB;AACvB,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SACEC,iBAAiB,EACjBC,kBAAkB,EAClBC,mBAAmB,EACnBC,yBAAyB,EACzBC,UAAU,EACVC,UAAU,QACL,sBAAsB;AAC7B,SAASC,gBAAgB,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,MAAMC,MAAM,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnB,MAAM;IAAEC;EAAK,CAAC,GAAGZ,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACa,SAAS,EAAEC,YAAY,CAAC,GAAGjC,QAAQ,CAAC,SAAS,CAAC;EAErD,MAAM,CAACkC,UAAU,EAAEC,aAAa,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACoC,QAAQ,EAAEC,WAAW,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACsC,WAAW,EAAEC,cAAc,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACwC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAAC0C,UAAU,EAAEC,aAAa,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC4C,cAAc,EAAEC,iBAAiB,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC8C,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACgD,YAAY,EAAEC,eAAe,CAAC,GAAGjD,QAAQ,CAAC;IAC/CkD,IAAI,EAAE,EAAE;IACRC,WAAW,EAAE,EAAE;IACfC,IAAI,EAAE,UAAU;IAChBC,MAAM,EAAE,EAAE;IACVC,QAAQ,EAAE,CAAC;IACXC,UAAU,EAAE;EACd,CAAC,CAAC;EAGFtD,SAAS,CAAC,MAAM;IACd;IACA,IAAI;MACF,MAAMuD,eAAe,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,kBAAkB,CAAC,IAAI,IAAI,CAAC;MACpF,MAAMC,eAAe,GAAGJ,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC,IAAI,IAAI,CAAC;MAClF,MAAME,gBAAgB,GAAGL,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC;MAEhF,IAAIJ,eAAe,CAACO,MAAM,GAAG,CAAC,EAAE;QAC9B5B,aAAa,CAACqB,eAAe,CAAC;MAChC;MACAX,iBAAiB,CAACgB,eAAe,CAAC;MAClC,IAAIC,gBAAgB,CAACC,MAAM,GAAG,CAAC,EAAE;QAC/BxB,cAAc,CAACuB,gBAAgB,CAAC;MAClC;IACF,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iDAAiD,EAAEA,KAAK,CAAC;IACzE;;IAEA;IACA,MAAME,SAAS,GAAG9C,iBAAiB,CAAC,EAAE,CAAC;IACvC,MAAM+C,cAAc,GAAG9C,kBAAkB,CAAC6C,SAAS,CAAC;IACpD,MAAME,YAAY,GAAG1C,gBAAgB,CAAC;IACpC;IACA;MACEwB,IAAI,EAAE,kBAAkB;MACxBmB,MAAM,EAAEC,KAAK,CAACC,IAAI,CAAC;QAAER,MAAM,EAAE;MAAG,CAAC,EAAE,CAACS,CAAC,EAAEC,CAAC,MAAM;QAC5CC,GAAG,EAAE,OAAO,GAAID,CAAC,GAAG,KAAM;QAC1BE,GAAG,EAAE,MAAM,GAAIF,CAAC,GAAG,KAAM;QACzBG,SAAS,EAAE,GAAG,GAAGC,IAAI,CAACC,GAAG,CAACL,CAAC,CAAC,GAAG;MACjC,CAAC,CAAC,CAAC;MACHrB,IAAI,EAAE;IACR,CAAC,EACD;MACEF,IAAI,EAAE,qBAAqB;MAC3BmB,MAAM,EAAEC,KAAK,CAACC,IAAI,CAAC;QAAER,MAAM,EAAE;MAAG,CAAC,EAAE,CAACS,CAAC,EAAEC,CAAC,MAAM;QAC5CC,GAAG,EAAE,OAAO,GAAID,CAAC,GAAG,MAAO;QAC3BE,GAAG,EAAE,MAAM,GAAIF,CAAC,GAAG,MAAO;QAC1BG,SAAS,EAAE,GAAG,GAAGC,IAAI,CAACE,GAAG,CAACN,CAAC,CAAC,GAAG;MACjC,CAAC,CAAC,CAAC;MACHrB,IAAI,EAAE;IACR,CAAC,CACF,CAAC;;IAEF;IACA,IAAIlB,UAAU,CAAC6B,MAAM,KAAK,CAAC,EAAE;MAC3B5B,aAAa,CAACgC,cAAc,CAAC;IAC/B;IACA9B,WAAW,CAAC+B,YAAY,CAAC;IAEzB,IAAIrC,IAAI,IAAIO,WAAW,CAACyB,MAAM,KAAK,CAAC,EAAE;MACpC,MAAMiB,eAAe,GAAG1D,mBAAmB,CAAC4C,SAAS,EAAEnC,IAAI,CAACkD,EAAE,CAAC;MAC/D,MAAMC,WAAW,GAAG3D,yBAAyB,CAAC2C,SAAS,EAAEnC,IAAI,CAACkD,EAAE,EAAED,eAAe,CAAC;MAClFzC,cAAc,CAACyC,eAAe,CAAC;MAC/BvC,oBAAoB,CAACyC,WAAW,CAAC;IACnC;EACF,CAAC,EAAE,CAACnD,IAAI,CAAC,CAAC;EAEV,MAAMoD,eAAe,GAAG7C,WAAW,CAAC8C,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAK,UAAU,CAAC;EACxE,MAAMC,eAAe,GAAGjD,WAAW,CAAC8C,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAK,SAAS,CAAC;EACvE,MAAME,gBAAgB,GAAGtD,UAAU,CAACkD,MAAM,CAACK,CAAC,IAAIA,CAAC,CAACC,QAAQ,CAAC;EAC3D,MAAMC,mBAAmB,GAAGzD,UAAU,CAACkD,MAAM,CAACK,CAAC,IAAI,CAACA,CAAC,CAACC,QAAQ,CAAC;EAE/D,MAAME,kBAAkB,GAAIC,YAAY,IAAK;IAC3CtD,cAAc,CAACuD,IAAI,IAAIA,IAAI,CAACC,GAAG,CAACV,CAAC,IAC/BA,CAAC,CAACJ,EAAE,KAAKY,YAAY,GAAG;MAAE,GAAGR,CAAC;MAAEC,MAAM,EAAE;IAAW,CAAC,GAAGD,CACzD,CAAC,CAAC;EACJ,CAAC;EAED,MAAMW,kBAAkB,GAAIH,YAAY,IAAK;IAC3CtD,cAAc,CAACuD,IAAI,IAAIA,IAAI,CAACV,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACJ,EAAE,KAAKY,YAAY,CAAC,CAAC;EACjE,CAAC;EAED,MAAMI,mBAAmB,GAAIC,WAAW,IAAK;IAC3C/D,aAAa,CAAC2D,IAAI,IAAIA,IAAI,CAACC,GAAG,CAACI,SAAS,IAAI;MAC1C,IAAIA,SAAS,CAAClB,EAAE,KAAKiB,WAAW,EAAE;QAChC,MAAME,cAAc,GAAG;UACrBrE,IAAI,EAAE;YACJkD,EAAE,EAAElD,IAAI,CAACkD,EAAE;YACXoB,SAAS,EAAEtE,IAAI,CAACsE,SAAS;YACzBC,QAAQ,EAAEvE,IAAI,CAACuE,QAAQ;YACvBC,MAAM,EAAExE,IAAI,CAACwE,MAAM,IAAI,oCAAoCxE,IAAI,CAACsE,SAAS,IAAItE,IAAI,CAACuE,QAAQ;UAC5F,CAAC;UACDE,QAAQ,EAAE,CAAC;UACXC,IAAI,EAAEN,SAAS,CAACO,YAAY,CAAC3C,MAAM,GAAG,CAAC;UACvC4C,QAAQ,EAAE,IAAIC,IAAI,CAAC,CAAC;UACpBC,WAAW,EAAE,KAAK;UAClBC,YAAY,EAAE,IAAIF,IAAI,CAAC;QACzB,CAAC;QAED,OAAO;UACL,GAAGT,SAAS;UACZO,YAAY,EAAE,CAAC,GAAGP,SAAS,CAACO,YAAY,EAAEN,cAAc;QAC1D,CAAC;MACH;MACA,OAAOD,SAAS;IAClB,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMY,eAAe,GAAG5B,eAAe,CAACC,MAAM,CAAC4B,UAAU,IACvDA,UAAU,CAACC,KAAK,CAACZ,SAAS,CAACa,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACzE,UAAU,CAACwE,WAAW,CAAC,CAAC,CAAC,IAC3EF,UAAU,CAACC,KAAK,CAACX,QAAQ,CAACY,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACzE,UAAU,CAACwE,WAAW,CAAC,CAAC,CAC3E,CAAC;EAED,MAAME,kBAAkB,GAAI7D,UAAU,IAAK;IACzC,QAAQA,UAAU;MAChB,KAAK,QAAQ;QAAE,OAAO,6BAA6B;MACnD,KAAK,QAAQ;QAAE,OAAO,+BAA+B;MACrD,KAAK,WAAW;QAAE,OAAO,yBAAyB;MAClD;QAAS,OAAO,2BAA2B;IAC7C;EACF,CAAC;EAED,MAAM8D,WAAW,GAAIjE,IAAI,IAAK;IAC5B,QAAQA,IAAI;MACV,KAAK,SAAS;QAAE,OAAO,OAAO;MAC9B,KAAK,SAAS;QAAE,OAAO,OAAO;MAC9B,KAAK,QAAQ;QAAE,OAAO,IAAI;MAC1B,KAAK,SAAS;QAAE,OAAO,OAAO;MAC9B;QAAS,OAAO,IAAI;IACtB;EACF,CAAC;EAED,MAAMkE,gBAAgB,GAAIlE,IAAI,IAAK;IACjC,QAAQA,IAAI;MACV,KAAK,UAAU;QAAE,OAAO,IAAI;MAC5B,KAAK,YAAY;QAAE,OAAO,OAAO;MACjC,KAAK,MAAM;QAAE,OAAO,IAAI;MACxB,KAAK,QAAQ;QAAE,OAAO,IAAI;MAC1B;QAAS,OAAO,IAAI;IACtB;EACF,CAAC;EAED,oBACExB,OAAA;IAAK2F,SAAS,EAAC,yBAAyB;IAAAC,QAAA,eACtC5F,OAAA;MAAK2F,SAAS,EAAC,6CAA6C;MAAAC,QAAA,gBAE1D5F,OAAA;QAAK2F,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/B5F,OAAA;UAAI2F,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAEtD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLhG,OAAA;UAAG2F,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EAAC;QAGvD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGNhG,OAAA;QAAK2F,SAAS,EAAC,gDAAgD;QAAAC,QAAA,gBAC7D5F,OAAA;UACEiG,OAAO,EAAEA,CAAA,KAAM5F,YAAY,CAAC,SAAS,CAAE;UACvCsF,SAAS,EAAE,qEACTvF,SAAS,KAAK,SAAS,GACnB,kCAAkC,GAClC,mCAAmC,EACtC;UAAAwF,QAAA,gBAEH5F,OAAA,CAAC1B,OAAO;YAACqH,SAAS,EAAC;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,UAC7B,EAACzC,eAAe,CAACpB,MAAM,EAAC,GAChC;QAAA;UAAA0D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACThG,OAAA;UACEiG,OAAO,EAAEA,CAAA,KAAM5F,YAAY,CAAC,YAAY,CAAE;UAC1CsF,SAAS,EAAE,qEACTvF,SAAS,KAAK,YAAY,GACtB,kCAAkC,GAClC,mCAAmC,EACtC;UAAAwF,QAAA,gBAEH5F,OAAA,CAACrB,QAAQ;YAACgH,SAAS,EAAC;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,cAC7B,EAACpC,gBAAgB,CAACzB,MAAM,EAAC,GAClC;QAAA;UAAA0D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACThG,OAAA;UACEiG,OAAO,EAAEA,CAAA,KAAM5F,YAAY,CAAC,cAAc,CAAE;UAC5CsF,SAAS,EAAE,qEACTvF,SAAS,KAAK,cAAc,GACxB,kCAAkC,GAClC,mCAAmC,EACtC;UAAAwF,QAAA,gBAEH5F,OAAA,CAACzB,YAAY;YAACoH,SAAS,EAAC;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAE1C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAGL5F,SAAS,KAAK,SAAS,iBACtBJ,OAAA;QAAK2F,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBAEpD5F,OAAA;UAAK2F,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5B5F,OAAA;YAAK2F,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChD5F,OAAA;cAAK2F,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrD5F,OAAA;gBAAI2F,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,GAAC,YACxC,EAACrC,eAAe,CAACpB,MAAM,EAAC,GACpC;cAAA;gBAAA0D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLhG,OAAA;gBAAK2F,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACvB5F,OAAA,CAAChB,QAAQ;kBAAC2G,SAAS,EAAC;gBAA6C;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACpEhG,OAAA;kBACEwB,IAAI,EAAC,MAAM;kBACX0E,WAAW,EAAC,sBAAsB;kBAClCC,KAAK,EAAErF,UAAW;kBAClBsF,QAAQ,EAAGC,CAAC,IAAKtF,aAAa,CAACsF,CAAC,CAAC5E,MAAM,CAAC0E,KAAK,CAAE;kBAC/CR,SAAS,EAAC;gBAAuG;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENhG,OAAA;cAAK2F,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvBT,eAAe,CAAChB,GAAG,CAACiB,UAAU,iBAC7BpF,OAAA;gBAAyB2F,SAAS,EAAC,0FAA0F;gBAAAC,QAAA,gBAC3H5F,OAAA;kBAAK2F,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1C5F,OAAA;oBACEsG,GAAG,EAAElB,UAAU,CAACC,KAAK,CAACV,MAAO;oBAC7B4B,GAAG,EAAE,GAAGnB,UAAU,CAACC,KAAK,CAACZ,SAAS,IAAIW,UAAU,CAACC,KAAK,CAACX,QAAQ,EAAG;oBAClEiB,SAAS,EAAC;kBAAwB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC,CAAC,eACFhG,OAAA;oBAAA4F,QAAA,gBACE5F,OAAA;sBAAI2F,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,GACtCR,UAAU,CAACC,KAAK,CAACZ,SAAS,EAAC,GAAC,EAACW,UAAU,CAACC,KAAK,CAACX,QAAQ;oBAAA;sBAAAmB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrD,CAAC,eACLhG,OAAA;sBAAK2F,SAAS,EAAC,mDAAmD;sBAAAC,QAAA,gBAChE5F,OAAA;wBAAM2F,SAAS,EAAC,mBAAmB;wBAAAC,QAAA,gBACjC5F,OAAA,CAACnB,QAAQ;0BAAC8G,SAAS,EAAC;wBAAc;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,EACpCZ,UAAU,CAACC,KAAK,CAACmB,IAAI;sBAAA;wBAAAX,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClB,CAAC,eACPhG,OAAA;wBAAM2F,SAAS,EAAC,mBAAmB;wBAAAC,QAAA,gBACjC5F,OAAA,CAACpB,OAAO;0BAAC+G,SAAS,EAAC;wBAAc;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,EACnCZ,UAAU,CAACC,KAAK,CAACoB,QAAQ,GAAG,UAAU,GAAG,YAAY;sBAAA;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClD,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACNhG,OAAA;sBAAK2F,SAAS,EAAC,4BAA4B;sBAAAC,QAAA,GACxCR,UAAU,CAACsB,aAAa,EAAC,yBAAkB,EAACtB,UAAU,CAACuB,gBAAgB,EAAC,4BAC3E;oBAAA;sBAAAd,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNhG,OAAA;kBAAK2F,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1C5F,OAAA;oBAAQ2F,SAAS,EAAC,+CAA+C;oBAAAC,QAAA,eAC/D5F,OAAA,CAACtB,eAAe;sBAACiH,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjC,CAAC,eACThG,OAAA;oBAAK2F,SAAS,EAAC,YAAY;oBAAAC,QAAA,gBACzB5F,OAAA;sBAAK2F,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,GAC/CR,UAAU,CAACC,KAAK,CAACuB,KAAK,CAACC,aAAa,EAAC,KACxC;oBAAA;sBAAAhB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNhG,OAAA;sBAAK2F,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,GACnCR,UAAU,CAACC,KAAK,CAACuB,KAAK,CAACE,eAAe,EAAC,eAC1C;oBAAA;sBAAAjB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA,GAtCEZ,UAAU,CAAC/B,EAAE;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAuClB,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNhG,OAAA;UAAK2F,SAAS,EAAC,WAAW;UAAAC,QAAA,GAEvBjC,eAAe,CAACxB,MAAM,GAAG,CAAC,iBACzBnC,OAAA;YAAK2F,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChD5F,OAAA;cAAI2F,SAAS,EAAC,0CAA0C;cAAAC,QAAA,GAAC,wBACpC,EAACjC,eAAe,CAACxB,MAAM,EAAC,GAC7C;YAAA;cAAA0D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLhG,OAAA;cAAK2F,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvBjC,eAAe,CAACQ,GAAG,CAAC4C,OAAO,iBAC1B/G,OAAA;gBAAsB2F,SAAS,EAAC,yEAAyE;gBAAAC,QAAA,gBACvG5F,OAAA;kBAAK2F,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1C5F,OAAA;oBACEsG,GAAG,EAAES,OAAO,CAACC,KAAK,CAACrC,MAAO;oBAC1B4B,GAAG,EAAE,GAAGQ,OAAO,CAACC,KAAK,CAACvC,SAAS,IAAIsC,OAAO,CAACC,KAAK,CAACtC,QAAQ,EAAG;oBAC5DiB,SAAS,EAAC;kBAAwB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC,CAAC,eACFhG,OAAA;oBAAA4F,QAAA,gBACE5F,OAAA;sBAAI2F,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,GAC9CmB,OAAO,CAACC,KAAK,CAACvC,SAAS,EAAC,GAAC,EAACsC,OAAO,CAACC,KAAK,CAACtC,QAAQ;oBAAA;sBAAAmB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/C,CAAC,eACLhG,OAAA;sBAAG2F,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAEmB,OAAO,CAACC,KAAK,CAACR;oBAAI;sBAAAX,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1D,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNhG,OAAA;kBAAK2F,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC7B5F,OAAA;oBACEiG,OAAO,EAAEA,CAAA,KAAMjC,kBAAkB,CAAC+C,OAAO,CAAC1D,EAAE,CAAE;oBAC9CsC,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,eAExD5F,OAAA,CAAClB,OAAO;sBAAC6G,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzB,CAAC,eACThG,OAAA;oBACEiG,OAAO,EAAEA,CAAA,KAAM7B,kBAAkB,CAAC2C,OAAO,CAAC1D,EAAE,CAAE;oBAC9CsC,SAAS,EAAC,0CAA0C;oBAAAC,QAAA,eAEpD5F,OAAA,CAACjB,GAAG;sBAAC4G,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA,GA3BEe,OAAO,CAAC1D,EAAE;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA4Bf,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eAGDhG,OAAA;YAAK2F,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChD5F,OAAA;cAAI2F,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAEzD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLhG,OAAA;cAAK2F,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvBhF,iBAAiB,CAACqG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC9C,GAAG,CAAC+C,UAAU,iBAC3ClH,OAAA;gBAA8B2F,SAAS,EAAC,yEAAyE;gBAAAC,QAAA,gBAC/G5F,OAAA;kBAAK2F,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1C5F,OAAA;oBACEsG,GAAG,EAAEY,UAAU,CAAC/G,IAAI,CAACwE,MAAO;oBAC5B4B,GAAG,EAAE,GAAGW,UAAU,CAAC/G,IAAI,CAACsE,SAAS,IAAIyC,UAAU,CAAC/G,IAAI,CAACuE,QAAQ,EAAG;oBAChEiB,SAAS,EAAC;kBAAwB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC,CAAC,eACFhG,OAAA;oBAAA4F,QAAA,gBACE5F,OAAA;sBAAI2F,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,GAC9CsB,UAAU,CAAC/G,IAAI,CAACsE,SAAS,EAAC,GAAC,EAACyC,UAAU,CAAC/G,IAAI,CAACuE,QAAQ;oBAAA;sBAAAmB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnD,CAAC,eACLhG,OAAA;sBAAG2F,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAEsB,UAAU,CAACC;oBAAM;sBAAAtB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNhG,OAAA;kBAAQ2F,SAAS,EAAC,4CAA4C;kBAAAC,QAAA,eAC5D5F,OAAA,CAACvB,UAAU;oBAACkH,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B,CAAC;cAAA,GAhBDkB,UAAU,CAAC/G,IAAI,CAACkD,EAAE;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAiBvB,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA5F,SAAS,KAAK,YAAY,iBACzBJ,OAAA;QAAK2F,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBAEpD5F,OAAA;UAAK2F,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5B5F,OAAA;YAAK2F,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChD5F,OAAA;cAAI2F,SAAS,EAAC,0CAA0C;cAAAC,QAAA,GAAC,mBACzC,EAAChC,gBAAgB,CAACzB,MAAM,EAAC,GACzC;YAAA;cAAA0D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAELhG,OAAA;cAAK2F,SAAS,EAAC,YAAY;cAAAC,QAAA,EACxBhC,gBAAgB,CAACO,GAAG,CAACI,SAAS,iBAC7BvE,OAAA;gBAAwB2F,SAAS,EAAC,yEAAyE;gBAAAC,QAAA,gBACzG5F,OAAA;kBAAK2F,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,gBACpD5F,OAAA;oBAAK2F,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,gBAC1C5F,OAAA;sBAAM2F,SAAS,EAAC,UAAU;sBAAAC,QAAA,EAAEF,gBAAgB,CAACnB,SAAS,CAAC/C,IAAI;oBAAC;sBAAAqE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACpEhG,OAAA;sBAAA4F,QAAA,gBACE5F,OAAA;wBAAI2F,SAAS,EAAC,6BAA6B;wBAAAC,QAAA,EAAErB,SAAS,CAACjD;sBAAI;wBAAAuE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACjEhG,OAAA;wBAAG2F,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EAAErB,SAAS,CAAChD;sBAAW;wBAAAsE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7D,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNhG,OAAA;oBAAM2F,SAAS,EAAE,8CACfpB,SAAS,CAAC6C,QAAQ,KAAK,MAAM,GAAG,+BAA+B,GAAG,2BAA2B,EAC5F;oBAAAxB,QAAA,EACArB,SAAS,CAAC6C,QAAQ,KAAK,MAAM,GAAG,QAAQ,GAAG;kBAAY;oBAAAvB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eAENhG,OAAA;kBAAK2F,SAAS,EAAC,4CAA4C;kBAAAC,QAAA,gBACzD5F,OAAA;oBAAK2F,SAAS,EAAC,aAAa;oBAAAC,QAAA,gBAC1B5F,OAAA;sBAAK2F,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,EAAErB,SAAS,CAACO,YAAY,CAAC3C;oBAAM;sBAAA0D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACtFhG,OAAA;sBAAK2F,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAAY;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtD,CAAC,eACNhG,OAAA;oBAAK2F,SAAS,EAAC,aAAa;oBAAAC,QAAA,gBAC1B5F,OAAA;sBAAK2F,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,EAAErB,SAAS,CAAC9C;oBAAM;sBAAAoE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACzEhG,OAAA;sBAAK2F,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAErB,SAAS,CAAC8C;oBAAI;sBAAAxB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1D,CAAC,eACNhG,OAAA;oBAAK2F,SAAS,EAAC,aAAa;oBAAAC,QAAA,gBAC1B5F,OAAA;sBAAK2F,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,EAAErB,SAAS,CAAC7C;oBAAQ;sBAAAmE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC3EhG,OAAA;sBAAK2F,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAAK;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/C,CAAC,eACNhG,OAAA;oBAAK2F,SAAS,EAAC,aAAa;oBAAAC,QAAA,gBAC1B5F,OAAA;sBAAK2F,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,EAC7C3C,IAAI,CAACqE,IAAI,CAAC,CAAC/C,SAAS,CAACgD,OAAO,GAAG,IAAIvC,IAAI,CAAC,CAAC,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;oBAAC;sBAAAa,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjE,CAAC,eACNhG,OAAA;sBAAK2F,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAAc;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENhG,OAAA;kBAAK2F,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,gBAChD5F,OAAA;oBAAK2F,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,gBACpC5F,OAAA;sBAAM2F,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAM;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAACzB,SAAS,CAACiD,KAAK;kBAAA;oBAAA3B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzD,CAAC,eACNhG,OAAA;oBACEiG,OAAO,EAAEA,CAAA,KAAM5B,mBAAmB,CAACE,SAAS,CAAClB,EAAE,CAAE;oBACjDsC,SAAS,EAAC,yFAAyF;oBAAAC,QAAA,EACpG;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA,GA/CEzB,SAAS,CAAClB,EAAE;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAgDjB,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNhG,OAAA;UAAK2F,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB5F,OAAA;YAAK2F,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChD5F,OAAA;cAAI2F,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAEzD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLhG,OAAA;cAAK2F,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvBhC,gBAAgB,CAACqD,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC9C,GAAG,CAACI,SAAS,IAAI;gBAC7C,MAAMkD,iBAAiB,GAAGlD,SAAS,CAACO,YAAY,CAAC4C,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACxH,IAAI,CAACkD,EAAE,MAAKlD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkD,EAAE,EAAC;gBAClF,IAAI,CAACoE,iBAAiB,EAAE,OAAO,IAAI;gBAEnC,MAAMG,kBAAkB,GAAIH,iBAAiB,CAAC7C,QAAQ,GAAGL,SAAS,CAAC9C,MAAM,GAAI,GAAG;gBAEhF,oBACEzB,OAAA;kBAAwB2F,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,gBACvE5F,OAAA;oBAAK2F,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,gBACrD5F,OAAA;sBAAI2F,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAErB,SAAS,CAACjD;oBAAI;sBAAAuE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACvEhG,OAAA;sBAAM2F,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,GAAC,GAAC,EAAC6B,iBAAiB,CAAC5C,IAAI;oBAAA;sBAAAgB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrE,CAAC,eACNhG,OAAA;oBAAK2F,SAAS,EAAC,0CAA0C;oBAAAC,QAAA,eACvD5F,OAAA;sBACE2F,SAAS,EAAC,8BAA8B;sBACxCkC,KAAK,EAAE;wBAAEC,KAAK,EAAE,GAAG7E,IAAI,CAAC8E,GAAG,CAACH,kBAAkB,EAAE,GAAG,CAAC;sBAAI;oBAAE;sBAAA/B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACNhG,OAAA;oBAAK2F,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,GACnC6B,iBAAiB,CAAC7C,QAAQ,EAAC,KAAG,EAACL,SAAS,CAAC9C,MAAM,EAAC,GAAC,EAAC8C,SAAS,CAAC8C,IAAI;kBAAA;oBAAAxB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9D,CAAC;gBAAA,GAbEzB,SAAS,CAAClB,EAAE;kBAAAwC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAcjB,CAAC;cAEV,CAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENhG,OAAA;YAAK2F,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChD5F,OAAA;cAAI2F,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAEzD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLhG,OAAA;cAAK2F,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvB7B,mBAAmB,CAACkD,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC9C,GAAG,CAACI,SAAS,iBAC5CvE,OAAA;gBAAwB2F,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,gBACvE5F,OAAA;kBAAI2F,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAErB,SAAS,CAACjD;gBAAI;kBAAAuE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACvEhG,OAAA;kBAAK2F,SAAS,EAAC,wCAAwC;kBAAAC,QAAA,gBACrD5F,OAAA;oBAAM2F,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,GACpCrB,SAAS,CAACO,YAAY,CAAC3C,MAAM,EAAC,eACjC;kBAAA;oBAAA0D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACPhG,OAAA;oBAAM2F,SAAS,EAAC,oCAAoC;oBAAAC,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChE,CAAC;cAAA,GAPEzB,SAAS,CAAClB,EAAE;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAQjB,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA5F,SAAS,KAAK,cAAc,iBAC3BJ,OAAA;QAAK2F,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBAEpD5F,OAAA;UAAK2F,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5B5F,OAAA;YAAK2F,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChD5F,OAAA;cAAI2F,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAEzD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAELhG,OAAA;cAAK2F,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvBpF,QAAQ,CAACyG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC9C,GAAG,CAAC6D,OAAO,iBAC/BhI,OAAA;gBAAsB2F,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,gBACrE5F,OAAA;kBAAK2F,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,eACpD5F,OAAA;oBAAK2F,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,gBAC1C5F,OAAA;sBAAM2F,SAAS,EAAC,SAAS;sBAAAC,QAAA,EAAEH,WAAW,CAACuC,OAAO,CAACxG,IAAI;oBAAC;sBAAAqE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAC5DhG,OAAA;sBAAA4F,QAAA,gBACE5F,OAAA;wBAAI2F,SAAS,EAAC,6BAA6B;wBAAAC,QAAA,EAAEoC,OAAO,CAAC1G;sBAAI;wBAAAuE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAC/DhG,OAAA;wBAAG2F,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EAAEoC,OAAO,CAACzG;sBAAW;wBAAAsE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAC9DhG,OAAA;wBAAK2F,SAAS,EAAC,kCAAkC;wBAAAC,QAAA,gBAC/C5F,OAAA;0BAAM2F,SAAS,EAAC,uBAAuB;0BAAAC,QAAA,GAAEoC,OAAO,CAACC,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC,EAAC,KAAG;wBAAA;0BAAArC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eAC/EhG,OAAA;0BAAM2F,SAAS,EAAE,8CAA8CH,kBAAkB,CAACwC,OAAO,CAACrG,UAAU,CAAC,EAAG;0BAAAiE,QAAA,EACrGoC,OAAO,CAACrG;wBAAU;0BAAAkE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACf,CAAC,eACPhG,OAAA;0BAAM2F,SAAS,EAAC,uBAAuB;0BAAAC,QAAA,GAAEoC,OAAO,CAACG,aAAa,EAAC,aAAW;wBAAA;0BAAAtC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9E,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENhG,OAAA;kBAAK2F,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBACxB5F,OAAA;oBAAI2F,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EAChEgC,OAAO,CAACI,WAAW,CAACnB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC9C,GAAG,CAAC,CAACkE,KAAK,EAAEC,KAAK,kBAChDtI,OAAA;oBAAiB2F,SAAS,EAAC,mEAAmE;oBAAAC,QAAA,gBAC5F5F,OAAA;sBAAK2F,SAAS,EAAC,6BAA6B;sBAAAC,QAAA,gBAC1C5F,OAAA;wBAAM2F,SAAS,EAAE,2EACf2C,KAAK,KAAK,CAAC,GAAG,+BAA+B,GAC7CA,KAAK,KAAK,CAAC,GAAG,2BAA2B,GACzCA,KAAK,KAAK,CAAC,GAAG,+BAA+B,GAC7C,2BAA2B,EAC1B;wBAAA1C,QAAA,EACAyC,KAAK,CAACxD;sBAAI;wBAAAgB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACP,CAAC,eACPhG,OAAA;wBAAA4F,QAAA,gBACE5F,OAAA;0BAAM2F,SAAS,EAAC,mCAAmC;0BAAAC,QAAA,EAAEyC,KAAK,CAACE;wBAAO;0BAAA1C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC,EACzEqC,KAAK,CAACG,aAAa,iBAClBxI,OAAA,CAACxB,OAAO;0BAACmH,SAAS,EAAC;wBAAqC;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAC3D;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNhG,OAAA;sBAAK2F,SAAS,EAAC,YAAY;sBAAAC,QAAA,gBACzB5F,OAAA;wBAAK2F,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,EAC/ChG,UAAU,CAACyI,KAAK,CAACI,IAAI;sBAAC;wBAAA5C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpB,CAAC,eACNhG,OAAA;wBAAK2F,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EACnC/F,UAAU,CAACwI,KAAK,CAACK,IAAI;sBAAC;wBAAA7C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA,GAxBEsC,KAAK;oBAAAzC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAyBV,CACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA,GAhDEgC,OAAO,CAAC3E,EAAE;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAiDf,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNhG,OAAA;UAAK2F,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB5F,OAAA;YAAK2F,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChD5F,OAAA;cAAI2F,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAEzD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLhG,OAAA;cAAK2F,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxB5F,OAAA;gBAAK2F,SAAS,EAAC,+DAA+D;gBAAAC,QAAA,gBAC5E5F,OAAA;kBAAK2F,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1C5F,OAAA,CAACxB,OAAO;oBAACmH,SAAS,EAAC;kBAAyB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC/ChG,OAAA;oBAAM2F,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,EAAC;kBAAgB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChE,CAAC,eACNhG,OAAA;kBAAM2F,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC,eAENhG,OAAA;gBAAK2F,SAAS,EAAC,6DAA6D;gBAAAC,QAAA,gBAC1E5F,OAAA;kBAAK2F,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1C5F,OAAA,CAACzB,YAAY;oBAACoH,SAAS,EAAC;kBAAuB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAClDhG,OAAA;oBAAM2F,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD,CAAC,eACNhG,OAAA;kBAAM2F,SAAS,EAAC,iCAAiC;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD,CAAC,eAENhG,OAAA;gBAAK2F,SAAS,EAAC,8DAA8D;gBAAAC,QAAA,gBAC3E5F,OAAA;kBAAK2F,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1C5F,OAAA,CAACxB,OAAO;oBAACmH,SAAS,EAAC;kBAAwB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC9ChG,OAAA;oBAAM2F,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,EAAC;kBAAkB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClE,CAAC,eACNhG,OAAA;kBAAM2F,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENhG,OAAA;YAAK2F,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChD5F,OAAA;cAAI2F,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAEzD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLhG,OAAA;cAAK2F,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvBpF,QAAQ,CAACyG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC9C,GAAG,CAAC6D,OAAO;gBAAA,IAAAW,qBAAA;gBAAA,oBAC/B3I,OAAA;kBAAsB2F,SAAS,EAAC,yGAAyG;kBAAAC,QAAA,gBACvI5F,OAAA;oBAAA4F,QAAA,gBACE5F,OAAA;sBAAI2F,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAEoC,OAAO,CAAC1G;oBAAI;sBAAAuE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACrEhG,OAAA;sBAAK2F,SAAS,EAAC,mDAAmD;sBAAAC,QAAA,gBAChE5F,OAAA;wBAAA4F,QAAA,GAAOoC,OAAO,CAACC,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC,EAAC,KAAG;sBAAA;wBAAArC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAC7ChG,OAAA;wBAAA4F,QAAA,EAAM;sBAAC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACdhG,OAAA;wBAAA4F,QAAA,GAAOoC,OAAO,CAACG,aAAa,EAAC,aAAW;sBAAA;wBAAAtC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5C,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNhG,OAAA;oBAAK2F,SAAS,EAAC,YAAY;oBAAAC,QAAA,gBACzB5F,OAAA;sBAAK2F,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAC/ChG,UAAU,CAAC,EAAA+I,qBAAA,GAAAX,OAAO,CAACI,WAAW,CAAC,CAAC,CAAC,cAAAO,qBAAA,uBAAtBA,qBAAA,CAAwBF,IAAI,KAAI,CAAC;oBAAC;sBAAA5C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3C,CAAC,eACNhG,OAAA;sBAAK2F,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAAM;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChD,CAAC;gBAAA,GAdEgC,OAAO,CAAC3E,EAAE;kBAAAwC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAef,CAAC;cAAA,CACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC9F,EAAA,CA1lBID,MAAM;EAAA,QACOV,OAAO;AAAA;AAAAqJ,EAAA,GADpB3I,MAAM;AA4lBZ,eAAeA,MAAM;AAAC,IAAA2I,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}