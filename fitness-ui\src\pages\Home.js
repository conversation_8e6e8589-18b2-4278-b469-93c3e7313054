import React from 'react';
import { Link } from 'react-router-dom';
import { FiClock, FiActivity, FiBarChart2 } from 'react-icons/fi';

const Home = () => {
  const features = [
    {
      icon: <FiClock className="h-8 w-8 text-primary" />,
      title: 'Minuteur personnalisable',
      description: 'Configurez votre minuteur selon vos besoins avec des préréglages pour HIIT, Tabata, EMOM et plus encore.',
      link: '/timer'
    },
    {
      icon: <FiActivity className="h-8 w-8 text-primary" />,
      title: 'Programmes d\'entraînement',
      description: 'Accédez à une bibliothèque de programmes d\'entraînement catégorisés par type et niveau de difficulté.',
      link: '/programs'
    },
    {
      icon: <FiBarChart2 className="h-8 w-8 text-primary" />,
      title: 'Suivi de progression',
      description: 'Suivez votre progression avec des graphiques et des statistiques détaillées sur vos performances.',
      link: '/progress'
    }
  ];

  const testimonials = [
    {
      name: '<PERSON>',
      role: 'Sportive débutante',
      content: 'FitTracker m\'a aidé à structurer mes entraînements et à rester motivée. L\'interface est intuitive et les programmes sont parfaits pour mon niveau.',
      avatar: 'https://randomuser.me/api/portraits/women/1.jpg'
    },
    {
      name: 'Thomas L.',
      role: 'Coach sportif',
      content: 'J\'utilise FitTracker avec mes clients pour suivre leur progression. C\'est un outil complet qui répond parfaitement à mes besoins professionnels.',
      avatar: 'https://randomuser.me/api/portraits/men/1.jpg'
    },
    {
      name: 'Julie D.',
      role: 'Sportive avancée',
      content: 'Le minuteur HIIT est exactement ce dont j\'avais besoin pour mes entraînements intensifs. Je recommande FitTracker à tous les sportifs sérieux.',
      avatar: 'https://randomuser.me/api/portraits/women/2.jpg'
    }
  ];

  return (
    <div>
      {/* Hero section */}
      <section className="bg-gradient-to-r from-primary to-blue-700 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-extrabold mb-6">
              Suivez vos entraînements, atteignez vos objectifs
            </h1>
            <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto">
              FitTracker est votre compagnon d'entraînement personnel avec minuteur, programmes d'exercices et suivi de progression.
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <Link
                to="/timer"
                className="btn bg-secondary hover:bg-orange-600 text-white font-bold py-3 px-6 rounded-lg shadow-lg"
              >
                Essayer le minuteur
              </Link>
              <Link
                to="/programs"
                className="btn bg-white hover:bg-gray-100 text-primary font-bold py-3 px-6 rounded-lg shadow-lg"
              >
                Découvrir les programmes
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Features section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900">
              Fonctionnalités principales
            </h2>
            <p className="mt-4 text-xl text-gray-600">
              Tout ce dont vous avez besoin pour optimiser vos entraînements
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <div
                key={index}
                className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow"
              >
                <div className="mb-4">{feature.icon}</div>
                <h3 className="text-xl font-semibold mb-2">{feature.title}</h3>
                <p className="text-gray-600 mb-4">{feature.description}</p>
                <Link
                  to={feature.link}
                  className="text-primary hover:text-blue-700 font-medium"
                >
                  En savoir plus →
                </Link>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900">
              Ce que disent nos utilisateurs
            </h2>
            <p className="mt-4 text-xl text-gray-600">
              Découvrez comment FitTracker aide nos utilisateurs à atteindre leurs objectifs
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <div
                key={index}
                className="bg-gray-50 rounded-lg p-6"
              >
                <div className="flex items-center mb-4">
                  <img
                    className="h-12 w-12 rounded-full mr-4"
                    src={testimonial.avatar}
                    alt={testimonial.name}
                  />
                  <div>
                    <h4 className="font-semibold">{testimonial.name}</h4>
                    <p className="text-gray-600 text-sm">{testimonial.role}</p>
                  </div>
                </div>
                <p className="text-gray-700 italic">"{testimonial.content}"</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA section */}
      <section className="py-16 bg-secondary text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold mb-6">
            Prêt à améliorer vos entraînements ?
          </h2>
          <p className="text-xl mb-8 max-w-3xl mx-auto">
            Commencez dès aujourd'hui à utiliser FitTracker gratuitement et sans publicité.
          </p>
          <Link
            to="/timer"
            className="btn bg-white hover:bg-gray-100 text-secondary font-bold py-3 px-8 rounded-lg shadow-lg"
          >
            Commencer maintenant
          </Link>
        </div>
      </section>
    </div>
  );
};

export default Home;
