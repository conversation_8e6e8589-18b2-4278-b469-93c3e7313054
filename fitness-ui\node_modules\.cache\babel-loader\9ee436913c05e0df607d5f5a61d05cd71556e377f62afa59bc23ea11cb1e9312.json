{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projet fitness\\\\DATA\\\\fitness-ui\\\\src\\\\pages\\\\Routes.js\",\n  _s2 = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, useMapEvents } from 'react-leaflet';\nimport L from 'leaflet';\nimport { FiMap, FiNavigation, FiMapPin, FiSearch, FiPlus, FiStar, FiUsers, FiTarget, FiBookmark, FiTrash2, FiPlay, FiClock, FiAlertCircle } from 'react-icons/fi';\nimport { useAuth } from '../contexts/AuthContext';\nimport { generatePopularRoutes, generatePOIs, calculateDistance, optimizeRouteWithPOIs, DEFAULT_POSITION } from '../utils/mapUtils';\n\n// Configuration des icônes Leaflet\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\ndelete L.Icon.Default.prototype._getIconUrl;\nL.Icon.Default.mergeOptions({\n  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',\n  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',\n  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png'\n});\nconst Routes = () => {\n  _s2();\n  var _s = $RefreshSig$();\n  const {\n    user\n  } = useAuth();\n  const [userPosition, setUserPosition] = useState(DEFAULT_POSITION);\n  const [routes, setRoutes] = useState([]);\n  const [pois, setPois] = useState([]);\n  const [selectedRoute, setSelectedRoute] = useState(null);\n  const [selectedPOIs, setSelectedPOIs] = useState([]);\n  const [optimizedRoute, setOptimizedRoute] = useState(null);\n  const [isPlanning, setIsPlanning] = useState(false);\n  const [planningPoints, setPlanningPoints] = useState([]);\n  const [filters, setFilters] = useState({\n    type: 'all',\n    difficulty: 'all',\n    distance: 'all'\n  });\n  const [searchTerm, setSearchTerm] = useState('');\n  const [showPOIs, setShowPOIs] = useState(true);\n  const [activeTab, setActiveTab] = useState('discover');\n  const [savedRoutes, setSavedRoutes] = useState([]);\n  const [routeHistory, setRouteHistory] = useState([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    // Charger les routes sauvegardées et l'historique depuis localStorage\n    try {\n      const saved = JSON.parse(localStorage.getItem('savedRoutes') || '[]');\n      const history = JSON.parse(localStorage.getItem('routeHistory') || '[]');\n      setSavedRoutes(saved);\n      setRouteHistory(history);\n    } catch (error) {\n      console.error('Erreur lors du chargement des données:', error);\n    }\n  }, []);\n  useEffect(() => {\n    setIsLoading(true);\n    setError(null);\n\n    // Obtenir la position de l'utilisateur\n    if (navigator.geolocation) {\n      navigator.geolocation.getCurrentPosition(position => {\n        const pos = {\n          lat: position.coords.latitude,\n          lng: position.coords.longitude\n        };\n        setUserPosition(pos);\n        loadRoutesAndPOIs(pos);\n      }, error => {\n        console.warn('Géolocalisation échouée:', error);\n        setError('Impossible d\\'obtenir votre position. Utilisation de la position par défaut.');\n        loadRoutesAndPOIs(DEFAULT_POSITION);\n      }, {\n        enableHighAccuracy: true,\n        timeout: 10000,\n        maximumAge: 300000 // 5 minutes\n      });\n    } else {\n      setError('Géolocalisation non supportée par votre navigateur.');\n      loadRoutesAndPOIs(DEFAULT_POSITION);\n    }\n  }, []);\n  const loadRoutesAndPOIs = position => {\n    try {\n      const popularRoutes = generatePopularRoutes(position.lat, position.lng, 15);\n      const nearbyPOIs = generatePOIs(position.lat, position.lng, 10);\n\n      // Combiner avec les routes sauvegardées\n      const allRoutes = [...savedRoutes, ...popularRoutes];\n      setRoutes(allRoutes);\n      setPois(nearbyPOIs);\n      setIsLoading(false);\n    } catch (error) {\n      console.error('Erreur lors du chargement des routes:', error);\n      setError('Erreur lors du chargement des données de carte.');\n      setIsLoading(false);\n    }\n  };\n  const filteredRoutes = routes.filter(route => {\n    const matchesSearch = route.name.toLowerCase().includes(searchTerm.toLowerCase()) || route.description.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesType = filters.type === 'all' || route.type === filters.type;\n    const matchesDifficulty = filters.difficulty === 'all' || route.difficulty === filters.difficulty;\n    const matchesDistance = filters.distance === 'all' || filters.distance === 'short' && route.distance <= 5 || filters.distance === 'medium' && route.distance > 5 && route.distance <= 15 || filters.distance === 'long' && route.distance > 15;\n    return matchesSearch && matchesType && matchesDifficulty && matchesDistance;\n  });\n  const handleRouteSelect = route => {\n    setSelectedRoute(route);\n    setOptimizedRoute(null);\n    setSelectedPOIs([]);\n  };\n  const handlePOIToggle = poi => {\n    setSelectedPOIs(prev => {\n      const isSelected = prev.find(p => p.id === poi.id);\n      if (isSelected) {\n        return prev.filter(p => p.id !== poi.id);\n      } else {\n        return [...prev, poi];\n      }\n    });\n  };\n  const optimizeRoute = () => {\n    if (selectedRoute && selectedPOIs.length > 0) {\n      const startPoint = selectedRoute.points[0];\n      const endPoint = selectedRoute.points[selectedRoute.points.length - 1];\n      const optimized = optimizeRouteWithPOIs(startPoint, endPoint, selectedPOIs);\n      setOptimizedRoute(optimized);\n    }\n  };\n  const startRoutePlanning = () => {\n    setIsPlanning(true);\n    setPlanningPoints([]);\n    setSelectedRoute(null);\n    setOptimizedRoute(null);\n  };\n  const MapClickHandler = () => {\n    _s();\n    useMapEvents({\n      click: e => {\n        if (isPlanning) {\n          const newPoint = {\n            lat: e.latlng.lat,\n            lng: e.latlng.lng,\n            elevation: 100 // Valeur par défaut\n          };\n          setPlanningPoints(prev => [...prev, newPoint]);\n        }\n      }\n    });\n    return null;\n  };\n  _s(MapClickHandler, \"Ld/tk8Iz8AdZhC1l7acENaOEoCo=\", false, function () {\n    return [useMapEvents];\n  });\n  const finishPlanning = () => {\n    if (planningPoints.length >= 2) {\n      const newRoute = {\n        id: `custom_${Date.now()}`,\n        name: 'Mon parcours personnalisé',\n        type: 'running',\n        distance: calculateTotalDistance(planningPoints),\n        points: planningPoints,\n        difficulty: 'modéré',\n        rating: 0,\n        completions: 0,\n        createdBy: (user === null || user === void 0 ? void 0 : user.firstName) || 'Moi',\n        tags: ['custom'],\n        description: 'Parcours créé par l\\'utilisateur',\n        createdAt: new Date().toISOString(),\n        isCustom: true\n      };\n\n      // Sauvegarder dans localStorage\n      const updatedSavedRoutes = [newRoute, ...savedRoutes];\n      setSavedRoutes(updatedSavedRoutes);\n      localStorage.setItem('savedRoutes', JSON.stringify(updatedSavedRoutes));\n      setRoutes(prev => [newRoute, ...prev]);\n      setSelectedRoute(newRoute);\n      setIsPlanning(false);\n      setPlanningPoints([]);\n    }\n  };\n  const calculateTotalDistance = points => {\n    let total = 0;\n    for (let i = 1; i < points.length; i++) {\n      total += calculateDistance(points[i - 1].lat, points[i - 1].lng, points[i].lat, points[i].lng);\n    }\n    return total;\n  };\n  const getDifficultyColor = difficulty => {\n    switch (difficulty) {\n      case 'facile':\n        return 'text-green-600 bg-green-100';\n      case 'modéré':\n        return 'text-yellow-600 bg-yellow-100';\n      case 'difficile':\n        return 'text-red-600 bg-red-100';\n      default:\n        return 'text-gray-600 bg-gray-100';\n    }\n  };\n  const getTypeIcon = type => {\n    switch (type) {\n      case 'running':\n        return '🏃‍♂️';\n      case 'cycling':\n        return '🚴‍♂️';\n      case 'hiking':\n        return '🥾';\n      case 'walking':\n        return '🚶‍♂️';\n      default:\n        return '📍';\n    }\n  };\n  const saveRoute = route => {\n    if (!savedRoutes.find(r => r.id === route.id)) {\n      const routeToSave = {\n        ...route,\n        savedAt: new Date().toISOString()\n      };\n      const updatedSavedRoutes = [routeToSave, ...savedRoutes];\n      setSavedRoutes(updatedSavedRoutes);\n      localStorage.setItem('savedRoutes', JSON.stringify(updatedSavedRoutes));\n    }\n  };\n  const unsaveRoute = routeId => {\n    const updatedSavedRoutes = savedRoutes.filter(r => r.id !== routeId);\n    setSavedRoutes(updatedSavedRoutes);\n    localStorage.setItem('savedRoutes', JSON.stringify(updatedSavedRoutes));\n  };\n  const deleteCustomRoute = routeId => {\n    // Supprimer des routes sauvegardées\n    const updatedSavedRoutes = savedRoutes.filter(r => r.id !== routeId);\n    setSavedRoutes(updatedSavedRoutes);\n    localStorage.setItem('savedRoutes', JSON.stringify(updatedSavedRoutes));\n\n    // Supprimer de la liste des routes\n    setRoutes(prev => prev.filter(r => r.id !== routeId));\n\n    // Désélectionner si c'était la route sélectionnée\n    if ((selectedRoute === null || selectedRoute === void 0 ? void 0 : selectedRoute.id) === routeId) {\n      setSelectedRoute(null);\n    }\n  };\n  const startRoute = route => {\n    const historyEntry = {\n      id: Date.now(),\n      route: route,\n      startedAt: new Date().toISOString(),\n      status: 'started'\n    };\n    const updatedHistory = [historyEntry, ...routeHistory];\n    setRouteHistory(updatedHistory);\n    localStorage.setItem('routeHistory', JSON.stringify(updatedHistory));\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold text-gray-900 mb-4\",\n          children: \"Cartes et Itin\\xE9raires\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 290,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg text-gray-600 max-w-3xl mx-auto\",\n          children: \"D\\xE9couvrez de nouveaux parcours, planifiez vos itin\\xE9raires et explorez les points d'int\\xE9r\\xEAt autour de vous.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 293,\n          columnNumber: 11\n        }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4 max-w-md mx-auto bg-yellow-50 border border-yellow-200 rounded-lg p-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(FiAlertCircle, {\n              className: \"h-5 w-5 text-yellow-600 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 302,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-yellow-800\",\n              children: error\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 303,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 301,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 300,\n          columnNumber: 13\n        }, this), isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4 max-w-md mx-auto bg-blue-50 border border-blue-200 rounded-lg p-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 312,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-blue-800\",\n              children: \"Chargement des donn\\xE9es...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 313,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 311,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 310,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 289,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex space-x-1 bg-gray-100 p-1 rounded-lg mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setActiveTab('discover'),\n          className: `flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${activeTab === 'discover' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'}`,\n          children: [/*#__PURE__*/_jsxDEV(FiMap, {\n            className: \"inline mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 329,\n            columnNumber: 13\n          }, this), \"D\\xE9couvrir\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 321,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setActiveTab('saved'),\n          className: `flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${activeTab === 'saved' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'}`,\n          children: [/*#__PURE__*/_jsxDEV(FiBookmark, {\n            className: \"inline mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 340,\n            columnNumber: 13\n          }, this), \"Sauvegard\\xE9es (\", savedRoutes.length, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 332,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setActiveTab('plan'),\n          className: `flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${activeTab === 'plan' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'}`,\n          children: [/*#__PURE__*/_jsxDEV(FiNavigation, {\n            className: \"inline mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 351,\n            columnNumber: 13\n          }, this), \"Planifier\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 343,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setActiveTab('pois'),\n          className: `flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${activeTab === 'pois' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'}`,\n          children: [/*#__PURE__*/_jsxDEV(FiMapPin, {\n            className: \"inline mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 362,\n            columnNumber: 13\n          }, this), \"Points d'int\\xE9r\\xEAt\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 354,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 320,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:col-span-1\",\n          children: [activeTab === 'discover' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-xl font-semibold text-gray-900\",\n                children: \"Routes populaires\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 373,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setShowPOIs(!showPOIs),\n                className: `px-3 py-1 rounded-md text-sm ${showPOIs ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-600'}`,\n                children: \"POIs\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 376,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 372,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4 mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative\",\n                children: [/*#__PURE__*/_jsxDEV(FiSearch, {\n                  className: \"absolute left-3 top-3 h-4 w-4 text-gray-400\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 389,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  placeholder: \"Rechercher un parcours...\",\n                  value: searchTerm,\n                  onChange: e => setSearchTerm(e.target.value),\n                  className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 390,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 388,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-2 gap-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"select\", {\n                  value: filters.type,\n                  onChange: e => setFilters(prev => ({\n                    ...prev,\n                    type: e.target.value\n                  })),\n                  className: \"px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"all\",\n                    children: \"Tous types\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 405,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"running\",\n                    children: \"Course\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 406,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"cycling\",\n                    children: \"V\\xE9lo\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 407,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"hiking\",\n                    children: \"Randonn\\xE9e\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 408,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"walking\",\n                    children: \"Marche\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 409,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 400,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  value: filters.difficulty,\n                  onChange: e => setFilters(prev => ({\n                    ...prev,\n                    difficulty: e.target.value\n                  })),\n                  className: \"px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"all\",\n                    children: \"Toutes difficult\\xE9s\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 417,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"facile\",\n                    children: \"Facile\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 418,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"mod\\xE9r\\xE9\",\n                    children: \"Mod\\xE9r\\xE9\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 419,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"difficile\",\n                    children: \"Difficile\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 420,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 412,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 399,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 387,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3 max-h-96 overflow-y-auto\",\n              children: filteredRoutes.map(route => /*#__PURE__*/_jsxDEV(\"div\", {\n                onClick: () => handleRouteSelect(route),\n                className: `p-4 border rounded-lg cursor-pointer transition-colors ${(selectedRoute === null || selectedRoute === void 0 ? void 0 : selectedRoute.id) === route.id ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'}`,\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-start justify-between mb-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-lg\",\n                      children: getTypeIcon(route.type)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 439,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"font-medium text-gray-900 text-sm\",\n                      children: route.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 440,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 438,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(route.difficulty)}`,\n                    children: route.difficulty\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 442,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 437,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-gray-600 mb-2\",\n                  children: route.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 447,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between text-xs text-gray-500 mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [route.distance.toFixed(1), \" km\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 452,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(FiStar, {\n                        className: \"h-3 w-3 text-yellow-400 mr-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 455,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: route.rating.toFixed(1)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 456,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 454,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(FiUsers, {\n                        className: \"h-3 w-3 mr-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 459,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: route.completions\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 460,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 458,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 453,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 451,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2\",\n                  onClick: e => e.stopPropagation(),\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => startRoute(route),\n                    className: \"flex-1 bg-green-600 text-white py-1 px-2 rounded text-xs hover:bg-green-700 transition-colors flex items-center justify-center\",\n                    children: [/*#__PURE__*/_jsxDEV(FiPlay, {\n                      className: \"h-3 w-3 mr-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 471,\n                      columnNumber: 27\n                    }, this), \"D\\xE9marrer\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 467,\n                    columnNumber: 25\n                  }, this), savedRoutes.find(r => r.id === route.id) ? /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => unsaveRoute(route.id),\n                    className: \"bg-gray-600 text-white py-1 px-2 rounded text-xs hover:bg-gray-700 transition-colors\",\n                    title: \"Retirer des favoris\",\n                    children: /*#__PURE__*/_jsxDEV(FiBookmark, {\n                      className: \"h-3 w-3\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 481,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 476,\n                    columnNumber: 27\n                  }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => saveRoute(route),\n                    className: \"bg-blue-600 text-white py-1 px-2 rounded text-xs hover:bg-blue-700 transition-colors\",\n                    title: \"Sauvegarder\",\n                    children: /*#__PURE__*/_jsxDEV(FiBookmark, {\n                      className: \"h-3 w-3\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 489,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 484,\n                    columnNumber: 27\n                  }, this), route.isCustom && /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => deleteCustomRoute(route.id),\n                    className: \"bg-red-600 text-white py-1 px-2 rounded text-xs hover:bg-red-700 transition-colors\",\n                    title: \"Supprimer\",\n                    children: /*#__PURE__*/_jsxDEV(FiTrash2, {\n                      className: \"h-3 w-3\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 499,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 494,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 466,\n                  columnNumber: 23\n                }, this)]\n              }, route.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 428,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 426,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 371,\n            columnNumber: 15\n          }, this), activeTab === 'plan' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-xl font-semibold text-gray-900 mb-4\",\n              children: \"Planificateur d'itin\\xE9raire\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 511,\n              columnNumber: 17\n            }, this), !isPlanning ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: startRoutePlanning,\n                className: \"w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center\",\n                children: [/*#__PURE__*/_jsxDEV(FiPlus, {\n                  className: \"mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 521,\n                  columnNumber: 23\n                }, this), \"Cr\\xE9er un nouveau parcours\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 517,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-600\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mb-2\",\n                  children: \"Instructions :\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 526,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                  className: \"list-disc list-inside space-y-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                    children: \"Cliquez sur \\\"Cr\\xE9er un nouveau parcours\\\"\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 528,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    children: \"Cliquez sur la carte pour ajouter des points\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 529,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    children: \"Minimum 2 points requis\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 530,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    children: \"Cliquez sur \\\"Terminer\\\" pour sauvegarder\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 531,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 527,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 525,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 516,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-600\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"font-medium mb-2\",\n                  children: \"Mode planification actif\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 538,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [\"Points ajout\\xE9s : \", planningPoints.length]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 539,\n                  columnNumber: 23\n                }, this), planningPoints.length >= 2 && /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [\"Distance : \", calculateTotalDistance(planningPoints).toFixed(1), \" km\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 541,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 537,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: finishPlanning,\n                  disabled: planningPoints.length < 2,\n                  className: \"flex-1 bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors disabled:bg-gray-300 disabled:cursor-not-allowed\",\n                  children: \"Terminer\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 546,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    setIsPlanning(false);\n                    setPlanningPoints([]);\n                  },\n                  className: \"flex-1 bg-gray-600 text-white py-2 px-4 rounded-lg hover:bg-gray-700 transition-colors\",\n                  children: \"Annuler\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 553,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 545,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 536,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 510,\n            columnNumber: 15\n          }, this), activeTab === 'pois' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-xl font-semibold text-gray-900 mb-4\",\n              children: \"Points d'int\\xE9r\\xEAt\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 570,\n              columnNumber: 17\n            }, this), selectedRoute && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4 p-3 bg-blue-50 rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-blue-700 mb-2\",\n                children: \"S\\xE9lectionnez des POIs pour optimiser votre parcours\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 576,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: optimizeRoute,\n                disabled: selectedPOIs.length === 0,\n                className: \"w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors disabled:bg-gray-300 disabled:cursor-not-allowed text-sm\",\n                children: [/*#__PURE__*/_jsxDEV(FiTarget, {\n                  className: \"inline mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 584,\n                  columnNumber: 23\n                }, this), \"Optimiser l'itin\\xE9raire (\", selectedPOIs.length, \" POIs)\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 579,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 575,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2 max-h-96 overflow-y-auto\",\n              children: pois.map(poi => /*#__PURE__*/_jsxDEV(\"div\", {\n                onClick: () => handlePOIToggle(poi),\n                className: `p-3 border rounded-lg cursor-pointer transition-colors ${selectedPOIs.find(p => p.id === poi.id) ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'}`,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-lg\",\n                    children: poi.icon\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 602,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"font-medium text-gray-900 text-sm\",\n                      children: poi.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 604,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-xs text-gray-600\",\n                      children: poi.description\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 605,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center mt-1\",\n                      children: [/*#__PURE__*/_jsxDEV(FiStar, {\n                        className: \"h-3 w-3 text-yellow-400 mr-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 607,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-xs text-gray-500\",\n                        children: poi.rating.toFixed(1)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 608,\n                        columnNumber: 29\n                      }, this), poi.verified && /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"ml-2 text-xs text-green-600\",\n                        children: \"\\u2713 V\\xE9rifi\\xE9\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 610,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 606,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 603,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 601,\n                  columnNumber: 23\n                }, this)\n              }, poi.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 592,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 590,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 569,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 369,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:col-span-2\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg overflow-hidden\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"h-96 lg:h-[600px]\",\n              children: /*#__PURE__*/_jsxDEV(MapContainer, {\n                center: [userPosition.lat, userPosition.lng],\n                zoom: 13,\n                style: {\n                  height: '100%',\n                  width: '100%'\n                },\n                children: [/*#__PURE__*/_jsxDEV(TileLayer, {\n                  url: \"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png\",\n                  attribution: \"\\xA9 <a href=\\\"https://www.openstreetmap.org/copyright\\\">OpenStreetMap</a> contributors\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 631,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MapClickHandler, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 636,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Marker, {\n                  position: [userPosition.lat, userPosition.lng],\n                  children: /*#__PURE__*/_jsxDEV(Popup, {\n                    children: \"Votre position\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 640,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 639,\n                  columnNumber: 19\n                }, this), selectedRoute && /*#__PURE__*/_jsxDEV(Polyline, {\n                  positions: selectedRoute.points.map(p => [p.lat, p.lng]),\n                  color: \"blue\",\n                  weight: 4,\n                  opacity: 0.7\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 645,\n                  columnNumber: 21\n                }, this), optimizedRoute && /*#__PURE__*/_jsxDEV(Polyline, {\n                  positions: optimizedRoute.map(p => [p.lat, p.lng]),\n                  color: \"red\",\n                  weight: 4,\n                  opacity: 0.8\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 655,\n                  columnNumber: 21\n                }, this), planningPoints.map((point, index) => /*#__PURE__*/_jsxDEV(Marker, {\n                  position: [point.lat, point.lng],\n                  children: /*#__PURE__*/_jsxDEV(Popup, {\n                    children: [\"Point \", index + 1]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 666,\n                    columnNumber: 23\n                  }, this)\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 665,\n                  columnNumber: 21\n                }, this)), planningPoints.length > 1 && /*#__PURE__*/_jsxDEV(Polyline, {\n                  positions: planningPoints.map(p => [p.lat, p.lng]),\n                  color: \"green\",\n                  weight: 4,\n                  opacity: 0.7\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 672,\n                  columnNumber: 21\n                }, this), showPOIs && pois.map(poi => /*#__PURE__*/_jsxDEV(Marker, {\n                  position: [poi.lat, poi.lng],\n                  icon: L.divIcon({\n                    html: `<div style=\"background: ${poi.color}; color: white; border-radius: 50%; width: 30px; height: 30px; display: flex; align-items: center; justify-content: center; font-size: 16px;\">${poi.icon}</div>`,\n                    className: 'custom-poi-marker',\n                    iconSize: [30, 30]\n                  }),\n                  children: /*#__PURE__*/_jsxDEV(Popup, {\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                        className: \"font-medium\",\n                        children: poi.name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 693,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm text-gray-600\",\n                        children: poi.description\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 694,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center mt-1\",\n                        children: [/*#__PURE__*/_jsxDEV(FiStar, {\n                          className: \"h-3 w-3 text-yellow-400 mr-1\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 696,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-sm\",\n                          children: poi.rating.toFixed(1)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 697,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 695,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 692,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 691,\n                    columnNumber: 23\n                  }, this)\n                }, poi.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 682,\n                  columnNumber: 21\n                }, this)), selectedPOIs.map(poi => /*#__PURE__*/_jsxDEV(Marker, {\n                  position: [poi.lat, poi.lng],\n                  icon: L.divIcon({\n                    html: `<div style=\"background: red; color: white; border-radius: 50%; width: 35px; height: 35px; display: flex; align-items: center; justify-content: center; font-size: 18px; border: 3px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.3);\">${poi.icon}</div>`,\n                    className: 'custom-selected-poi-marker',\n                    iconSize: [35, 35]\n                  })\n                }, `selected-${poi.id}`, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 706,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 626,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 625,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 624,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 623,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 367,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 287,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 286,\n    columnNumber: 5\n  }, this);\n};\n_s2(Routes, \"xpmEf8mHgqX64tgKrpvfmoaSGpE=\", false, function () {\n  return [useAuth];\n});\n_c = Routes;\nexport default Routes;\nvar _c;\n$RefreshReg$(_c, \"Routes\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "MapContainer", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Popup", "Polyline", "useMapEvents", "L", "FiMap", "FiNavigation", "FiMapPin", "FiSearch", "FiPlus", "FiStar", "FiUsers", "<PERSON><PERSON><PERSON><PERSON>", "FiBookmark", "FiTrash2", "FiPlay", "<PERSON><PERSON><PERSON>", "FiAlertCircle", "useAuth", "generatePopularRoutes", "generatePOIs", "calculateDistance", "optimizeRouteWithPOIs", "DEFAULT_POSITION", "jsxDEV", "_jsxDEV", "Icon", "<PERSON><PERSON><PERSON>", "prototype", "_getIconUrl", "mergeOptions", "iconRetinaUrl", "iconUrl", "shadowUrl", "Routes", "_s2", "_s", "$RefreshSig$", "user", "userPosition", "setUserPosition", "routes", "setRoutes", "pois", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedRoute", "selectedPOIs", "setSelectedPOIs", "optimizedRoute", "setOptimizedRoute", "isPlanning", "setIsPlanning", "planningPoints", "setPlanningPoints", "filters", "setFilters", "type", "difficulty", "distance", "searchTerm", "setSearchTerm", "showPOIs", "setShowPOIs", "activeTab", "setActiveTab", "savedRoutes", "setSavedRoutes", "routeHistory", "setRouteHistory", "isLoading", "setIsLoading", "error", "setError", "saved", "JSON", "parse", "localStorage", "getItem", "history", "console", "navigator", "geolocation", "getCurrentPosition", "position", "pos", "lat", "coords", "latitude", "lng", "longitude", "loadRoutesAndPOIs", "warn", "enableHighAccuracy", "timeout", "maximumAge", "popularRoutes", "nearbyPOIs", "allRoutes", "filteredRoutes", "filter", "route", "matchesSearch", "name", "toLowerCase", "includes", "description", "matchesType", "matchesDifficulty", "matchesDistance", "handleRouteSelect", "handlePOIToggle", "poi", "prev", "isSelected", "find", "p", "id", "optimizeRoute", "length", "startPoint", "points", "endPoint", "optimized", "startRoutePlanning", "MapClickHandler", "click", "e", "newPoint", "latlng", "elevation", "finishPlanning", "newRoute", "Date", "now", "calculateTotalDistance", "rating", "completions", "created<PERSON>y", "firstName", "tags", "createdAt", "toISOString", "isCustom", "updatedSavedRoutes", "setItem", "stringify", "total", "i", "getDifficultyColor", "getTypeIcon", "saveRoute", "r", "routeToSave", "savedAt", "unsaveRoute", "routeId", "deleteCustomRoute", "startRoute", "historyEntry", "startedAt", "status", "updatedHistory", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "placeholder", "value", "onChange", "target", "map", "toFixed", "stopPropagation", "title", "disabled", "icon", "verified", "center", "zoom", "style", "height", "width", "url", "attribution", "positions", "color", "weight", "opacity", "point", "index", "divIcon", "html", "iconSize", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/projet fitness/DATA/fitness-ui/src/pages/Routes.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, useMapEvents } from 'react-leaflet';\nimport L from 'leaflet';\nimport {\n  FiMap,\n  FiNavigation,\n  FiMapPin,\n  FiSearch,\n  FiPlus,\n  FiStar,\n  FiUsers,\n  FiTarget,\n  FiBookmark,\n  FiTrash2,\n  FiPlay,\n  FiClock,\n  FiAlertCircle\n} from 'react-icons/fi';\nimport { useAuth } from '../contexts/AuthContext';\nimport {\n  generatePopularRoutes,\n  generatePOIs,\n  calculateDistance,\n  optimizeRouteWithPOIs,\n  DEFAULT_POSITION\n} from '../utils/mapUtils';\n\n// Configuration des icônes Leaflet\ndelete L.Icon.Default.prototype._getIconUrl;\nL.Icon.Default.mergeOptions({\n  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',\n  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',\n  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',\n});\n\nconst Routes = () => {\n  const { user } = useAuth();\n  const [userPosition, setUserPosition] = useState(DEFAULT_POSITION);\n  const [routes, setRoutes] = useState([]);\n  const [pois, setPois] = useState([]);\n  const [selectedRoute, setSelectedRoute] = useState(null);\n  const [selectedPOIs, setSelectedPOIs] = useState([]);\n  const [optimizedRoute, setOptimizedRoute] = useState(null);\n  const [isPlanning, setIsPlanning] = useState(false);\n  const [planningPoints, setPlanningPoints] = useState([]);\n  const [filters, setFilters] = useState({\n    type: 'all',\n    difficulty: 'all',\n    distance: 'all'\n  });\n  const [searchTerm, setSearchTerm] = useState('');\n  const [showPOIs, setShowPOIs] = useState(true);\n  const [activeTab, setActiveTab] = useState('discover');\n  const [savedRoutes, setSavedRoutes] = useState([]);\n  const [routeHistory, setRouteHistory] = useState([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n  useEffect(() => {\n    // Charger les routes sauvegardées et l'historique depuis localStorage\n    try {\n      const saved = JSON.parse(localStorage.getItem('savedRoutes') || '[]');\n      const history = JSON.parse(localStorage.getItem('routeHistory') || '[]');\n      setSavedRoutes(saved);\n      setRouteHistory(history);\n    } catch (error) {\n      console.error('Erreur lors du chargement des données:', error);\n    }\n  }, []);\n\n  useEffect(() => {\n    setIsLoading(true);\n    setError(null);\n\n    // Obtenir la position de l'utilisateur\n    if (navigator.geolocation) {\n      navigator.geolocation.getCurrentPosition(\n        (position) => {\n          const pos = {\n            lat: position.coords.latitude,\n            lng: position.coords.longitude\n          };\n          setUserPosition(pos);\n          loadRoutesAndPOIs(pos);\n        },\n        (error) => {\n          console.warn('Géolocalisation échouée:', error);\n          setError('Impossible d\\'obtenir votre position. Utilisation de la position par défaut.');\n          loadRoutesAndPOIs(DEFAULT_POSITION);\n        },\n        {\n          enableHighAccuracy: true,\n          timeout: 10000,\n          maximumAge: 300000 // 5 minutes\n        }\n      );\n    } else {\n      setError('Géolocalisation non supportée par votre navigateur.');\n      loadRoutesAndPOIs(DEFAULT_POSITION);\n    }\n  }, []);\n\n  const loadRoutesAndPOIs = (position) => {\n    try {\n      const popularRoutes = generatePopularRoutes(position.lat, position.lng, 15);\n      const nearbyPOIs = generatePOIs(position.lat, position.lng, 10);\n\n      // Combiner avec les routes sauvegardées\n      const allRoutes = [...savedRoutes, ...popularRoutes];\n      setRoutes(allRoutes);\n      setPois(nearbyPOIs);\n      setIsLoading(false);\n    } catch (error) {\n      console.error('Erreur lors du chargement des routes:', error);\n      setError('Erreur lors du chargement des données de carte.');\n      setIsLoading(false);\n    }\n  };\n\n  const filteredRoutes = routes.filter(route => {\n    const matchesSearch = route.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         route.description.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesType = filters.type === 'all' || route.type === filters.type;\n    const matchesDifficulty = filters.difficulty === 'all' || route.difficulty === filters.difficulty;\n    const matchesDistance = filters.distance === 'all' || \n                           (filters.distance === 'short' && route.distance <= 5) ||\n                           (filters.distance === 'medium' && route.distance > 5 && route.distance <= 15) ||\n                           (filters.distance === 'long' && route.distance > 15);\n    \n    return matchesSearch && matchesType && matchesDifficulty && matchesDistance;\n  });\n\n  const handleRouteSelect = (route) => {\n    setSelectedRoute(route);\n    setOptimizedRoute(null);\n    setSelectedPOIs([]);\n  };\n\n  const handlePOIToggle = (poi) => {\n    setSelectedPOIs(prev => {\n      const isSelected = prev.find(p => p.id === poi.id);\n      if (isSelected) {\n        return prev.filter(p => p.id !== poi.id);\n      } else {\n        return [...prev, poi];\n      }\n    });\n  };\n\n  const optimizeRoute = () => {\n    if (selectedRoute && selectedPOIs.length > 0) {\n      const startPoint = selectedRoute.points[0];\n      const endPoint = selectedRoute.points[selectedRoute.points.length - 1];\n      const optimized = optimizeRouteWithPOIs(startPoint, endPoint, selectedPOIs);\n      setOptimizedRoute(optimized);\n    }\n  };\n\n  const startRoutePlanning = () => {\n    setIsPlanning(true);\n    setPlanningPoints([]);\n    setSelectedRoute(null);\n    setOptimizedRoute(null);\n  };\n\n  const MapClickHandler = () => {\n    useMapEvents({\n      click: (e) => {\n        if (isPlanning) {\n          const newPoint = {\n            lat: e.latlng.lat,\n            lng: e.latlng.lng,\n            elevation: 100 // Valeur par défaut\n          };\n          setPlanningPoints(prev => [...prev, newPoint]);\n        }\n      }\n    });\n    return null;\n  };\n\n  const finishPlanning = () => {\n    if (planningPoints.length >= 2) {\n      const newRoute = {\n        id: `custom_${Date.now()}`,\n        name: 'Mon parcours personnalisé',\n        type: 'running',\n        distance: calculateTotalDistance(planningPoints),\n        points: planningPoints,\n        difficulty: 'modéré',\n        rating: 0,\n        completions: 0,\n        createdBy: user?.firstName || 'Moi',\n        tags: ['custom'],\n        description: 'Parcours créé par l\\'utilisateur',\n        createdAt: new Date().toISOString(),\n        isCustom: true\n      };\n\n      // Sauvegarder dans localStorage\n      const updatedSavedRoutes = [newRoute, ...savedRoutes];\n      setSavedRoutes(updatedSavedRoutes);\n      localStorage.setItem('savedRoutes', JSON.stringify(updatedSavedRoutes));\n\n      setRoutes(prev => [newRoute, ...prev]);\n      setSelectedRoute(newRoute);\n      setIsPlanning(false);\n      setPlanningPoints([]);\n    }\n  };\n\n  const calculateTotalDistance = (points) => {\n    let total = 0;\n    for (let i = 1; i < points.length; i++) {\n      total += calculateDistance(\n        points[i-1].lat, points[i-1].lng,\n        points[i].lat, points[i].lng\n      );\n    }\n    return total;\n  };\n\n  const getDifficultyColor = (difficulty) => {\n    switch (difficulty) {\n      case 'facile': return 'text-green-600 bg-green-100';\n      case 'modéré': return 'text-yellow-600 bg-yellow-100';\n      case 'difficile': return 'text-red-600 bg-red-100';\n      default: return 'text-gray-600 bg-gray-100';\n    }\n  };\n\n  const getTypeIcon = (type) => {\n    switch (type) {\n      case 'running': return '🏃‍♂️';\n      case 'cycling': return '🚴‍♂️';\n      case 'hiking': return '🥾';\n      case 'walking': return '🚶‍♂️';\n      default: return '📍';\n    }\n  };\n\n  const saveRoute = (route) => {\n    if (!savedRoutes.find(r => r.id === route.id)) {\n      const routeToSave = { ...route, savedAt: new Date().toISOString() };\n      const updatedSavedRoutes = [routeToSave, ...savedRoutes];\n      setSavedRoutes(updatedSavedRoutes);\n      localStorage.setItem('savedRoutes', JSON.stringify(updatedSavedRoutes));\n    }\n  };\n\n  const unsaveRoute = (routeId) => {\n    const updatedSavedRoutes = savedRoutes.filter(r => r.id !== routeId);\n    setSavedRoutes(updatedSavedRoutes);\n    localStorage.setItem('savedRoutes', JSON.stringify(updatedSavedRoutes));\n  };\n\n  const deleteCustomRoute = (routeId) => {\n    // Supprimer des routes sauvegardées\n    const updatedSavedRoutes = savedRoutes.filter(r => r.id !== routeId);\n    setSavedRoutes(updatedSavedRoutes);\n    localStorage.setItem('savedRoutes', JSON.stringify(updatedSavedRoutes));\n\n    // Supprimer de la liste des routes\n    setRoutes(prev => prev.filter(r => r.id !== routeId));\n\n    // Désélectionner si c'était la route sélectionnée\n    if (selectedRoute?.id === routeId) {\n      setSelectedRoute(null);\n    }\n  };\n\n  const startRoute = (route) => {\n    const historyEntry = {\n      id: Date.now(),\n      route: route,\n      startedAt: new Date().toISOString(),\n      status: 'started'\n    };\n\n    const updatedHistory = [historyEntry, ...routeHistory];\n    setRouteHistory(updatedHistory);\n    localStorage.setItem('routeHistory', JSON.stringify(updatedHistory));\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Header */}\n        <div className=\"text-center mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-4\">\n            Cartes et Itinéraires\n          </h1>\n          <p className=\"text-lg text-gray-600 max-w-3xl mx-auto\">\n            Découvrez de nouveaux parcours, planifiez vos itinéraires et explorez\n            les points d'intérêt autour de vous.\n          </p>\n\n          {/* Messages d'erreur */}\n          {error && (\n            <div className=\"mt-4 max-w-md mx-auto bg-yellow-50 border border-yellow-200 rounded-lg p-4\">\n              <div className=\"flex items-center\">\n                <FiAlertCircle className=\"h-5 w-5 text-yellow-600 mr-2\" />\n                <p className=\"text-sm text-yellow-800\">{error}</p>\n              </div>\n            </div>\n          )}\n\n          {/* Indicateur de chargement */}\n          {isLoading && (\n            <div className=\"mt-4 max-w-md mx-auto bg-blue-50 border border-blue-200 rounded-lg p-4\">\n              <div className=\"flex items-center justify-center\">\n                <div className=\"animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600 mr-2\"></div>\n                <p className=\"text-sm text-blue-800\">Chargement des données...</p>\n              </div>\n            </div>\n          )}\n        </div>\n\n        {/* Tabs */}\n        <div className=\"flex space-x-1 bg-gray-100 p-1 rounded-lg mb-6\">\n          <button\n            onClick={() => setActiveTab('discover')}\n            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${\n              activeTab === 'discover'\n                ? 'bg-white text-blue-600 shadow-sm'\n                : 'text-gray-600 hover:text-gray-900'\n            }`}\n          >\n            <FiMap className=\"inline mr-2\" />\n            Découvrir\n          </button>\n          <button\n            onClick={() => setActiveTab('saved')}\n            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${\n              activeTab === 'saved'\n                ? 'bg-white text-blue-600 shadow-sm'\n                : 'text-gray-600 hover:text-gray-900'\n            }`}\n          >\n            <FiBookmark className=\"inline mr-2\" />\n            Sauvegardées ({savedRoutes.length})\n          </button>\n          <button\n            onClick={() => setActiveTab('plan')}\n            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${\n              activeTab === 'plan'\n                ? 'bg-white text-blue-600 shadow-sm'\n                : 'text-gray-600 hover:text-gray-900'\n            }`}\n          >\n            <FiNavigation className=\"inline mr-2\" />\n            Planifier\n          </button>\n          <button\n            onClick={() => setActiveTab('pois')}\n            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${\n              activeTab === 'pois'\n                ? 'bg-white text-blue-600 shadow-sm'\n                : 'text-gray-600 hover:text-gray-900'\n            }`}\n          >\n            <FiMapPin className=\"inline mr-2\" />\n            Points d'intérêt\n          </button>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n          {/* Sidebar */}\n          <div className=\"lg:col-span-1\">\n            {activeTab === 'discover' && (\n              <div className=\"bg-white rounded-lg shadow-lg p-6\">\n                <div className=\"flex items-center justify-between mb-4\">\n                  <h2 className=\"text-xl font-semibold text-gray-900\">\n                    Routes populaires\n                  </h2>\n                  <button\n                    onClick={() => setShowPOIs(!showPOIs)}\n                    className={`px-3 py-1 rounded-md text-sm ${\n                      showPOIs ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-600'\n                    }`}\n                  >\n                    POIs\n                  </button>\n                </div>\n\n                {/* Search and Filters */}\n                <div className=\"space-y-4 mb-6\">\n                  <div className=\"relative\">\n                    <FiSearch className=\"absolute left-3 top-3 h-4 w-4 text-gray-400\" />\n                    <input\n                      type=\"text\"\n                      placeholder=\"Rechercher un parcours...\"\n                      value={searchTerm}\n                      onChange={(e) => setSearchTerm(e.target.value)}\n                      className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    />\n                  </div>\n\n                  <div className=\"grid grid-cols-2 gap-2\">\n                    <select\n                      value={filters.type}\n                      onChange={(e) => setFilters(prev => ({ ...prev, type: e.target.value }))}\n                      className=\"px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    >\n                      <option value=\"all\">Tous types</option>\n                      <option value=\"running\">Course</option>\n                      <option value=\"cycling\">Vélo</option>\n                      <option value=\"hiking\">Randonnée</option>\n                      <option value=\"walking\">Marche</option>\n                    </select>\n\n                    <select\n                      value={filters.difficulty}\n                      onChange={(e) => setFilters(prev => ({ ...prev, difficulty: e.target.value }))}\n                      className=\"px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    >\n                      <option value=\"all\">Toutes difficultés</option>\n                      <option value=\"facile\">Facile</option>\n                      <option value=\"modéré\">Modéré</option>\n                      <option value=\"difficile\">Difficile</option>\n                    </select>\n                  </div>\n                </div>\n\n                {/* Routes List */}\n                <div className=\"space-y-3 max-h-96 overflow-y-auto\">\n                  {filteredRoutes.map(route => (\n                    <div\n                      key={route.id}\n                      onClick={() => handleRouteSelect(route)}\n                      className={`p-4 border rounded-lg cursor-pointer transition-colors ${\n                        selectedRoute?.id === route.id\n                          ? 'border-blue-500 bg-blue-50'\n                          : 'border-gray-200 hover:border-gray-300'\n                      }`}\n                    >\n                      <div className=\"flex items-start justify-between mb-2\">\n                        <div className=\"flex items-center space-x-2\">\n                          <span className=\"text-lg\">{getTypeIcon(route.type)}</span>\n                          <h3 className=\"font-medium text-gray-900 text-sm\">{route.name}</h3>\n                        </div>\n                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(route.difficulty)}`}>\n                          {route.difficulty}\n                        </span>\n                      </div>\n                      \n                      <div className=\"text-sm text-gray-600 mb-2\">\n                        {route.description}\n                      </div>\n                      \n                      <div className=\"flex items-center justify-between text-xs text-gray-500 mb-3\">\n                        <span>{route.distance.toFixed(1)} km</span>\n                        <div className=\"flex items-center space-x-2\">\n                          <div className=\"flex items-center\">\n                            <FiStar className=\"h-3 w-3 text-yellow-400 mr-1\" />\n                            <span>{route.rating.toFixed(1)}</span>\n                          </div>\n                          <div className=\"flex items-center\">\n                            <FiUsers className=\"h-3 w-3 mr-1\" />\n                            <span>{route.completions}</span>\n                          </div>\n                        </div>\n                      </div>\n\n                      {/* Boutons d'action */}\n                      <div className=\"flex items-center space-x-2\" onClick={(e) => e.stopPropagation()}>\n                        <button\n                          onClick={() => startRoute(route)}\n                          className=\"flex-1 bg-green-600 text-white py-1 px-2 rounded text-xs hover:bg-green-700 transition-colors flex items-center justify-center\"\n                        >\n                          <FiPlay className=\"h-3 w-3 mr-1\" />\n                          Démarrer\n                        </button>\n\n                        {savedRoutes.find(r => r.id === route.id) ? (\n                          <button\n                            onClick={() => unsaveRoute(route.id)}\n                            className=\"bg-gray-600 text-white py-1 px-2 rounded text-xs hover:bg-gray-700 transition-colors\"\n                            title=\"Retirer des favoris\"\n                          >\n                            <FiBookmark className=\"h-3 w-3\" />\n                          </button>\n                        ) : (\n                          <button\n                            onClick={() => saveRoute(route)}\n                            className=\"bg-blue-600 text-white py-1 px-2 rounded text-xs hover:bg-blue-700 transition-colors\"\n                            title=\"Sauvegarder\"\n                          >\n                            <FiBookmark className=\"h-3 w-3\" />\n                          </button>\n                        )}\n\n                        {route.isCustom && (\n                          <button\n                            onClick={() => deleteCustomRoute(route.id)}\n                            className=\"bg-red-600 text-white py-1 px-2 rounded text-xs hover:bg-red-700 transition-colors\"\n                            title=\"Supprimer\"\n                          >\n                            <FiTrash2 className=\"h-3 w-3\" />\n                          </button>\n                        )}\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            )}\n\n            {activeTab === 'plan' && (\n              <div className=\"bg-white rounded-lg shadow-lg p-6\">\n                <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">\n                  Planificateur d'itinéraire\n                </h2>\n                \n                {!isPlanning ? (\n                  <div className=\"space-y-4\">\n                    <button\n                      onClick={startRoutePlanning}\n                      className=\"w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center\"\n                    >\n                      <FiPlus className=\"mr-2\" />\n                      Créer un nouveau parcours\n                    </button>\n                    \n                    <div className=\"text-sm text-gray-600\">\n                      <p className=\"mb-2\">Instructions :</p>\n                      <ul className=\"list-disc list-inside space-y-1\">\n                        <li>Cliquez sur \"Créer un nouveau parcours\"</li>\n                        <li>Cliquez sur la carte pour ajouter des points</li>\n                        <li>Minimum 2 points requis</li>\n                        <li>Cliquez sur \"Terminer\" pour sauvegarder</li>\n                      </ul>\n                    </div>\n                  </div>\n                ) : (\n                  <div className=\"space-y-4\">\n                    <div className=\"text-sm text-gray-600\">\n                      <p className=\"font-medium mb-2\">Mode planification actif</p>\n                      <p>Points ajoutés : {planningPoints.length}</p>\n                      {planningPoints.length >= 2 && (\n                        <p>Distance : {calculateTotalDistance(planningPoints).toFixed(1)} km</p>\n                      )}\n                    </div>\n                    \n                    <div className=\"flex space-x-2\">\n                      <button\n                        onClick={finishPlanning}\n                        disabled={planningPoints.length < 2}\n                        className=\"flex-1 bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors disabled:bg-gray-300 disabled:cursor-not-allowed\"\n                      >\n                        Terminer\n                      </button>\n                      <button\n                        onClick={() => {\n                          setIsPlanning(false);\n                          setPlanningPoints([]);\n                        }}\n                        className=\"flex-1 bg-gray-600 text-white py-2 px-4 rounded-lg hover:bg-gray-700 transition-colors\"\n                      >\n                        Annuler\n                      </button>\n                    </div>\n                  </div>\n                )}\n              </div>\n            )}\n\n            {activeTab === 'pois' && (\n              <div className=\"bg-white rounded-lg shadow-lg p-6\">\n                <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">\n                  Points d'intérêt\n                </h2>\n                \n                {selectedRoute && (\n                  <div className=\"mb-4 p-3 bg-blue-50 rounded-lg\">\n                    <p className=\"text-sm text-blue-700 mb-2\">\n                      Sélectionnez des POIs pour optimiser votre parcours\n                    </p>\n                    <button\n                      onClick={optimizeRoute}\n                      disabled={selectedPOIs.length === 0}\n                      className=\"w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors disabled:bg-gray-300 disabled:cursor-not-allowed text-sm\"\n                    >\n                      <FiTarget className=\"inline mr-2\" />\n                      Optimiser l'itinéraire ({selectedPOIs.length} POIs)\n                    </button>\n                  </div>\n                )}\n                \n                <div className=\"space-y-2 max-h-96 overflow-y-auto\">\n                  {pois.map(poi => (\n                    <div\n                      key={poi.id}\n                      onClick={() => handlePOIToggle(poi)}\n                      className={`p-3 border rounded-lg cursor-pointer transition-colors ${\n                        selectedPOIs.find(p => p.id === poi.id)\n                          ? 'border-blue-500 bg-blue-50'\n                          : 'border-gray-200 hover:border-gray-300'\n                      }`}\n                    >\n                      <div className=\"flex items-center space-x-3\">\n                        <span className=\"text-lg\">{poi.icon}</span>\n                        <div className=\"flex-1\">\n                          <h4 className=\"font-medium text-gray-900 text-sm\">{poi.name}</h4>\n                          <p className=\"text-xs text-gray-600\">{poi.description}</p>\n                          <div className=\"flex items-center mt-1\">\n                            <FiStar className=\"h-3 w-3 text-yellow-400 mr-1\" />\n                            <span className=\"text-xs text-gray-500\">{poi.rating.toFixed(1)}</span>\n                            {poi.verified && (\n                              <span className=\"ml-2 text-xs text-green-600\">✓ Vérifié</span>\n                            )}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            )}\n          </div>\n\n          {/* Map */}\n          <div className=\"lg:col-span-2\">\n            <div className=\"bg-white rounded-lg shadow-lg overflow-hidden\">\n              <div className=\"h-96 lg:h-[600px]\">\n                <MapContainer\n                  center={[userPosition.lat, userPosition.lng]}\n                  zoom={13}\n                  style={{ height: '100%', width: '100%' }}\n                >\n                  <TileLayer\n                    url=\"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png\"\n                    attribution='&copy; <a href=\"https://www.openstreetmap.org/copyright\">OpenStreetMap</a> contributors'\n                  />\n                  \n                  <MapClickHandler />\n                  \n                  {/* User position */}\n                  <Marker position={[userPosition.lat, userPosition.lng]}>\n                    <Popup>Votre position</Popup>\n                  </Marker>\n                  \n                  {/* Selected route */}\n                  {selectedRoute && (\n                    <Polyline\n                      positions={selectedRoute.points.map(p => [p.lat, p.lng])}\n                      color=\"blue\"\n                      weight={4}\n                      opacity={0.7}\n                    />\n                  )}\n                  \n                  {/* Optimized route */}\n                  {optimizedRoute && (\n                    <Polyline\n                      positions={optimizedRoute.map(p => [p.lat, p.lng])}\n                      color=\"red\"\n                      weight={4}\n                      opacity={0.8}\n                    />\n                  )}\n                  \n                  {/* Planning points */}\n                  {planningPoints.map((point, index) => (\n                    <Marker key={index} position={[point.lat, point.lng]}>\n                      <Popup>Point {index + 1}</Popup>\n                    </Marker>\n                  ))}\n                  \n                  {/* Planning route */}\n                  {planningPoints.length > 1 && (\n                    <Polyline\n                      positions={planningPoints.map(p => [p.lat, p.lng])}\n                      color=\"green\"\n                      weight={4}\n                      opacity={0.7}\n                    />\n                  )}\n                  \n                  {/* POIs */}\n                  {showPOIs && pois.map(poi => (\n                    <Marker\n                      key={poi.id}\n                      position={[poi.lat, poi.lng]}\n                      icon={L.divIcon({\n                        html: `<div style=\"background: ${poi.color}; color: white; border-radius: 50%; width: 30px; height: 30px; display: flex; align-items: center; justify-content: center; font-size: 16px;\">${poi.icon}</div>`,\n                        className: 'custom-poi-marker',\n                        iconSize: [30, 30]\n                      })}\n                    >\n                      <Popup>\n                        <div>\n                          <h4 className=\"font-medium\">{poi.name}</h4>\n                          <p className=\"text-sm text-gray-600\">{poi.description}</p>\n                          <div className=\"flex items-center mt-1\">\n                            <FiStar className=\"h-3 w-3 text-yellow-400 mr-1\" />\n                            <span className=\"text-sm\">{poi.rating.toFixed(1)}</span>\n                          </div>\n                        </div>\n                      </Popup>\n                    </Marker>\n                  ))}\n                  \n                  {/* Selected POIs highlight */}\n                  {selectedPOIs.map(poi => (\n                    <Marker\n                      key={`selected-${poi.id}`}\n                      position={[poi.lat, poi.lng]}\n                      icon={L.divIcon({\n                        html: `<div style=\"background: red; color: white; border-radius: 50%; width: 35px; height: 35px; display: flex; align-items: center; justify-content: center; font-size: 18px; border: 3px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.3);\">${poi.icon}</div>`,\n                        className: 'custom-selected-poi-marker',\n                        iconSize: [35, 35]\n                      })}\n                    />\n                  ))}\n                </MapContainer>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Routes;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,YAAY,EAAEC,SAAS,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,YAAY,QAAQ,eAAe;AAC9F,OAAOC,CAAC,MAAM,SAAS;AACvB,SACEC,KAAK,EACLC,YAAY,EACZC,QAAQ,EACRC,QAAQ,EACRC,MAAM,EACNC,MAAM,EACNC,OAAO,EACPC,QAAQ,EACRC,UAAU,EACVC,QAAQ,EACRC,MAAM,EACNC,OAAO,EACPC,aAAa,QACR,gBAAgB;AACvB,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SACEC,qBAAqB,EACrBC,YAAY,EACZC,iBAAiB,EACjBC,qBAAqB,EACrBC,gBAAgB,QACX,mBAAmB;;AAE1B;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,OAAOrB,CAAC,CAACsB,IAAI,CAACC,OAAO,CAACC,SAAS,CAACC,WAAW;AAC3CzB,CAAC,CAACsB,IAAI,CAACC,OAAO,CAACG,YAAY,CAAC;EAC1BC,aAAa,EAAE,gFAAgF;EAC/FC,OAAO,EAAE,6EAA6E;EACtFC,SAAS,EAAE;AACb,CAAC,CAAC;AAEF,MAAMC,MAAM,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAAA,IAAAC,EAAA,GAAAC,YAAA;EACnB,MAAM;IAAEC;EAAK,CAAC,GAAGpB,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACqB,YAAY,EAAEC,eAAe,CAAC,GAAG5C,QAAQ,CAAC2B,gBAAgB,CAAC;EAClE,MAAM,CAACkB,MAAM,EAAEC,SAAS,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAAC+C,IAAI,EAAEC,OAAO,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACiD,aAAa,EAAEC,gBAAgB,CAAC,GAAGlD,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACmD,YAAY,EAAEC,eAAe,CAAC,GAAGpD,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACqD,cAAc,EAAEC,iBAAiB,CAAC,GAAGtD,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACuD,UAAU,EAAEC,aAAa,CAAC,GAAGxD,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACyD,cAAc,EAAEC,iBAAiB,CAAC,GAAG1D,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC2D,OAAO,EAAEC,UAAU,CAAC,GAAG5D,QAAQ,CAAC;IACrC6D,IAAI,EAAE,KAAK;IACXC,UAAU,EAAE,KAAK;IACjBC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGjE,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACkE,QAAQ,EAAEC,WAAW,CAAC,GAAGnE,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACoE,SAAS,EAAEC,YAAY,CAAC,GAAGrE,QAAQ,CAAC,UAAU,CAAC;EACtD,MAAM,CAACsE,WAAW,EAAEC,cAAc,CAAC,GAAGvE,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACwE,YAAY,EAAEC,eAAe,CAAC,GAAGzE,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC0E,SAAS,EAAEC,YAAY,CAAC,GAAG3E,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAAC4E,KAAK,EAAEC,QAAQ,CAAC,GAAG7E,QAAQ,CAAC,IAAI,CAAC;EAExCC,SAAS,CAAC,MAAM;IACd;IACA,IAAI;MACF,MAAM6E,KAAK,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC;MACrE,MAAMC,OAAO,GAAGJ,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC,IAAI,IAAI,CAAC;MACxEX,cAAc,CAACO,KAAK,CAAC;MACrBL,eAAe,CAACU,OAAO,CAAC;IAC1B,CAAC,CAAC,OAAOP,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;IAChE;EACF,CAAC,EAAE,EAAE,CAAC;EAEN3E,SAAS,CAAC,MAAM;IACd0E,YAAY,CAAC,IAAI,CAAC;IAClBE,QAAQ,CAAC,IAAI,CAAC;;IAEd;IACA,IAAIQ,SAAS,CAACC,WAAW,EAAE;MACzBD,SAAS,CAACC,WAAW,CAACC,kBAAkB,CACrCC,QAAQ,IAAK;QACZ,MAAMC,GAAG,GAAG;UACVC,GAAG,EAAEF,QAAQ,CAACG,MAAM,CAACC,QAAQ;UAC7BC,GAAG,EAAEL,QAAQ,CAACG,MAAM,CAACG;QACvB,CAAC;QACDlD,eAAe,CAAC6C,GAAG,CAAC;QACpBM,iBAAiB,CAACN,GAAG,CAAC;MACxB,CAAC,EACAb,KAAK,IAAK;QACTQ,OAAO,CAACY,IAAI,CAAC,0BAA0B,EAAEpB,KAAK,CAAC;QAC/CC,QAAQ,CAAC,8EAA8E,CAAC;QACxFkB,iBAAiB,CAACpE,gBAAgB,CAAC;MACrC,CAAC,EACD;QACEsE,kBAAkB,EAAE,IAAI;QACxBC,OAAO,EAAE,KAAK;QACdC,UAAU,EAAE,MAAM,CAAC;MACrB,CACF,CAAC;IACH,CAAC,MAAM;MACLtB,QAAQ,CAAC,qDAAqD,CAAC;MAC/DkB,iBAAiB,CAACpE,gBAAgB,CAAC;IACrC;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMoE,iBAAiB,GAAIP,QAAQ,IAAK;IACtC,IAAI;MACF,MAAMY,aAAa,GAAG7E,qBAAqB,CAACiE,QAAQ,CAACE,GAAG,EAAEF,QAAQ,CAACK,GAAG,EAAE,EAAE,CAAC;MAC3E,MAAMQ,UAAU,GAAG7E,YAAY,CAACgE,QAAQ,CAACE,GAAG,EAAEF,QAAQ,CAACK,GAAG,EAAE,EAAE,CAAC;;MAE/D;MACA,MAAMS,SAAS,GAAG,CAAC,GAAGhC,WAAW,EAAE,GAAG8B,aAAa,CAAC;MACpDtD,SAAS,CAACwD,SAAS,CAAC;MACpBtD,OAAO,CAACqD,UAAU,CAAC;MACnB1B,YAAY,CAAC,KAAK,CAAC;IACrB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC7DC,QAAQ,CAAC,iDAAiD,CAAC;MAC3DF,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAM4B,cAAc,GAAG1D,MAAM,CAAC2D,MAAM,CAACC,KAAK,IAAI;IAC5C,MAAMC,aAAa,GAAGD,KAAK,CAACE,IAAI,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC7C,UAAU,CAAC4C,WAAW,CAAC,CAAC,CAAC,IAC5DH,KAAK,CAACK,WAAW,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC7C,UAAU,CAAC4C,WAAW,CAAC,CAAC,CAAC;IACvF,MAAMG,WAAW,GAAGpD,OAAO,CAACE,IAAI,KAAK,KAAK,IAAI4C,KAAK,CAAC5C,IAAI,KAAKF,OAAO,CAACE,IAAI;IACzE,MAAMmD,iBAAiB,GAAGrD,OAAO,CAACG,UAAU,KAAK,KAAK,IAAI2C,KAAK,CAAC3C,UAAU,KAAKH,OAAO,CAACG,UAAU;IACjG,MAAMmD,eAAe,GAAGtD,OAAO,CAACI,QAAQ,KAAK,KAAK,IAC1BJ,OAAO,CAACI,QAAQ,KAAK,OAAO,IAAI0C,KAAK,CAAC1C,QAAQ,IAAI,CAAE,IACpDJ,OAAO,CAACI,QAAQ,KAAK,QAAQ,IAAI0C,KAAK,CAAC1C,QAAQ,GAAG,CAAC,IAAI0C,KAAK,CAAC1C,QAAQ,IAAI,EAAG,IAC5EJ,OAAO,CAACI,QAAQ,KAAK,MAAM,IAAI0C,KAAK,CAAC1C,QAAQ,GAAG,EAAG;IAE3E,OAAO2C,aAAa,IAAIK,WAAW,IAAIC,iBAAiB,IAAIC,eAAe;EAC7E,CAAC,CAAC;EAEF,MAAMC,iBAAiB,GAAIT,KAAK,IAAK;IACnCvD,gBAAgB,CAACuD,KAAK,CAAC;IACvBnD,iBAAiB,CAAC,IAAI,CAAC;IACvBF,eAAe,CAAC,EAAE,CAAC;EACrB,CAAC;EAED,MAAM+D,eAAe,GAAIC,GAAG,IAAK;IAC/BhE,eAAe,CAACiE,IAAI,IAAI;MACtB,MAAMC,UAAU,GAAGD,IAAI,CAACE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKL,GAAG,CAACK,EAAE,CAAC;MAClD,IAAIH,UAAU,EAAE;QACd,OAAOD,IAAI,CAACb,MAAM,CAACgB,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKL,GAAG,CAACK,EAAE,CAAC;MAC1C,CAAC,MAAM;QACL,OAAO,CAAC,GAAGJ,IAAI,EAAED,GAAG,CAAC;MACvB;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMM,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAIzE,aAAa,IAAIE,YAAY,CAACwE,MAAM,GAAG,CAAC,EAAE;MAC5C,MAAMC,UAAU,GAAG3E,aAAa,CAAC4E,MAAM,CAAC,CAAC,CAAC;MAC1C,MAAMC,QAAQ,GAAG7E,aAAa,CAAC4E,MAAM,CAAC5E,aAAa,CAAC4E,MAAM,CAACF,MAAM,GAAG,CAAC,CAAC;MACtE,MAAMI,SAAS,GAAGrG,qBAAqB,CAACkG,UAAU,EAAEE,QAAQ,EAAE3E,YAAY,CAAC;MAC3EG,iBAAiB,CAACyE,SAAS,CAAC;IAC9B;EACF,CAAC;EAED,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/BxE,aAAa,CAAC,IAAI,CAAC;IACnBE,iBAAiB,CAAC,EAAE,CAAC;IACrBR,gBAAgB,CAAC,IAAI,CAAC;IACtBI,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAM2E,eAAe,GAAGA,CAAA,KAAM;IAAAzF,EAAA;IAC5BjC,YAAY,CAAC;MACX2H,KAAK,EAAGC,CAAC,IAAK;QACZ,IAAI5E,UAAU,EAAE;UACd,MAAM6E,QAAQ,GAAG;YACf1C,GAAG,EAAEyC,CAAC,CAACE,MAAM,CAAC3C,GAAG;YACjBG,GAAG,EAAEsC,CAAC,CAACE,MAAM,CAACxC,GAAG;YACjByC,SAAS,EAAE,GAAG,CAAC;UACjB,CAAC;UACD5E,iBAAiB,CAAC2D,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEe,QAAQ,CAAC,CAAC;QAChD;MACF;IACF,CAAC,CAAC;IACF,OAAO,IAAI;EACb,CAAC;EAAC5F,EAAA,CAdIyF,eAAe;IAAA,QACnB1H,YAAY;EAAA;EAed,MAAMgI,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAI9E,cAAc,CAACkE,MAAM,IAAI,CAAC,EAAE;MAC9B,MAAMa,QAAQ,GAAG;QACff,EAAE,EAAE,UAAUgB,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;QAC1B/B,IAAI,EAAE,2BAA2B;QACjC9C,IAAI,EAAE,SAAS;QACfE,QAAQ,EAAE4E,sBAAsB,CAAClF,cAAc,CAAC;QAChDoE,MAAM,EAAEpE,cAAc;QACtBK,UAAU,EAAE,QAAQ;QACpB8E,MAAM,EAAE,CAAC;QACTC,WAAW,EAAE,CAAC;QACdC,SAAS,EAAE,CAAApG,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqG,SAAS,KAAI,KAAK;QACnCC,IAAI,EAAE,CAAC,QAAQ,CAAC;QAChBlC,WAAW,EAAE,kCAAkC;QAC/CmC,SAAS,EAAE,IAAIR,IAAI,CAAC,CAAC,CAACS,WAAW,CAAC,CAAC;QACnCC,QAAQ,EAAE;MACZ,CAAC;;MAED;MACA,MAAMC,kBAAkB,GAAG,CAACZ,QAAQ,EAAE,GAAGlE,WAAW,CAAC;MACrDC,cAAc,CAAC6E,kBAAkB,CAAC;MAClCnE,YAAY,CAACoE,OAAO,CAAC,aAAa,EAAEtE,IAAI,CAACuE,SAAS,CAACF,kBAAkB,CAAC,CAAC;MAEvEtG,SAAS,CAACuE,IAAI,IAAI,CAACmB,QAAQ,EAAE,GAAGnB,IAAI,CAAC,CAAC;MACtCnE,gBAAgB,CAACsF,QAAQ,CAAC;MAC1BhF,aAAa,CAAC,KAAK,CAAC;MACpBE,iBAAiB,CAAC,EAAE,CAAC;IACvB;EACF,CAAC;EAED,MAAMiF,sBAAsB,GAAId,MAAM,IAAK;IACzC,IAAI0B,KAAK,GAAG,CAAC;IACb,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG3B,MAAM,CAACF,MAAM,EAAE6B,CAAC,EAAE,EAAE;MACtCD,KAAK,IAAI9H,iBAAiB,CACxBoG,MAAM,CAAC2B,CAAC,GAAC,CAAC,CAAC,CAAC9D,GAAG,EAAEmC,MAAM,CAAC2B,CAAC,GAAC,CAAC,CAAC,CAAC3D,GAAG,EAChCgC,MAAM,CAAC2B,CAAC,CAAC,CAAC9D,GAAG,EAAEmC,MAAM,CAAC2B,CAAC,CAAC,CAAC3D,GAC3B,CAAC;IACH;IACA,OAAO0D,KAAK;EACd,CAAC;EAED,MAAME,kBAAkB,GAAI3F,UAAU,IAAK;IACzC,QAAQA,UAAU;MAChB,KAAK,QAAQ;QAAE,OAAO,6BAA6B;MACnD,KAAK,QAAQ;QAAE,OAAO,+BAA+B;MACrD,KAAK,WAAW;QAAE,OAAO,yBAAyB;MAClD;QAAS,OAAO,2BAA2B;IAC7C;EACF,CAAC;EAED,MAAM4F,WAAW,GAAI7F,IAAI,IAAK;IAC5B,QAAQA,IAAI;MACV,KAAK,SAAS;QAAE,OAAO,OAAO;MAC9B,KAAK,SAAS;QAAE,OAAO,OAAO;MAC9B,KAAK,QAAQ;QAAE,OAAO,IAAI;MAC1B,KAAK,SAAS;QAAE,OAAO,OAAO;MAC9B;QAAS,OAAO,IAAI;IACtB;EACF,CAAC;EAED,MAAM8F,SAAS,GAAIlD,KAAK,IAAK;IAC3B,IAAI,CAACnC,WAAW,CAACiD,IAAI,CAACqC,CAAC,IAAIA,CAAC,CAACnC,EAAE,KAAKhB,KAAK,CAACgB,EAAE,CAAC,EAAE;MAC7C,MAAMoC,WAAW,GAAG;QAAE,GAAGpD,KAAK;QAAEqD,OAAO,EAAE,IAAIrB,IAAI,CAAC,CAAC,CAACS,WAAW,CAAC;MAAE,CAAC;MACnE,MAAME,kBAAkB,GAAG,CAACS,WAAW,EAAE,GAAGvF,WAAW,CAAC;MACxDC,cAAc,CAAC6E,kBAAkB,CAAC;MAClCnE,YAAY,CAACoE,OAAO,CAAC,aAAa,EAAEtE,IAAI,CAACuE,SAAS,CAACF,kBAAkB,CAAC,CAAC;IACzE;EACF,CAAC;EAED,MAAMW,WAAW,GAAIC,OAAO,IAAK;IAC/B,MAAMZ,kBAAkB,GAAG9E,WAAW,CAACkC,MAAM,CAACoD,CAAC,IAAIA,CAAC,CAACnC,EAAE,KAAKuC,OAAO,CAAC;IACpEzF,cAAc,CAAC6E,kBAAkB,CAAC;IAClCnE,YAAY,CAACoE,OAAO,CAAC,aAAa,EAAEtE,IAAI,CAACuE,SAAS,CAACF,kBAAkB,CAAC,CAAC;EACzE,CAAC;EAED,MAAMa,iBAAiB,GAAID,OAAO,IAAK;IACrC;IACA,MAAMZ,kBAAkB,GAAG9E,WAAW,CAACkC,MAAM,CAACoD,CAAC,IAAIA,CAAC,CAACnC,EAAE,KAAKuC,OAAO,CAAC;IACpEzF,cAAc,CAAC6E,kBAAkB,CAAC;IAClCnE,YAAY,CAACoE,OAAO,CAAC,aAAa,EAAEtE,IAAI,CAACuE,SAAS,CAACF,kBAAkB,CAAC,CAAC;;IAEvE;IACAtG,SAAS,CAACuE,IAAI,IAAIA,IAAI,CAACb,MAAM,CAACoD,CAAC,IAAIA,CAAC,CAACnC,EAAE,KAAKuC,OAAO,CAAC,CAAC;;IAErD;IACA,IAAI,CAAA/G,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEwE,EAAE,MAAKuC,OAAO,EAAE;MACjC9G,gBAAgB,CAAC,IAAI,CAAC;IACxB;EACF,CAAC;EAED,MAAMgH,UAAU,GAAIzD,KAAK,IAAK;IAC5B,MAAM0D,YAAY,GAAG;MACnB1C,EAAE,EAAEgB,IAAI,CAACC,GAAG,CAAC,CAAC;MACdjC,KAAK,EAAEA,KAAK;MACZ2D,SAAS,EAAE,IAAI3B,IAAI,CAAC,CAAC,CAACS,WAAW,CAAC,CAAC;MACnCmB,MAAM,EAAE;IACV,CAAC;IAED,MAAMC,cAAc,GAAG,CAACH,YAAY,EAAE,GAAG3F,YAAY,CAAC;IACtDC,eAAe,CAAC6F,cAAc,CAAC;IAC/BrF,YAAY,CAACoE,OAAO,CAAC,cAAc,EAAEtE,IAAI,CAACuE,SAAS,CAACgB,cAAc,CAAC,CAAC;EACtE,CAAC;EAED,oBACEzI,OAAA;IAAK0I,SAAS,EAAC,yBAAyB;IAAAC,QAAA,eACtC3I,OAAA;MAAK0I,SAAS,EAAC,6CAA6C;MAAAC,QAAA,gBAE1D3I,OAAA;QAAK0I,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/B3I,OAAA;UAAI0I,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAEtD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL/I,OAAA;UAAG0I,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EAAC;QAGvD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,EAGHhG,KAAK,iBACJ/C,OAAA;UAAK0I,SAAS,EAAC,4EAA4E;UAAAC,QAAA,eACzF3I,OAAA;YAAK0I,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChC3I,OAAA,CAACR,aAAa;cAACkJ,SAAS,EAAC;YAA8B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1D/I,OAAA;cAAG0I,SAAS,EAAC,yBAAyB;cAAAC,QAAA,EAAE5F;YAAK;cAAA6F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAGAlG,SAAS,iBACR7C,OAAA;UAAK0I,SAAS,EAAC,wEAAwE;UAAAC,QAAA,eACrF3I,OAAA;YAAK0I,SAAS,EAAC,kCAAkC;YAAAC,QAAA,gBAC/C3I,OAAA;cAAK0I,SAAS,EAAC;YAAmE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzF/I,OAAA;cAAG0I,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAyB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGN/I,OAAA;QAAK0I,SAAS,EAAC,gDAAgD;QAAAC,QAAA,gBAC7D3I,OAAA;UACEgJ,OAAO,EAAEA,CAAA,KAAMxG,YAAY,CAAC,UAAU,CAAE;UACxCkG,SAAS,EAAE,qEACTnG,SAAS,KAAK,UAAU,GACpB,kCAAkC,GAClC,mCAAmC,EACtC;UAAAoG,QAAA,gBAEH3I,OAAA,CAACpB,KAAK;YAAC8J,SAAS,EAAC;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAEnC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT/I,OAAA;UACEgJ,OAAO,EAAEA,CAAA,KAAMxG,YAAY,CAAC,OAAO,CAAE;UACrCkG,SAAS,EAAE,qEACTnG,SAAS,KAAK,OAAO,GACjB,kCAAkC,GAClC,mCAAmC,EACtC;UAAAoG,QAAA,gBAEH3I,OAAA,CAACZ,UAAU;YAACsJ,SAAS,EAAC;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,qBACxB,EAACtG,WAAW,CAACqD,MAAM,EAAC,GACpC;QAAA;UAAA8C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT/I,OAAA;UACEgJ,OAAO,EAAEA,CAAA,KAAMxG,YAAY,CAAC,MAAM,CAAE;UACpCkG,SAAS,EAAE,qEACTnG,SAAS,KAAK,MAAM,GAChB,kCAAkC,GAClC,mCAAmC,EACtC;UAAAoG,QAAA,gBAEH3I,OAAA,CAACnB,YAAY;YAAC6J,SAAS,EAAC;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,aAE1C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT/I,OAAA;UACEgJ,OAAO,EAAEA,CAAA,KAAMxG,YAAY,CAAC,MAAM,CAAE;UACpCkG,SAAS,EAAE,qEACTnG,SAAS,KAAK,MAAM,GAChB,kCAAkC,GAClC,mCAAmC,EACtC;UAAAoG,QAAA,gBAEH3I,OAAA,CAAClB,QAAQ;YAAC4J,SAAS,EAAC;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,0BAEtC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAEN/I,OAAA;QAAK0I,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBAEpD3I,OAAA;UAAK0I,SAAS,EAAC,eAAe;UAAAC,QAAA,GAC3BpG,SAAS,KAAK,UAAU,iBACvBvC,OAAA;YAAK0I,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChD3I,OAAA;cAAK0I,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrD3I,OAAA;gBAAI0I,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,EAAC;cAEpD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL/I,OAAA;gBACEgJ,OAAO,EAAEA,CAAA,KAAM1G,WAAW,CAAC,CAACD,QAAQ,CAAE;gBACtCqG,SAAS,EAAE,gCACTrG,QAAQ,GAAG,2BAA2B,GAAG,2BAA2B,EACnE;gBAAAsG,QAAA,EACJ;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAGN/I,OAAA;cAAK0I,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7B3I,OAAA;gBAAK0I,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACvB3I,OAAA,CAACjB,QAAQ;kBAAC2J,SAAS,EAAC;gBAA6C;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACpE/I,OAAA;kBACEgC,IAAI,EAAC,MAAM;kBACXiH,WAAW,EAAC,2BAA2B;kBACvCC,KAAK,EAAE/G,UAAW;kBAClBgH,QAAQ,EAAG7C,CAAC,IAAKlE,aAAa,CAACkE,CAAC,CAAC8C,MAAM,CAACF,KAAK,CAAE;kBAC/CR,SAAS,EAAC;gBAA8G;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAEN/I,OAAA;gBAAK0I,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACrC3I,OAAA;kBACEkJ,KAAK,EAAEpH,OAAO,CAACE,IAAK;kBACpBmH,QAAQ,EAAG7C,CAAC,IAAKvE,UAAU,CAACyD,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAExD,IAAI,EAAEsE,CAAC,CAAC8C,MAAM,CAACF;kBAAM,CAAC,CAAC,CAAE;kBACzER,SAAS,EAAC,yGAAyG;kBAAAC,QAAA,gBAEnH3I,OAAA;oBAAQkJ,KAAK,EAAC,KAAK;oBAAAP,QAAA,EAAC;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACvC/I,OAAA;oBAAQkJ,KAAK,EAAC,SAAS;oBAAAP,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACvC/I,OAAA;oBAAQkJ,KAAK,EAAC,SAAS;oBAAAP,QAAA,EAAC;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACrC/I,OAAA;oBAAQkJ,KAAK,EAAC,QAAQ;oBAAAP,QAAA,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACzC/I,OAAA;oBAAQkJ,KAAK,EAAC,SAAS;oBAAAP,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC,CAAC,eAET/I,OAAA;kBACEkJ,KAAK,EAAEpH,OAAO,CAACG,UAAW;kBAC1BkH,QAAQ,EAAG7C,CAAC,IAAKvE,UAAU,CAACyD,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAEvD,UAAU,EAAEqE,CAAC,CAAC8C,MAAM,CAACF;kBAAM,CAAC,CAAC,CAAE;kBAC/ER,SAAS,EAAC,yGAAyG;kBAAAC,QAAA,gBAEnH3I,OAAA;oBAAQkJ,KAAK,EAAC,KAAK;oBAAAP,QAAA,EAAC;kBAAkB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC/C/I,OAAA;oBAAQkJ,KAAK,EAAC,QAAQ;oBAAAP,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACtC/I,OAAA;oBAAQkJ,KAAK,EAAC,cAAQ;oBAAAP,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACtC/I,OAAA;oBAAQkJ,KAAK,EAAC,WAAW;oBAAAP,QAAA,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN/I,OAAA;cAAK0I,SAAS,EAAC,oCAAoC;cAAAC,QAAA,EAChDjE,cAAc,CAAC2E,GAAG,CAACzE,KAAK,iBACvB5E,OAAA;gBAEEgJ,OAAO,EAAEA,CAAA,KAAM3D,iBAAiB,CAACT,KAAK,CAAE;gBACxC8D,SAAS,EAAE,0DACT,CAAAtH,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEwE,EAAE,MAAKhB,KAAK,CAACgB,EAAE,GAC1B,4BAA4B,GAC5B,uCAAuC,EAC1C;gBAAA+C,QAAA,gBAEH3I,OAAA;kBAAK0I,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,gBACpD3I,OAAA;oBAAK0I,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,gBAC1C3I,OAAA;sBAAM0I,SAAS,EAAC,SAAS;sBAAAC,QAAA,EAAEd,WAAW,CAACjD,KAAK,CAAC5C,IAAI;oBAAC;sBAAA4G,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAC1D/I,OAAA;sBAAI0I,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAE/D,KAAK,CAACE;oBAAI;sBAAA8D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChE,CAAC,eACN/I,OAAA;oBAAM0I,SAAS,EAAE,8CAA8Cd,kBAAkB,CAAChD,KAAK,CAAC3C,UAAU,CAAC,EAAG;oBAAA0G,QAAA,EACnG/D,KAAK,CAAC3C;kBAAU;oBAAA2G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eAEN/I,OAAA;kBAAK0I,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EACxC/D,KAAK,CAACK;gBAAW;kBAAA2D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC,eAEN/I,OAAA;kBAAK0I,SAAS,EAAC,8DAA8D;kBAAAC,QAAA,gBAC3E3I,OAAA;oBAAA2I,QAAA,GAAO/D,KAAK,CAAC1C,QAAQ,CAACoH,OAAO,CAAC,CAAC,CAAC,EAAC,KAAG;kBAAA;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC3C/I,OAAA;oBAAK0I,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,gBAC1C3I,OAAA;sBAAK0I,SAAS,EAAC,mBAAmB;sBAAAC,QAAA,gBAChC3I,OAAA,CAACf,MAAM;wBAACyJ,SAAS,EAAC;sBAA8B;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACnD/I,OAAA;wBAAA2I,QAAA,EAAO/D,KAAK,CAACmC,MAAM,CAACuC,OAAO,CAAC,CAAC;sBAAC;wBAAAV,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnC,CAAC,eACN/I,OAAA;sBAAK0I,SAAS,EAAC,mBAAmB;sBAAAC,QAAA,gBAChC3I,OAAA,CAACd,OAAO;wBAACwJ,SAAS,EAAC;sBAAc;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACpC/I,OAAA;wBAAA2I,QAAA,EAAO/D,KAAK,CAACoC;sBAAW;wBAAA4B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGN/I,OAAA;kBAAK0I,SAAS,EAAC,6BAA6B;kBAACM,OAAO,EAAG1C,CAAC,IAAKA,CAAC,CAACiD,eAAe,CAAC,CAAE;kBAAAZ,QAAA,gBAC/E3I,OAAA;oBACEgJ,OAAO,EAAEA,CAAA,KAAMX,UAAU,CAACzD,KAAK,CAAE;oBACjC8D,SAAS,EAAC,gIAAgI;oBAAAC,QAAA,gBAE1I3I,OAAA,CAACV,MAAM;sBAACoJ,SAAS,EAAC;oBAAc;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAErC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EAERtG,WAAW,CAACiD,IAAI,CAACqC,CAAC,IAAIA,CAAC,CAACnC,EAAE,KAAKhB,KAAK,CAACgB,EAAE,CAAC,gBACvC5F,OAAA;oBACEgJ,OAAO,EAAEA,CAAA,KAAMd,WAAW,CAACtD,KAAK,CAACgB,EAAE,CAAE;oBACrC8C,SAAS,EAAC,sFAAsF;oBAChGc,KAAK,EAAC,qBAAqB;oBAAAb,QAAA,eAE3B3I,OAAA,CAACZ,UAAU;sBAACsJ,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC,gBAET/I,OAAA;oBACEgJ,OAAO,EAAEA,CAAA,KAAMlB,SAAS,CAAClD,KAAK,CAAE;oBAChC8D,SAAS,EAAC,sFAAsF;oBAChGc,KAAK,EAAC,aAAa;oBAAAb,QAAA,eAEnB3I,OAAA,CAACZ,UAAU;sBAACsJ,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CACT,EAEAnE,KAAK,CAAC0C,QAAQ,iBACbtH,OAAA;oBACEgJ,OAAO,EAAEA,CAAA,KAAMZ,iBAAiB,CAACxD,KAAK,CAACgB,EAAE,CAAE;oBAC3C8C,SAAS,EAAC,oFAAoF;oBAC9Fc,KAAK,EAAC,WAAW;oBAAAb,QAAA,eAEjB3I,OAAA,CAACX,QAAQ;sBAACqJ,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B,CACT;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA,GAzEDnE,KAAK,CAACgB,EAAE;gBAAAgD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA0EV,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAEAxG,SAAS,KAAK,MAAM,iBACnBvC,OAAA;YAAK0I,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChD3I,OAAA;cAAI0I,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAEzD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAEJ,CAACrH,UAAU,gBACV1B,OAAA;cAAK0I,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxB3I,OAAA;gBACEgJ,OAAO,EAAE7C,kBAAmB;gBAC5BuC,SAAS,EAAC,yHAAyH;gBAAAC,QAAA,gBAEnI3I,OAAA,CAAChB,MAAM;kBAAC0J,SAAS,EAAC;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gCAE7B;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAET/I,OAAA;gBAAK0I,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,gBACpC3I,OAAA;kBAAG0I,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACtC/I,OAAA;kBAAI0I,SAAS,EAAC,iCAAiC;kBAAAC,QAAA,gBAC7C3I,OAAA;oBAAA2I,QAAA,EAAI;kBAAuC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAChD/I,OAAA;oBAAA2I,QAAA,EAAI;kBAA4C;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACrD/I,OAAA;oBAAA2I,QAAA,EAAI;kBAAuB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAChC/I,OAAA;oBAAA2I,QAAA,EAAI;kBAAuC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,gBAEN/I,OAAA;cAAK0I,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxB3I,OAAA;gBAAK0I,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,gBACpC3I,OAAA;kBAAG0I,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,EAAC;gBAAwB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAC5D/I,OAAA;kBAAA2I,QAAA,GAAG,sBAAiB,EAAC/G,cAAc,CAACkE,MAAM;gBAAA;kBAAA8C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EAC9CnH,cAAc,CAACkE,MAAM,IAAI,CAAC,iBACzB9F,OAAA;kBAAA2I,QAAA,GAAG,aAAW,EAAC7B,sBAAsB,CAAClF,cAAc,CAAC,CAAC0H,OAAO,CAAC,CAAC,CAAC,EAAC,KAAG;gBAAA;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CACxE;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEN/I,OAAA;gBAAK0I,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7B3I,OAAA;kBACEgJ,OAAO,EAAEtC,cAAe;kBACxB+C,QAAQ,EAAE7H,cAAc,CAACkE,MAAM,GAAG,CAAE;kBACpC4C,SAAS,EAAC,2IAA2I;kBAAAC,QAAA,EACtJ;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT/I,OAAA;kBACEgJ,OAAO,EAAEA,CAAA,KAAM;oBACbrH,aAAa,CAAC,KAAK,CAAC;oBACpBE,iBAAiB,CAAC,EAAE,CAAC;kBACvB,CAAE;kBACF6G,SAAS,EAAC,wFAAwF;kBAAAC,QAAA,EACnG;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN,EAEAxG,SAAS,KAAK,MAAM,iBACnBvC,OAAA;YAAK0I,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChD3I,OAAA;cAAI0I,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAEzD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAEJ3H,aAAa,iBACZpB,OAAA;cAAK0I,SAAS,EAAC,gCAAgC;cAAAC,QAAA,gBAC7C3I,OAAA;gBAAG0I,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAE1C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJ/I,OAAA;gBACEgJ,OAAO,EAAEnD,aAAc;gBACvB4D,QAAQ,EAAEnI,YAAY,CAACwE,MAAM,KAAK,CAAE;gBACpC4C,SAAS,EAAC,iJAAiJ;gBAAAC,QAAA,gBAE3J3I,OAAA,CAACb,QAAQ;kBAACuJ,SAAS,EAAC;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,+BACZ,EAACzH,YAAY,CAACwE,MAAM,EAAC,QAC/C;cAAA;gBAAA8C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CACN,eAED/I,OAAA;cAAK0I,SAAS,EAAC,oCAAoC;cAAAC,QAAA,EAChDzH,IAAI,CAACmI,GAAG,CAAC9D,GAAG,iBACXvF,OAAA;gBAEEgJ,OAAO,EAAEA,CAAA,KAAM1D,eAAe,CAACC,GAAG,CAAE;gBACpCmD,SAAS,EAAE,0DACTpH,YAAY,CAACoE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKL,GAAG,CAACK,EAAE,CAAC,GACnC,4BAA4B,GAC5B,uCAAuC,EAC1C;gBAAA+C,QAAA,eAEH3I,OAAA;kBAAK0I,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1C3I,OAAA;oBAAM0I,SAAS,EAAC,SAAS;oBAAAC,QAAA,EAAEpD,GAAG,CAACmE;kBAAI;oBAAAd,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC3C/I,OAAA;oBAAK0I,SAAS,EAAC,QAAQ;oBAAAC,QAAA,gBACrB3I,OAAA;sBAAI0I,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAEpD,GAAG,CAACT;oBAAI;sBAAA8D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACjE/I,OAAA;sBAAG0I,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAEpD,GAAG,CAACN;oBAAW;sBAAA2D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC1D/I,OAAA;sBAAK0I,SAAS,EAAC,wBAAwB;sBAAAC,QAAA,gBACrC3I,OAAA,CAACf,MAAM;wBAACyJ,SAAS,EAAC;sBAA8B;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACnD/I,OAAA;wBAAM0I,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EAAEpD,GAAG,CAACwB,MAAM,CAACuC,OAAO,CAAC,CAAC;sBAAC;wBAAAV,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,EACrExD,GAAG,CAACoE,QAAQ,iBACX3J,OAAA;wBAAM0I,SAAS,EAAC,6BAA6B;wBAAAC,QAAA,EAAC;sBAAS;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAC9D;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC,GArBDxD,GAAG,CAACK,EAAE;gBAAAgD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAsBR,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGN/I,OAAA;UAAK0I,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5B3I,OAAA;YAAK0I,SAAS,EAAC,+CAA+C;YAAAC,QAAA,eAC5D3I,OAAA;cAAK0I,SAAS,EAAC,mBAAmB;cAAAC,QAAA,eAChC3I,OAAA,CAAC3B,YAAY;gBACXuL,MAAM,EAAE,CAAC9I,YAAY,CAAC+C,GAAG,EAAE/C,YAAY,CAACkD,GAAG,CAAE;gBAC7C6F,IAAI,EAAE,EAAG;gBACTC,KAAK,EAAE;kBAAEC,MAAM,EAAE,MAAM;kBAAEC,KAAK,EAAE;gBAAO,CAAE;gBAAArB,QAAA,gBAEzC3I,OAAA,CAAC1B,SAAS;kBACR2L,GAAG,EAAC,oDAAoD;kBACxDC,WAAW,EAAC;gBAAyF;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtG,CAAC,eAEF/I,OAAA,CAACoG,eAAe;kBAAAwC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAGnB/I,OAAA,CAACzB,MAAM;kBAACoF,QAAQ,EAAE,CAAC7C,YAAY,CAAC+C,GAAG,EAAE/C,YAAY,CAACkD,GAAG,CAAE;kBAAA2E,QAAA,eACrD3I,OAAA,CAACxB,KAAK;oBAAAmK,QAAA,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,EAGR3H,aAAa,iBACZpB,OAAA,CAACvB,QAAQ;kBACP0L,SAAS,EAAE/I,aAAa,CAAC4E,MAAM,CAACqD,GAAG,CAAC1D,CAAC,IAAI,CAACA,CAAC,CAAC9B,GAAG,EAAE8B,CAAC,CAAC3B,GAAG,CAAC,CAAE;kBACzDoG,KAAK,EAAC,MAAM;kBACZC,MAAM,EAAE,CAAE;kBACVC,OAAO,EAAE;gBAAI;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd,CACF,EAGAvH,cAAc,iBACbxB,OAAA,CAACvB,QAAQ;kBACP0L,SAAS,EAAE3I,cAAc,CAAC6H,GAAG,CAAC1D,CAAC,IAAI,CAACA,CAAC,CAAC9B,GAAG,EAAE8B,CAAC,CAAC3B,GAAG,CAAC,CAAE;kBACnDoG,KAAK,EAAC,KAAK;kBACXC,MAAM,EAAE,CAAE;kBACVC,OAAO,EAAE;gBAAI;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd,CACF,EAGAnH,cAAc,CAACyH,GAAG,CAAC,CAACkB,KAAK,EAAEC,KAAK,kBAC/BxK,OAAA,CAACzB,MAAM;kBAAaoF,QAAQ,EAAE,CAAC4G,KAAK,CAAC1G,GAAG,EAAE0G,KAAK,CAACvG,GAAG,CAAE;kBAAA2E,QAAA,eACnD3I,OAAA,CAACxB,KAAK;oBAAAmK,QAAA,GAAC,QAAM,EAAC6B,KAAK,GAAG,CAAC;kBAAA;oBAAA5B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBAAC,GADrByB,KAAK;kBAAA5B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEV,CACT,CAAC,EAGDnH,cAAc,CAACkE,MAAM,GAAG,CAAC,iBACxB9F,OAAA,CAACvB,QAAQ;kBACP0L,SAAS,EAAEvI,cAAc,CAACyH,GAAG,CAAC1D,CAAC,IAAI,CAACA,CAAC,CAAC9B,GAAG,EAAE8B,CAAC,CAAC3B,GAAG,CAAC,CAAE;kBACnDoG,KAAK,EAAC,OAAO;kBACbC,MAAM,EAAE,CAAE;kBACVC,OAAO,EAAE;gBAAI;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd,CACF,EAGA1G,QAAQ,IAAInB,IAAI,CAACmI,GAAG,CAAC9D,GAAG,iBACvBvF,OAAA,CAACzB,MAAM;kBAELoF,QAAQ,EAAE,CAAC4B,GAAG,CAAC1B,GAAG,EAAE0B,GAAG,CAACvB,GAAG,CAAE;kBAC7B0F,IAAI,EAAE/K,CAAC,CAAC8L,OAAO,CAAC;oBACdC,IAAI,EAAE,2BAA2BnF,GAAG,CAAC6E,KAAK,iJAAiJ7E,GAAG,CAACmE,IAAI,QAAQ;oBAC3MhB,SAAS,EAAE,mBAAmB;oBAC9BiC,QAAQ,EAAE,CAAC,EAAE,EAAE,EAAE;kBACnB,CAAC,CAAE;kBAAAhC,QAAA,eAEH3I,OAAA,CAACxB,KAAK;oBAAAmK,QAAA,eACJ3I,OAAA;sBAAA2I,QAAA,gBACE3I,OAAA;wBAAI0I,SAAS,EAAC,aAAa;wBAAAC,QAAA,EAAEpD,GAAG,CAACT;sBAAI;wBAAA8D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAC3C/I,OAAA;wBAAG0I,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EAAEpD,GAAG,CAACN;sBAAW;wBAAA2D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAC1D/I,OAAA;wBAAK0I,SAAS,EAAC,wBAAwB;wBAAAC,QAAA,gBACrC3I,OAAA,CAACf,MAAM;0BAACyJ,SAAS,EAAC;wBAA8B;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eACnD/I,OAAA;0BAAM0I,SAAS,EAAC,SAAS;0BAAAC,QAAA,EAAEpD,GAAG,CAACwB,MAAM,CAACuC,OAAO,CAAC,CAAC;wBAAC;0BAAAV,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrD,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD;gBAAC,GAjBHxD,GAAG,CAACK,EAAE;kBAAAgD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAkBL,CACT,CAAC,EAGDzH,YAAY,CAAC+H,GAAG,CAAC9D,GAAG,iBACnBvF,OAAA,CAACzB,MAAM;kBAELoF,QAAQ,EAAE,CAAC4B,GAAG,CAAC1B,GAAG,EAAE0B,GAAG,CAACvB,GAAG,CAAE;kBAC7B0F,IAAI,EAAE/K,CAAC,CAAC8L,OAAO,CAAC;oBACdC,IAAI,EAAE,4OAA4OnF,GAAG,CAACmE,IAAI,QAAQ;oBAClQhB,SAAS,EAAE,4BAA4B;oBACvCiC,QAAQ,EAAE,CAAC,EAAE,EAAE,EAAE;kBACnB,CAAC;gBAAE,GANE,YAAYpF,GAAG,CAACK,EAAE,EAAE;kBAAAgD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAO1B,CACF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACU;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACrI,GAAA,CAhrBID,MAAM;EAAA,QACOhB,OAAO;AAAA;AAAAmL,EAAA,GADpBnK,MAAM;AAkrBZ,eAAeA,MAAM;AAAC,IAAAmK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}