import React, { useState, useEffect } from 'react';
import {
  FiTarget,
  FiTrendingUp,
  FiClock,
  FiActivity,
  FiZap,
  FiHeart,
  FiCalendar,
  FiSave,
  FiTrash2,
  FiAlertCircle
} from 'react-icons/fi';
import { useAuth } from '../contexts/AuthContext';
import {
  calculateRacePredictions,
  estimateVO2Max,
  calculateTrainingPaces,
  generateTrainingRecommendations,
  timeToSeconds,
  DISTANCES
} from '../utils/performancePrediction';

const Performance = () => {
  const { user } = useAuth();
  const [selectedDistance, setSelectedDistance] = useState('5K');
  const [timeInput, setTimeInput] = useState('25:00');
  const [predictions, setPredictions] = useState(null);
  const [trainingPaces, setTrainingPaces] = useState(null);
  const [recommendations, setRecommendations] = useState([]);
  const [vo2Max, setVO2Max] = useState(null);
  const [performanceHistory, setPerformanceHistory] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [showSaveModal, setShowSaveModal] = useState(false);

  const calculatePredictions = React.useCallback(() => {
    const timeInSeconds = timeToSeconds(timeInput);
    const distance = DISTANCES[selectedDistance];

    if (timeInSeconds > 0 && distance) {
      // Calculate race predictions
      const racePredictions = calculateRacePredictions(timeInSeconds, distance);
      setPredictions(racePredictions);

      // Calculate VO2 Max
      const estimatedVO2Max = estimateVO2Max(timeInSeconds, distance);
      setVO2Max(estimatedVO2Max);

      // Calculate training paces
      const paces = calculateTrainingPaces(estimatedVO2Max, calculateAge(user?.dateOfBirth));
      setTrainingPaces(paces);

      // Generate recommendations
      if (user) {
        const recs = generateTrainingRecommendations(user, { distance, time: timeInSeconds });
        setRecommendations(recs);
      }
    }
  }, [timeInput, selectedDistance, user]);

  useEffect(() => {
    if (timeInput && selectedDistance) {
      calculatePredictions();
    }
  }, [timeInput, selectedDistance, calculatePredictions]);



  const calculateAge = (dateOfBirth) => {
    if (!dateOfBirth) return 30;
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    return age;
  };

  // Charger les données de performance depuis localStorage
  useEffect(() => {
    try {
      const savedHistory = JSON.parse(localStorage.getItem('performanceHistory') || '[]');
      setPerformanceHistory(savedHistory);
    } catch (error) {
      console.error('Erreur lors du chargement de l\'historique:', error);
      setError('Erreur lors du chargement des données sauvegardées.');
    }
  }, []);

  // Sauvegarder une performance
  const savePerformance = () => {
    if (!predictions || !vo2Max) {
      setError('Veuillez d\'abord calculer vos prédictions.');
      return;
    }

    const performanceEntry = {
      id: Date.now(),
      date: new Date().toISOString(),
      distance: selectedDistance,
      time: timeInput,
      timeInSeconds: timeToSeconds(timeInput),
      vo2Max: vo2Max,
      predictions: predictions,
      trainingPaces: trainingPaces,
      user: user?.firstName || 'Utilisateur'
    };

    try {
      const updatedHistory = [performanceEntry, ...performanceHistory];
      setPerformanceHistory(updatedHistory);
      localStorage.setItem('performanceHistory', JSON.stringify(updatedHistory));
      setShowSaveModal(false);
    } catch (error) {
      console.error('Erreur lors de la sauvegarde:', error);
      setError('Erreur lors de la sauvegarde des données.');
    }
  };

  // Supprimer une performance de l'historique
  const deletePerformance = (performanceId) => {
    try {
      const updatedHistory = performanceHistory.filter(p => p.id !== performanceId);
      setPerformanceHistory(updatedHistory);
      localStorage.setItem('performanceHistory', JSON.stringify(updatedHistory));
    } catch (error) {
      console.error('Erreur lors de la suppression:', error);
      setError('Erreur lors de la suppression.');
    }
  };

  const formatDistance = (distance) => {
    const distanceNames = {
      '5K': '5 km',
      '10K': '10 km',
      'HALF_MARATHON': 'Semi-marathon',
      'MARATHON': 'Marathon'
    };
    return distanceNames[distance] || distance;
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            Prédiction de Performance
          </h1>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            Découvrez vos temps de course prédits, vos allures d'entraînement personnalisées
            et recevez des recommandations adaptées à vos objectifs.
          </p>

          {/* Messages d'erreur */}
          {error && (
            <div className="mt-4 max-w-md mx-auto bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-center">
                <FiAlertCircle className="h-5 w-5 text-red-600 mr-2" />
                <p className="text-sm text-red-800">{error}</p>
              </div>
            </div>
          )}
        </div>

        {/* Input Section */}
        <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-6 flex items-center">
            <FiTarget className="mr-2 text-blue-600" />
            Entrez votre performance actuelle
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label htmlFor="distance" className="block text-sm font-medium text-gray-700 mb-2">
                Distance
              </label>
              <select
                id="distance"
                value={selectedDistance}
                onChange={(e) => setSelectedDistance(e.target.value)}
                className="block w-full px-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                {Object.keys(DISTANCES).map(distance => (
                  <option key={distance} value={distance}>
                    {formatDistance(distance)}
                  </option>
                ))}
              </select>
            </div>
            
            <div>
              <label htmlFor="time" className="block text-sm font-medium text-gray-700 mb-2">
                Temps (MM:SS ou HH:MM:SS)
              </label>
              <input
                type="text"
                id="time"
                value={timeInput}
                onChange={(e) => setTimeInput(e.target.value)}
                placeholder="25:00"
                className="block w-full px-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>
        </div>

        {predictions && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Race Predictions */}
            <div className="lg:col-span-2">
              <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
                <h2 className="text-xl font-semibold text-gray-900 mb-6 flex items-center">
                  <FiClock className="mr-2 text-green-600" />
                  Prédictions de temps de course
                </h2>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {Object.entries(predictions).map(([distance, data]) => (
                    <div key={distance} className="bg-gray-50 rounded-lg p-4">
                      <div className="flex justify-between items-center mb-2">
                        <h3 className="font-semibold text-gray-900">{formatDistance(distance)}</h3>
                        <FiActivity className="text-blue-600" />
                      </div>
                      <div className="text-2xl font-bold text-blue-600 mb-1">
                        {data.timeFormatted}
                      </div>
                      <div className="text-sm text-gray-600">
                        Allure: {data.paceFormatted}/km
                      </div>
                    </div>
                  ))}
                </div>

                {/* Bouton de sauvegarde */}
                <div className="mt-6 flex justify-center">
                  <button
                    onClick={() => setShowSaveModal(true)}
                    className="bg-blue-600 text-white py-2 px-6 rounded-lg hover:bg-blue-700 transition-colors flex items-center"
                  >
                    <FiSave className="mr-2" />
                    Sauvegarder cette performance
                  </button>
                </div>
              </div>

              {/* Training Paces */}
              {trainingPaces && (
                <div className="bg-white rounded-lg shadow-lg p-6">
                  <h2 className="text-xl font-semibold text-gray-900 mb-6 flex items-center">
                    <FiZap className="mr-2 text-purple-600" />
                    Allures d'entraînement
                  </h2>
                  
                  <div className="space-y-4">
                    {Object.entries(trainingPaces).map(([type, data]) => (
                      <div key={type} className="border border-gray-200 rounded-lg p-4">
                        <div className="flex justify-between items-start mb-2">
                          <div>
                            <h3 className="font-semibold text-gray-900 capitalize">
                              {type === 'easy' ? 'Facile' : 
                               type === 'marathon' ? 'Marathon' :
                               type === 'threshold' ? 'Seuil' :
                               type === 'interval' ? 'Intervalles' :
                               type === 'repetition' ? 'Répétitions' : type}
                            </h3>
                            <p className="text-sm text-gray-600">{data.description}</p>
                          </div>
                          <div className="text-right">
                            <div className="text-lg font-bold text-purple-600">
                              {data.paceFormatted}/km
                            </div>
                            <div className="text-sm text-gray-500">
                              {data.hrZone.min}-{data.hrZone.max}% FCM
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Sidebar */}
            <div className="space-y-8">
              {/* VO2 Max */}
              {vo2Max && (
                <div className="bg-white rounded-lg shadow-lg p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <FiHeart className="mr-2 text-red-600" />
                    VO2 Max estimé
                  </h3>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-red-600 mb-2">
                      {vo2Max.toFixed(1)}
                    </div>
                    <div className="text-sm text-gray-600">
                      ml/kg/min
                    </div>
                    <div className="mt-4 text-sm text-gray-500">
                      {vo2Max >= 60 ? 'Excellent' :
                       vo2Max >= 50 ? 'Très bon' :
                       vo2Max >= 40 ? 'Bon' :
                       vo2Max >= 35 ? 'Moyen' : 'À améliorer'}
                    </div>
                  </div>
                </div>
              )}

              {/* Training Recommendations */}
              {recommendations.length > 0 && (
                <div className="bg-white rounded-lg shadow-lg p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <FiTrendingUp className="mr-2 text-orange-600" />
                    Recommandations
                  </h3>
                  
                  <div className="space-y-4">
                    {recommendations.map((rec, index) => (
                      <div key={index} className="border-l-4 border-orange-500 pl-4">
                        <h4 className="font-medium text-gray-900 mb-1">
                          {rec.title}
                        </h4>
                        <p className="text-sm text-gray-600 mb-2">
                          {rec.description}
                        </p>
                        {rec.frequency && (
                          <div className="text-xs text-gray-500">
                            Fréquence: {rec.frequency}
                          </div>
                        )}
                        {rec.duration && (
                          <div className="text-xs text-gray-500">
                            Durée: {rec.duration}
                          </div>
                        )}
                        {rec.intensity && (
                          <div className="text-xs text-gray-500">
                            Intensité: {rec.intensity}
                          </div>
                        )}
                        {rec.details && (
                          <ul className="text-xs text-gray-500 mt-2 ml-4">
                            {rec.details.map((detail, i) => (
                              <li key={i} className="list-disc">{detail}</li>
                            ))}
                          </ul>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Performance Tips */}
              <div className="bg-white rounded-lg shadow-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <FiCalendar className="mr-2 text-indigo-600" />
                  Conseils d'entraînement
                </h3>
                
                <div className="space-y-3 text-sm text-gray-600">
                  <div className="flex items-start">
                    <div className="w-2 h-2 bg-indigo-600 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                    <p>Respectez le principe de progressivité : augmentez votre volume d'entraînement de 10% maximum par semaine.</p>
                  </div>
                  <div className="flex items-start">
                    <div className="w-2 h-2 bg-indigo-600 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                    <p>80% de vos entraînements doivent être à allure facile pour développer votre endurance de base.</p>
                  </div>
                  <div className="flex items-start">
                    <div className="w-2 h-2 bg-indigo-600 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                    <p>Intégrez une sortie longue par semaine pour améliorer votre endurance.</p>
                  </div>
                  <div className="flex items-start">
                    <div className="w-2 h-2 bg-indigo-600 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                    <p>Planifiez des semaines de récupération toutes les 3-4 semaines.</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Historique des performances */}
        {performanceHistory.length > 0 && (
          <div className="bg-white rounded-lg shadow-lg p-6 mt-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-6 flex items-center">
              <FiCalendar className="mr-2 text-indigo-600" />
              Historique des performances ({performanceHistory.length})
            </h2>

            <div className="space-y-4 max-h-96 overflow-y-auto">
              {performanceHistory.map(performance => (
                <div key={performance.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-3">
                      <span className="text-lg font-semibold text-gray-900">
                        {formatDistance(performance.distance)}
                      </span>
                      <span className="text-blue-600 font-medium">
                        {performance.time}
                      </span>
                      <span className="text-sm text-gray-500">
                        VO2 Max: {performance.vo2Max?.toFixed(1)}
                      </span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-gray-500">
                        {new Date(performance.date).toLocaleDateString()}
                      </span>
                      <button
                        onClick={() => deletePerformance(performance.id)}
                        className="text-red-600 hover:text-red-800 transition-colors"
                        title="Supprimer"
                      >
                        <FiTrash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </div>

                  <div className="text-sm text-gray-600">
                    Prédictions: {Object.entries(performance.predictions || {}).map(([dist, data]) =>
                      `${formatDistance(dist)}: ${data.timeFormatted}`
                    ).join(' • ')}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Modal de sauvegarde */}
        {showSaveModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Sauvegarder cette performance
              </h3>
              <p className="text-gray-600 mb-6">
                Voulez-vous sauvegarder cette performance ({formatDistance(selectedDistance)} en {timeInput})
                dans votre historique ?
              </p>
              <div className="flex space-x-3">
                <button
                  onClick={savePerformance}
                  className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Sauvegarder
                </button>
                <button
                  onClick={() => setShowSaveModal(false)}
                  className="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-400 transition-colors"
                >
                  Annuler
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Performance;
