{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projet fitness\\\\DATA\\\\fitness-ui\\\\src\\\\pages\\\\Social.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { FiUsers, FiTrendingUp, FiAward, FiUserPlus, FiMessageCircle, FiTarget, FiClock, FiMapPin, FiCheck, FiX, FiSearch, FiPlus, FiEdit3, FiTrash2, FiShare2, FiHeart, FiThumbsUp } from 'react-icons/fi';\nimport { useAuth } from '../contexts/AuthContext';\nimport { generateMockUsers, generateChallenges, generateFriendships, generateFriendSuggestions, formatTime, formatPace } from '../utils/socialUtils';\nimport { generateSegments } from '../utils/mapUtils';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Social = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const [activeTab, setActiveTab] = useState('friends');\n  const [challenges, setChallenges] = useState([]);\n  const [segments, setSegments] = useState([]);\n  const [friendships, setFriendships] = useState([]);\n  const [friendSuggestions, setFriendSuggestions] = useState([]);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [userActivities, setUserActivities] = useState([]);\n  const [showCreateChallenge, setShowCreateChallenge] = useState(false);\n  const [newChallenge, setNewChallenge] = useState({\n    name: '',\n    description: '',\n    type: 'distance',\n    target: '',\n    duration: 7,\n    difficulty: 'modéré'\n  });\n  useEffect(() => {\n    // Charger les données sauvegardées\n    try {\n      const savedChallenges = JSON.parse(localStorage.getItem('socialChallenges') || '[]');\n      const savedActivities = JSON.parse(localStorage.getItem('userActivities') || '[]');\n      const savedFriendships = JSON.parse(localStorage.getItem('friendships') || '[]');\n      if (savedChallenges.length > 0) {\n        setChallenges(savedChallenges);\n      }\n      setUserActivities(savedActivities);\n      if (savedFriendships.length > 0) {\n        setFriendships(savedFriendships);\n      }\n    } catch (error) {\n      console.error('Erreur lors du chargement des données sociales:', error);\n    }\n\n    // Générer des données fictives si aucune donnée sauvegardée\n    const mockUsers = generateMockUsers(50);\n    const mockChallenges = generateChallenges(mockUsers);\n    const mockSegments = generateSegments([\n    // Quelques routes fictives pour générer des segments\n    {\n      name: 'Parcours du Parc',\n      points: Array.from({\n        length: 20\n      }, (_, i) => ({\n        lat: 48.8566 + i * 0.001,\n        lng: 2.3522 + i * 0.001,\n        elevation: 100 + Math.sin(i) * 50\n      })),\n      type: 'running'\n    }, {\n      name: 'Circuit de la Forêt',\n      points: Array.from({\n        length: 15\n      }, (_, i) => ({\n        lat: 48.8566 + i * 0.0015,\n        lng: 2.3522 - i * 0.0008,\n        elevation: 120 + Math.cos(i) * 40\n      })),\n      type: 'cycling'\n    }]);\n\n    // Utiliser les données fictives si pas de données sauvegardées\n    if (challenges.length === 0) {\n      setChallenges(mockChallenges);\n    }\n    setSegments(mockSegments);\n    if (user && friendships.length === 0) {\n      const userFriendships = generateFriendships(mockUsers, user.id);\n      const suggestions = generateFriendSuggestions(mockUsers, user.id, userFriendships);\n      setFriendships(userFriendships);\n      setFriendSuggestions(suggestions);\n    }\n  }, [user]);\n  const acceptedFriends = friendships.filter(f => f.status === 'accepted');\n  const pendingRequests = friendships.filter(f => f.status === 'pending');\n  const activeChallenges = challenges.filter(c => c.isActive);\n  const completedChallenges = challenges.filter(c => !c.isActive);\n  const handleAcceptFriend = friendshipId => {\n    setFriendships(prev => prev.map(f => f.id === friendshipId ? {\n      ...f,\n      status: 'accepted'\n    } : f));\n  };\n  const handleRejectFriend = friendshipId => {\n    setFriendships(prev => prev.filter(f => f.id !== friendshipId));\n  };\n  const handleJoinChallenge = challengeId => {\n    const updatedChallenges = challenges.map(challenge => {\n      if (challenge.id === challengeId) {\n        const newParticipant = {\n          user: {\n            id: user.id,\n            firstName: user.firstName,\n            lastName: user.lastName,\n            avatar: user.avatar || `https://ui-avatars.com/api/?name=${user.firstName}+${user.lastName}`\n          },\n          progress: 0,\n          rank: challenge.participants.length + 1,\n          joinedAt: new Date(),\n          isCompleted: false,\n          lastActivity: new Date()\n        };\n        return {\n          ...challenge,\n          participants: [...challenge.participants, newParticipant]\n        };\n      }\n      return challenge;\n    });\n    setChallenges(updatedChallenges);\n    localStorage.setItem('socialChallenges', JSON.stringify(updatedChallenges));\n  };\n  const createChallenge = () => {\n    if (!newChallenge.name || !newChallenge.target) return;\n    const challenge = {\n      id: `challenge_${Date.now()}`,\n      name: newChallenge.name,\n      description: newChallenge.description,\n      type: newChallenge.type,\n      target: parseFloat(newChallenge.target),\n      duration: newChallenge.duration,\n      difficulty: newChallenge.difficulty,\n      isActive: true,\n      startDate: new Date(),\n      endDate: new Date(Date.now() + newChallenge.duration * 24 * 60 * 60 * 1000),\n      createdBy: (user === null || user === void 0 ? void 0 : user.firstName) || 'Utilisateur',\n      participants: [{\n        user: {\n          id: (user === null || user === void 0 ? void 0 : user.id) || 'user1',\n          firstName: (user === null || user === void 0 ? void 0 : user.firstName) || 'Utilisateur',\n          lastName: (user === null || user === void 0 ? void 0 : user.lastName) || '',\n          avatar: (user === null || user === void 0 ? void 0 : user.avatar) || `https://ui-avatars.com/api/?name=${user === null || user === void 0 ? void 0 : user.firstName}+${user === null || user === void 0 ? void 0 : user.lastName}`\n        },\n        progress: 0,\n        rank: 1,\n        joinedAt: new Date(),\n        isCompleted: false,\n        lastActivity: new Date()\n      }],\n      icon: getChallengeIcon(newChallenge.type)\n    };\n    const updatedChallenges = [challenge, ...challenges];\n    setChallenges(updatedChallenges);\n    localStorage.setItem('socialChallenges', JSON.stringify(updatedChallenges));\n    setShowCreateChallenge(false);\n    setNewChallenge({\n      name: '',\n      description: '',\n      type: 'distance',\n      target: '',\n      duration: 7,\n      difficulty: 'modéré'\n    });\n  };\n  const deleteChallenge = challengeId => {\n    const updatedChallenges = challenges.filter(c => c.id !== challengeId);\n    setChallenges(updatedChallenges);\n    localStorage.setItem('socialChallenges', JSON.stringify(updatedChallenges));\n  };\n  const updateChallengeProgress = (challengeId, progress) => {\n    const updatedChallenges = challenges.map(challenge => {\n      if (challenge.id === challengeId) {\n        const updatedParticipants = challenge.participants.map(p => {\n          if (p.user.id === (user === null || user === void 0 ? void 0 : user.id)) {\n            return {\n              ...p,\n              progress: Math.min(progress, challenge.target),\n              isCompleted: progress >= challenge.target,\n              lastActivity: new Date()\n            };\n          }\n          return p;\n        });\n\n        // Recalculer les rangs\n        const sortedParticipants = updatedParticipants.sort((a, b) => b.progress - a.progress);\n        sortedParticipants.forEach((p, index) => {\n          p.rank = index + 1;\n        });\n        return {\n          ...challenge,\n          participants: sortedParticipants\n        };\n      }\n      return challenge;\n    });\n    setChallenges(updatedChallenges);\n    localStorage.setItem('socialChallenges', JSON.stringify(updatedChallenges));\n  };\n  const filteredFriends = acceptedFriends.filter(friendship => friendship.user2.firstName.toLowerCase().includes(searchTerm.toLowerCase()) || friendship.user2.lastName.toLowerCase().includes(searchTerm.toLowerCase()));\n  const getDifficultyColor = difficulty => {\n    switch (difficulty) {\n      case 'facile':\n        return 'text-green-600 bg-green-100';\n      case 'modéré':\n        return 'text-yellow-600 bg-yellow-100';\n      case 'difficile':\n        return 'text-red-600 bg-red-100';\n      default:\n        return 'text-gray-600 bg-gray-100';\n    }\n  };\n  const getTypeIcon = type => {\n    switch (type) {\n      case 'running':\n        return '🏃‍♂️';\n      case 'cycling':\n        return '🚴‍♂️';\n      case 'hiking':\n        return '🥾';\n      case 'walking':\n        return '🚶‍♂️';\n      default:\n        return '📍';\n    }\n  };\n  const getChallengeIcon = type => {\n    switch (type) {\n      case 'distance':\n        return '📏';\n      case 'activities':\n        return '🏃‍♂️';\n      case 'time':\n        return '⏱️';\n      case 'streak':\n        return '🔥';\n      default:\n        return '🎯';\n    }\n  };\n  const addActivity = activity => {\n    const newActivity = {\n      id: Date.now(),\n      user: {\n        id: (user === null || user === void 0 ? void 0 : user.id) || 'user1',\n        firstName: (user === null || user === void 0 ? void 0 : user.firstName) || 'Utilisateur',\n        lastName: (user === null || user === void 0 ? void 0 : user.lastName) || '',\n        avatar: (user === null || user === void 0 ? void 0 : user.avatar) || `https://ui-avatars.com/api/?name=${user === null || user === void 0 ? void 0 : user.firstName}+${user === null || user === void 0 ? void 0 : user.lastName}`\n      },\n      ...activity,\n      timestamp: new Date(),\n      likes: [],\n      comments: []\n    };\n    const updatedActivities = [newActivity, ...userActivities];\n    setUserActivities(updatedActivities);\n    localStorage.setItem('userActivities', JSON.stringify(updatedActivities));\n  };\n  const likeActivity = activityId => {\n    const updatedActivities = userActivities.map(activity => {\n      if (activity.id === activityId) {\n        const hasLiked = activity.likes.includes(user === null || user === void 0 ? void 0 : user.id);\n        return {\n          ...activity,\n          likes: hasLiked ? activity.likes.filter(id => id !== (user === null || user === void 0 ? void 0 : user.id)) : [...activity.likes, user === null || user === void 0 ? void 0 : user.id]\n        };\n      }\n      return activity;\n    });\n    setUserActivities(updatedActivities);\n    localStorage.setItem('userActivities', JSON.stringify(updatedActivities));\n  };\n  const addComment = (activityId, comment) => {\n    const updatedActivities = userActivities.map(activity => {\n      if (activity.id === activityId) {\n        const newComment = {\n          id: Date.now(),\n          user: {\n            id: (user === null || user === void 0 ? void 0 : user.id) || 'user1',\n            firstName: (user === null || user === void 0 ? void 0 : user.firstName) || 'Utilisateur',\n            lastName: (user === null || user === void 0 ? void 0 : user.lastName) || ''\n          },\n          text: comment,\n          timestamp: new Date()\n        };\n        return {\n          ...activity,\n          comments: [...activity.comments, newComment]\n        };\n      }\n      return activity;\n    });\n    setUserActivities(updatedActivities);\n    localStorage.setItem('userActivities', JSON.stringify(updatedActivities));\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold text-gray-900 mb-4\",\n          children: \"Communaut\\xE9 FitTracker\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 342,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg text-gray-600 max-w-3xl mx-auto\",\n          children: \"Connectez-vous avec d'autres athl\\xE8tes, participez \\xE0 des d\\xE9fis et d\\xE9couvrez les classements des segments populaires.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 345,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 341,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex space-x-1 bg-gray-100 p-1 rounded-lg mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setActiveTab('friends'),\n          className: `flex-1 py-3 px-4 rounded-md text-sm font-medium transition-colors ${activeTab === 'friends' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'}`,\n          children: [/*#__PURE__*/_jsxDEV(FiUsers, {\n            className: \"inline mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 361,\n            columnNumber: 13\n          }, this), \"Amis (\", acceptedFriends.length, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 353,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setActiveTab('challenges'),\n          className: `flex-1 py-3 px-4 rounded-md text-sm font-medium transition-colors ${activeTab === 'challenges' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'}`,\n          children: [/*#__PURE__*/_jsxDEV(FiTarget, {\n            className: \"inline mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 372,\n            columnNumber: 13\n          }, this), \"D\\xE9fis (\", activeChallenges.length, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 364,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setActiveTab('leaderboards'),\n          className: `flex-1 py-3 px-4 rounded-md text-sm font-medium transition-colors ${activeTab === 'leaderboards' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'}`,\n          children: [/*#__PURE__*/_jsxDEV(FiTrendingUp, {\n            className: \"inline mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 383,\n            columnNumber: 13\n          }, this), \"Classements\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 375,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 352,\n        columnNumber: 9\n      }, this), activeTab === 'friends' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:col-span-2\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-xl font-semibold text-gray-900\",\n                children: [\"Mes Amis (\", acceptedFriends.length, \")\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 395,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative\",\n                children: [/*#__PURE__*/_jsxDEV(FiSearch, {\n                  className: \"absolute left-3 top-3 h-4 w-4 text-gray-400\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 399,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  placeholder: \"Rechercher un ami...\",\n                  value: searchTerm,\n                  onChange: e => setSearchTerm(e.target.value),\n                  className: \"pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 400,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 398,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 394,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: filteredFriends.map(friendship => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    src: friendship.user2.avatar,\n                    alt: `${friendship.user2.firstName} ${friendship.user2.lastName}`,\n                    className: \"w-12 h-12 rounded-full\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 414,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"font-medium text-gray-900\",\n                      children: [friendship.user2.firstName, \" \", friendship.user2.lastName]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 420,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center space-x-4 text-sm text-gray-600\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"flex items-center\",\n                        children: [/*#__PURE__*/_jsxDEV(FiMapPin, {\n                          className: \"h-4 w-4 mr-1\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 425,\n                          columnNumber: 31\n                        }, this), friendship.user2.city]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 424,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"flex items-center\",\n                        children: [/*#__PURE__*/_jsxDEV(FiClock, {\n                          className: \"h-4 w-4 mr-1\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 429,\n                          columnNumber: 31\n                        }, this), friendship.user2.isOnline ? 'En ligne' : 'Hors ligne']\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 428,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 423,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-gray-500 mt-1\",\n                      children: [friendship.mutualFriends, \" amis en commun \\u2022 \", friendship.sharedActivities, \" activit\\xE9s partag\\xE9es\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 433,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 419,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 413,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"p-2 text-blue-600 hover:bg-blue-50 rounded-lg\",\n                    children: /*#__PURE__*/_jsxDEV(FiMessageCircle, {\n                      className: \"h-5 w-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 440,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 439,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-right\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm font-medium text-gray-900\",\n                      children: [friendship.user2.stats.totalDistance, \" km\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 443,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-gray-500\",\n                      children: [friendship.user2.stats.totalActivities, \" activit\\xE9s\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 446,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 442,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 438,\n                  columnNumber: 23\n                }, this)]\n              }, friendship.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 412,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 410,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 393,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 392,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-6\",\n          children: [pendingRequests.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-4\",\n              children: [\"Demandes d'amiti\\xE9 (\", pendingRequests.length, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 462,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3\",\n              children: pendingRequests.map(request => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between p-3 border border-gray-200 rounded-lg\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    src: request.user1.avatar,\n                    alt: `${request.user1.firstName} ${request.user1.lastName}`,\n                    className: \"w-10 h-10 rounded-full\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 469,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"font-medium text-gray-900 text-sm\",\n                      children: [request.user1.firstName, \" \", request.user1.lastName]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 475,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-xs text-gray-600\",\n                      children: request.user1.city\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 478,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 474,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 468,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex space-x-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleAcceptFriend(request.id),\n                    className: \"p-1 text-green-600 hover:bg-green-50 rounded\",\n                    children: /*#__PURE__*/_jsxDEV(FiCheck, {\n                      className: \"h-4 w-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 486,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 482,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleRejectFriend(request.id),\n                    className: \"p-1 text-red-600 hover:bg-red-50 rounded\",\n                    children: /*#__PURE__*/_jsxDEV(FiX, {\n                      className: \"h-4 w-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 492,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 488,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 481,\n                  columnNumber: 25\n                }, this)]\n              }, request.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 467,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 465,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 461,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-4\",\n              children: \"Suggestions d'amis\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 503,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3\",\n              children: friendSuggestions.slice(0, 5).map(suggestion => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between p-3 border border-gray-200 rounded-lg\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    src: suggestion.user.avatar,\n                    alt: `${suggestion.user.firstName} ${suggestion.user.lastName}`,\n                    className: \"w-10 h-10 rounded-full\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 510,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"font-medium text-gray-900 text-sm\",\n                      children: [suggestion.user.firstName, \" \", suggestion.user.lastName]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 516,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-xs text-gray-600\",\n                      children: suggestion.reason\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 519,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 515,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 509,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"p-1 text-blue-600 hover:bg-blue-50 rounded\",\n                  children: /*#__PURE__*/_jsxDEV(FiUserPlus, {\n                    className: \"h-4 w-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 523,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 522,\n                  columnNumber: 23\n                }, this)]\n              }, suggestion.user.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 508,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 506,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 502,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 458,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 390,\n        columnNumber: 11\n      }, this), activeTab === 'challenges' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:col-span-2\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-xl font-semibold text-gray-900\",\n                children: [\"D\\xE9fis Actifs (\", activeChallenges.length, \")\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 540,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setShowCreateChallenge(true),\n                className: \"bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(FiPlus, {\n                  className: \"mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 547,\n                  columnNumber: 21\n                }, this), \"Cr\\xE9er un d\\xE9fi\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 543,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 539,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid gap-6\",\n              children: activeChallenges.map(challenge => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-start justify-between mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-2xl\",\n                      children: getChallengeIcon(challenge.type)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 557,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"font-semibold text-gray-900\",\n                        children: challenge.name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 559,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm text-gray-600\",\n                        children: challenge.description\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 560,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 558,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 556,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `px-3 py-1 rounded-full text-xs font-medium ${challenge.category === 'team' ? 'bg-purple-100 text-purple-700' : 'bg-blue-100 text-blue-700'}`,\n                    children: challenge.category === 'team' ? 'Équipe' : 'Individuel'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 563,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 555,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid grid-cols-2 md:grid-cols-4 gap-4 mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-lg font-bold text-gray-900\",\n                      children: challenge.participants.length\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 572,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-gray-600\",\n                      children: \"Participants\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 573,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 571,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-lg font-bold text-gray-900\",\n                      children: challenge.target\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 576,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-gray-600\",\n                      children: challenge.unit\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 577,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 575,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-lg font-bold text-gray-900\",\n                      children: challenge.duration\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 580,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-gray-600\",\n                      children: \"jours\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 581,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 579,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-lg font-bold text-gray-900\",\n                      children: Math.ceil((challenge.endDate - new Date()) / (24 * 60 * 60 * 1000))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 584,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-gray-600\",\n                      children: \"jours restants\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 587,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 583,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 570,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm text-gray-600\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"Prix :\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 593,\n                      columnNumber: 27\n                    }, this), \" \", challenge.prize]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 592,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleJoinChallenge(challenge.id),\n                    className: \"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm\",\n                    children: \"Rejoindre\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 595,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 591,\n                  columnNumber: 23\n                }, this)]\n              }, challenge.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 554,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 552,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 538,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 537,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-4\",\n              children: \"Mes D\\xE9fis en Cours\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 611,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3\",\n              children: activeChallenges.slice(0, 3).map(challenge => {\n                const userParticipation = challenge.participants.find(p => p.user.id === (user === null || user === void 0 ? void 0 : user.id));\n                if (!userParticipation) return null;\n                const progressPercentage = userParticipation.progress / challenge.target * 100;\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"p-3 border border-gray-200 rounded-lg\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center justify-between mb-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"font-medium text-gray-900 text-sm\",\n                      children: challenge.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 624,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-xs text-gray-600\",\n                      children: [\"#\", userParticipation.rank]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 625,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 623,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full bg-gray-200 rounded-full h-2 mb-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-blue-600 h-2 rounded-full\",\n                      style: {\n                        width: `${Math.min(progressPercentage, 100)}%`\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 628,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 627,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-xs text-gray-600\",\n                    children: [userParticipation.progress, \" / \", challenge.target, \" \", challenge.unit]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 633,\n                    columnNumber: 25\n                  }, this)]\n                }, challenge.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 622,\n                  columnNumber: 23\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 614,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 610,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-4\",\n              children: \"D\\xE9fis Termin\\xE9s\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 643,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3\",\n              children: completedChallenges.slice(0, 3).map(challenge => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-3 border border-gray-200 rounded-lg\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"font-medium text-gray-900 text-sm\",\n                  children: challenge.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 649,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between mt-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-xs text-gray-600\",\n                    children: [challenge.participants.length, \" participants\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 651,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-xs text-green-600 font-medium\",\n                    children: \"Termin\\xE9\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 654,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 650,\n                  columnNumber: 23\n                }, this)]\n              }, challenge.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 648,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 646,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 642,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 609,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 535,\n        columnNumber: 11\n      }, this), activeTab === 'leaderboards' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:col-span-2\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-xl font-semibold text-gray-900 mb-6\",\n              children: \"Classements des Segments\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 670,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-6\",\n              children: segments.slice(0, 5).map(segment => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"border border-gray-200 rounded-lg p-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-start justify-between mb-4\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-xl\",\n                      children: getTypeIcon(segment.type)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 679,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"font-semibold text-gray-900\",\n                        children: segment.name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 681,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm text-gray-600\",\n                        children: segment.description\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 682,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center space-x-4 mt-1\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-xs text-gray-500\",\n                          children: [segment.distance.toFixed(1), \" km\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 684,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: `px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(segment.difficulty)}`,\n                          children: segment.difficulty\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 685,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-xs text-gray-500\",\n                          children: [segment.totalAttempts, \" tentatives\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 688,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 683,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 680,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 678,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 677,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"font-medium text-gray-900 text-sm mb-3\",\n                    children: \"Top 5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 695,\n                    columnNumber: 25\n                  }, this), segment.leaderboard.slice(0, 5).map((entry, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center justify-between p-2 rounded-lg hover:bg-gray-50\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center space-x-3\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: `w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold ${index === 0 ? 'bg-yellow-100 text-yellow-800' : index === 1 ? 'bg-gray-100 text-gray-800' : index === 2 ? 'bg-orange-100 text-orange-800' : 'bg-blue-100 text-blue-800'}`,\n                        children: entry.rank\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 699,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"font-medium text-gray-900 text-sm\",\n                          children: entry.athlete\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 708,\n                          columnNumber: 33\n                        }, this), entry.isLocalLegend && /*#__PURE__*/_jsxDEV(FiAward, {\n                          className: \"inline ml-2 h-4 w-4 text-yellow-500\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 710,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 707,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 698,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-right\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"font-medium text-gray-900 text-sm\",\n                        children: formatTime(entry.time)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 715,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-xs text-gray-500\",\n                        children: formatPace(entry.pace)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 718,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 714,\n                      columnNumber: 29\n                    }, this)]\n                  }, index, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 697,\n                    columnNumber: 27\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 694,\n                  columnNumber: 23\n                }, this)]\n              }, segment.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 676,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 674,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 669,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 668,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-4\",\n              children: \"Mes Performances\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 734,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between p-3 bg-yellow-50 rounded-lg\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(FiAward, {\n                    className: \"h-5 w-5 text-yellow-600\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 740,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-medium text-gray-900\",\n                    children: \"L\\xE9gendes Locales\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 741,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 739,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-lg font-bold text-yellow-600\",\n                  children: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 743,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 738,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between p-3 bg-blue-50 rounded-lg\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(FiTrendingUp, {\n                    className: \"h-5 w-5 text-blue-600\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 748,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-medium text-gray-900\",\n                    children: \"Top 10\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 749,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 747,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-lg font-bold text-blue-600\",\n                  children: \"8\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 751,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 746,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between p-3 bg-green-50 rounded-lg\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(FiAward, {\n                    className: \"h-5 w-5 text-green-600\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 756,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-medium text-gray-900\",\n                    children: \"Records Personnels\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 757,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 755,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-lg font-bold text-green-600\",\n                  children: \"15\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 759,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 754,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 737,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 733,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-4\",\n              children: \"Segments Populaires\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 765,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3\",\n              children: segments.slice(0, 5).map(segment => {\n                var _segment$leaderboard$;\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"font-medium text-gray-900 text-sm\",\n                      children: segment.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 772,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center space-x-2 text-xs text-gray-600\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        children: [segment.distance.toFixed(1), \" km\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 774,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"\\u2022\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 775,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: [segment.totalAttempts, \" tentatives\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 776,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 773,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 771,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-right\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm font-medium text-gray-900\",\n                      children: formatTime(((_segment$leaderboard$ = segment.leaderboard[0]) === null || _segment$leaderboard$ === void 0 ? void 0 : _segment$leaderboard$.time) || 0)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 780,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-gray-500\",\n                      children: \"Record\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 783,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 779,\n                    columnNumber: 23\n                  }, this)]\n                }, segment.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 770,\n                  columnNumber: 21\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 768,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 764,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 732,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 666,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 339,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 338,\n    columnNumber: 5\n  }, this);\n};\n_s(Social, \"0IvbzaJtPgLFneQD1wWFCK9yYKM=\", false, function () {\n  return [useAuth];\n});\n_c = Social;\nexport default Social;\nvar _c;\n$RefreshReg$(_c, \"Social\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "FiUsers", "FiTrendingUp", "FiAward", "FiUserPlus", "FiMessageCircle", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "FiMapPin", "<PERSON><PERSON><PERSON><PERSON>", "FiX", "FiSearch", "FiPlus", "FiEdit3", "FiTrash2", "FiShare2", "<PERSON><PERSON><PERSON><PERSON>", "FiThumbsUp", "useAuth", "generateMockUsers", "generateChallenges", "generateFriendships", "generateFriendSuggestions", "formatTime", "formatPace", "generateSegments", "jsxDEV", "_jsxDEV", "Social", "_s", "user", "activeTab", "setActiveTab", "challenges", "setChallenges", "segments", "setSegments", "friendships", "setFriendships", "friendSuggestions", "setFriendSuggestions", "searchTerm", "setSearchTerm", "userActivities", "setUserActivities", "showCreateChallenge", "setShowCreateChallenge", "newChallenge", "setNewChallenge", "name", "description", "type", "target", "duration", "difficulty", "savedChallenges", "JSON", "parse", "localStorage", "getItem", "savedActivities", "savedFriendships", "length", "error", "console", "mockUsers", "mockChallenges", "mockSegments", "points", "Array", "from", "_", "i", "lat", "lng", "elevation", "Math", "sin", "cos", "userFriendships", "id", "suggestions", "acceptedFriends", "filter", "f", "status", "pendingRequests", "activeChallenges", "c", "isActive", "completedChallenges", "handleAcceptFriend", "friendshipId", "prev", "map", "handleRejectFriend", "handleJoinChallenge", "challengeId", "updatedChallenges", "challenge", "newParticipant", "firstName", "lastName", "avatar", "progress", "rank", "participants", "joinedAt", "Date", "isCompleted", "lastActivity", "setItem", "stringify", "createChallenge", "now", "parseFloat", "startDate", "endDate", "created<PERSON>y", "icon", "getChallengeIcon", "deleteChallenge", "updateChallengeProgress", "updatedParticipants", "p", "min", "sortedParticipants", "sort", "a", "b", "for<PERSON>ach", "index", "filteredFriends", "friendship", "user2", "toLowerCase", "includes", "getDifficultyColor", "getTypeIcon", "addActivity", "activity", "newActivity", "timestamp", "likes", "comments", "updatedActivities", "likeActivity", "activityId", "<PERSON><PERSON><PERSON>d", "addComment", "comment", "newComment", "text", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "placeholder", "value", "onChange", "e", "src", "alt", "city", "isOnline", "mutualFriends", "sharedActivities", "stats", "totalDistance", "totalActivities", "request", "user1", "slice", "suggestion", "reason", "category", "unit", "ceil", "prize", "userParticipation", "find", "progressPercentage", "style", "width", "segment", "distance", "toFixed", "totalAttempts", "leaderboard", "entry", "athlete", "isLocalLegend", "time", "pace", "_segment$leaderboard$", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/projet fitness/DATA/fitness-ui/src/pages/Social.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  FiUsers,\n  FiTrendingUp,\n  FiAward,\n  FiUserPlus,\n  FiMessageCircle,\n  FiTarget,\n  FiClock,\n  FiMapPin,\n  FiCheck,\n  FiX,\n  FiSearch,\n  FiPlus,\n  FiEdit3,\n  FiTrash2,\n  FiShare2,\n  FiHeart,\n  FiThumbsUp\n} from 'react-icons/fi';\nimport { useAuth } from '../contexts/AuthContext';\nimport {\n  generateMockUsers,\n  generateChallenges,\n  generateFriendships,\n  generateFriendSuggestions,\n  formatTime,\n  formatPace\n} from '../utils/socialUtils';\nimport { generateSegments } from '../utils/mapUtils';\n\nconst Social = () => {\n  const { user } = useAuth();\n  const [activeTab, setActiveTab] = useState('friends');\n\n  const [challenges, setChallenges] = useState([]);\n  const [segments, setSegments] = useState([]);\n  const [friendships, setFriendships] = useState([]);\n  const [friendSuggestions, setFriendSuggestions] = useState([]);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [userActivities, setUserActivities] = useState([]);\n  const [showCreateChallenge, setShowCreateChallenge] = useState(false);\n  const [newChallenge, setNewChallenge] = useState({\n    name: '',\n    description: '',\n    type: 'distance',\n    target: '',\n    duration: 7,\n    difficulty: 'modéré'\n  });\n\n\n  useEffect(() => {\n    // Charger les données sauvegardées\n    try {\n      const savedChallenges = JSON.parse(localStorage.getItem('socialChallenges') || '[]');\n      const savedActivities = JSON.parse(localStorage.getItem('userActivities') || '[]');\n      const savedFriendships = JSON.parse(localStorage.getItem('friendships') || '[]');\n\n      if (savedChallenges.length > 0) {\n        setChallenges(savedChallenges);\n      }\n      setUserActivities(savedActivities);\n      if (savedFriendships.length > 0) {\n        setFriendships(savedFriendships);\n      }\n    } catch (error) {\n      console.error('Erreur lors du chargement des données sociales:', error);\n    }\n\n    // Générer des données fictives si aucune donnée sauvegardée\n    const mockUsers = generateMockUsers(50);\n    const mockChallenges = generateChallenges(mockUsers);\n    const mockSegments = generateSegments([\n      // Quelques routes fictives pour générer des segments\n      {\n        name: 'Parcours du Parc',\n        points: Array.from({ length: 20 }, (_, i) => ({\n          lat: 48.8566 + (i * 0.001),\n          lng: 2.3522 + (i * 0.001),\n          elevation: 100 + Math.sin(i) * 50\n        })),\n        type: 'running'\n      },\n      {\n        name: 'Circuit de la Forêt',\n        points: Array.from({ length: 15 }, (_, i) => ({\n          lat: 48.8566 + (i * 0.0015),\n          lng: 2.3522 - (i * 0.0008),\n          elevation: 120 + Math.cos(i) * 40\n        })),\n        type: 'cycling'\n      }\n    ]);\n\n    // Utiliser les données fictives si pas de données sauvegardées\n    if (challenges.length === 0) {\n      setChallenges(mockChallenges);\n    }\n    setSegments(mockSegments);\n\n    if (user && friendships.length === 0) {\n      const userFriendships = generateFriendships(mockUsers, user.id);\n      const suggestions = generateFriendSuggestions(mockUsers, user.id, userFriendships);\n      setFriendships(userFriendships);\n      setFriendSuggestions(suggestions);\n    }\n  }, [user]);\n\n  const acceptedFriends = friendships.filter(f => f.status === 'accepted');\n  const pendingRequests = friendships.filter(f => f.status === 'pending');\n  const activeChallenges = challenges.filter(c => c.isActive);\n  const completedChallenges = challenges.filter(c => !c.isActive);\n\n  const handleAcceptFriend = (friendshipId) => {\n    setFriendships(prev => prev.map(f => \n      f.id === friendshipId ? { ...f, status: 'accepted' } : f\n    ));\n  };\n\n  const handleRejectFriend = (friendshipId) => {\n    setFriendships(prev => prev.filter(f => f.id !== friendshipId));\n  };\n\n  const handleJoinChallenge = (challengeId) => {\n    const updatedChallenges = challenges.map(challenge => {\n      if (challenge.id === challengeId) {\n        const newParticipant = {\n          user: {\n            id: user.id,\n            firstName: user.firstName,\n            lastName: user.lastName,\n            avatar: user.avatar || `https://ui-avatars.com/api/?name=${user.firstName}+${user.lastName}`\n          },\n          progress: 0,\n          rank: challenge.participants.length + 1,\n          joinedAt: new Date(),\n          isCompleted: false,\n          lastActivity: new Date()\n        };\n\n        return {\n          ...challenge,\n          participants: [...challenge.participants, newParticipant]\n        };\n      }\n      return challenge;\n    });\n\n    setChallenges(updatedChallenges);\n    localStorage.setItem('socialChallenges', JSON.stringify(updatedChallenges));\n  };\n\n  const createChallenge = () => {\n    if (!newChallenge.name || !newChallenge.target) return;\n\n    const challenge = {\n      id: `challenge_${Date.now()}`,\n      name: newChallenge.name,\n      description: newChallenge.description,\n      type: newChallenge.type,\n      target: parseFloat(newChallenge.target),\n      duration: newChallenge.duration,\n      difficulty: newChallenge.difficulty,\n      isActive: true,\n      startDate: new Date(),\n      endDate: new Date(Date.now() + newChallenge.duration * 24 * 60 * 60 * 1000),\n      createdBy: user?.firstName || 'Utilisateur',\n      participants: [{\n        user: {\n          id: user?.id || 'user1',\n          firstName: user?.firstName || 'Utilisateur',\n          lastName: user?.lastName || '',\n          avatar: user?.avatar || `https://ui-avatars.com/api/?name=${user?.firstName}+${user?.lastName}`\n        },\n        progress: 0,\n        rank: 1,\n        joinedAt: new Date(),\n        isCompleted: false,\n        lastActivity: new Date()\n      }],\n      icon: getChallengeIcon(newChallenge.type)\n    };\n\n    const updatedChallenges = [challenge, ...challenges];\n    setChallenges(updatedChallenges);\n    localStorage.setItem('socialChallenges', JSON.stringify(updatedChallenges));\n\n    setShowCreateChallenge(false);\n    setNewChallenge({\n      name: '',\n      description: '',\n      type: 'distance',\n      target: '',\n      duration: 7,\n      difficulty: 'modéré'\n    });\n  };\n\n  const deleteChallenge = (challengeId) => {\n    const updatedChallenges = challenges.filter(c => c.id !== challengeId);\n    setChallenges(updatedChallenges);\n    localStorage.setItem('socialChallenges', JSON.stringify(updatedChallenges));\n  };\n\n  const updateChallengeProgress = (challengeId, progress) => {\n    const updatedChallenges = challenges.map(challenge => {\n      if (challenge.id === challengeId) {\n        const updatedParticipants = challenge.participants.map(p => {\n          if (p.user.id === user?.id) {\n            return {\n              ...p,\n              progress: Math.min(progress, challenge.target),\n              isCompleted: progress >= challenge.target,\n              lastActivity: new Date()\n            };\n          }\n          return p;\n        });\n\n        // Recalculer les rangs\n        const sortedParticipants = updatedParticipants.sort((a, b) => b.progress - a.progress);\n        sortedParticipants.forEach((p, index) => {\n          p.rank = index + 1;\n        });\n\n        return {\n          ...challenge,\n          participants: sortedParticipants\n        };\n      }\n      return challenge;\n    });\n\n    setChallenges(updatedChallenges);\n    localStorage.setItem('socialChallenges', JSON.stringify(updatedChallenges));\n  };\n\n  const filteredFriends = acceptedFriends.filter(friendship =>\n    friendship.user2.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    friendship.user2.lastName.toLowerCase().includes(searchTerm.toLowerCase())\n  );\n\n  const getDifficultyColor = (difficulty) => {\n    switch (difficulty) {\n      case 'facile': return 'text-green-600 bg-green-100';\n      case 'modéré': return 'text-yellow-600 bg-yellow-100';\n      case 'difficile': return 'text-red-600 bg-red-100';\n      default: return 'text-gray-600 bg-gray-100';\n    }\n  };\n\n  const getTypeIcon = (type) => {\n    switch (type) {\n      case 'running': return '🏃‍♂️';\n      case 'cycling': return '🚴‍♂️';\n      case 'hiking': return '🥾';\n      case 'walking': return '🚶‍♂️';\n      default: return '📍';\n    }\n  };\n\n  const getChallengeIcon = (type) => {\n    switch (type) {\n      case 'distance': return '📏';\n      case 'activities': return '🏃‍♂️';\n      case 'time': return '⏱️';\n      case 'streak': return '🔥';\n      default: return '🎯';\n    }\n  };\n\n  const addActivity = (activity) => {\n    const newActivity = {\n      id: Date.now(),\n      user: {\n        id: user?.id || 'user1',\n        firstName: user?.firstName || 'Utilisateur',\n        lastName: user?.lastName || '',\n        avatar: user?.avatar || `https://ui-avatars.com/api/?name=${user?.firstName}+${user?.lastName}`\n      },\n      ...activity,\n      timestamp: new Date(),\n      likes: [],\n      comments: []\n    };\n\n    const updatedActivities = [newActivity, ...userActivities];\n    setUserActivities(updatedActivities);\n    localStorage.setItem('userActivities', JSON.stringify(updatedActivities));\n  };\n\n  const likeActivity = (activityId) => {\n    const updatedActivities = userActivities.map(activity => {\n      if (activity.id === activityId) {\n        const hasLiked = activity.likes.includes(user?.id);\n        return {\n          ...activity,\n          likes: hasLiked\n            ? activity.likes.filter(id => id !== user?.id)\n            : [...activity.likes, user?.id]\n        };\n      }\n      return activity;\n    });\n\n    setUserActivities(updatedActivities);\n    localStorage.setItem('userActivities', JSON.stringify(updatedActivities));\n  };\n\n  const addComment = (activityId, comment) => {\n    const updatedActivities = userActivities.map(activity => {\n      if (activity.id === activityId) {\n        const newComment = {\n          id: Date.now(),\n          user: {\n            id: user?.id || 'user1',\n            firstName: user?.firstName || 'Utilisateur',\n            lastName: user?.lastName || ''\n          },\n          text: comment,\n          timestamp: new Date()\n        };\n\n        return {\n          ...activity,\n          comments: [...activity.comments, newComment]\n        };\n      }\n      return activity;\n    });\n\n    setUserActivities(updatedActivities);\n    localStorage.setItem('userActivities', JSON.stringify(updatedActivities));\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Header */}\n        <div className=\"text-center mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-4\">\n            Communauté FitTracker\n          </h1>\n          <p className=\"text-lg text-gray-600 max-w-3xl mx-auto\">\n            Connectez-vous avec d'autres athlètes, participez à des défis et \n            découvrez les classements des segments populaires.\n          </p>\n        </div>\n\n        {/* Tabs */}\n        <div className=\"flex space-x-1 bg-gray-100 p-1 rounded-lg mb-8\">\n          <button\n            onClick={() => setActiveTab('friends')}\n            className={`flex-1 py-3 px-4 rounded-md text-sm font-medium transition-colors ${\n              activeTab === 'friends'\n                ? 'bg-white text-blue-600 shadow-sm'\n                : 'text-gray-600 hover:text-gray-900'\n            }`}\n          >\n            <FiUsers className=\"inline mr-2\" />\n            Amis ({acceptedFriends.length})\n          </button>\n          <button\n            onClick={() => setActiveTab('challenges')}\n            className={`flex-1 py-3 px-4 rounded-md text-sm font-medium transition-colors ${\n              activeTab === 'challenges'\n                ? 'bg-white text-blue-600 shadow-sm'\n                : 'text-gray-600 hover:text-gray-900'\n            }`}\n          >\n            <FiTarget className=\"inline mr-2\" />\n            Défis ({activeChallenges.length})\n          </button>\n          <button\n            onClick={() => setActiveTab('leaderboards')}\n            className={`flex-1 py-3 px-4 rounded-md text-sm font-medium transition-colors ${\n              activeTab === 'leaderboards'\n                ? 'bg-white text-blue-600 shadow-sm'\n                : 'text-gray-600 hover:text-gray-900'\n            }`}\n          >\n            <FiTrendingUp className=\"inline mr-2\" />\n            Classements\n          </button>\n        </div>\n\n        {/* Friends Tab */}\n        {activeTab === 'friends' && (\n          <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n            {/* Friends List */}\n            <div className=\"lg:col-span-2\">\n              <div className=\"bg-white rounded-lg shadow-lg p-6\">\n                <div className=\"flex items-center justify-between mb-6\">\n                  <h2 className=\"text-xl font-semibold text-gray-900\">\n                    Mes Amis ({acceptedFriends.length})\n                  </h2>\n                  <div className=\"relative\">\n                    <FiSearch className=\"absolute left-3 top-3 h-4 w-4 text-gray-400\" />\n                    <input\n                      type=\"text\"\n                      placeholder=\"Rechercher un ami...\"\n                      value={searchTerm}\n                      onChange={(e) => setSearchTerm(e.target.value)}\n                      className=\"pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    />\n                  </div>\n                </div>\n\n                <div className=\"space-y-4\">\n                  {filteredFriends.map(friendship => (\n                    <div key={friendship.id} className=\"flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50\">\n                      <div className=\"flex items-center space-x-4\">\n                        <img\n                          src={friendship.user2.avatar}\n                          alt={`${friendship.user2.firstName} ${friendship.user2.lastName}`}\n                          className=\"w-12 h-12 rounded-full\"\n                        />\n                        <div>\n                          <h3 className=\"font-medium text-gray-900\">\n                            {friendship.user2.firstName} {friendship.user2.lastName}\n                          </h3>\n                          <div className=\"flex items-center space-x-4 text-sm text-gray-600\">\n                            <span className=\"flex items-center\">\n                              <FiMapPin className=\"h-4 w-4 mr-1\" />\n                              {friendship.user2.city}\n                            </span>\n                            <span className=\"flex items-center\">\n                              <FiClock className=\"h-4 w-4 mr-1\" />\n                              {friendship.user2.isOnline ? 'En ligne' : 'Hors ligne'}\n                            </span>\n                          </div>\n                          <div className=\"text-xs text-gray-500 mt-1\">\n                            {friendship.mutualFriends} amis en commun • {friendship.sharedActivities} activités partagées\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"flex items-center space-x-2\">\n                        <button className=\"p-2 text-blue-600 hover:bg-blue-50 rounded-lg\">\n                          <FiMessageCircle className=\"h-5 w-5\" />\n                        </button>\n                        <div className=\"text-right\">\n                          <div className=\"text-sm font-medium text-gray-900\">\n                            {friendship.user2.stats.totalDistance} km\n                          </div>\n                          <div className=\"text-xs text-gray-500\">\n                            {friendship.user2.stats.totalActivities} activités\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </div>\n\n            {/* Sidebar */}\n            <div className=\"space-y-6\">\n              {/* Pending Requests */}\n              {pendingRequests.length > 0 && (\n                <div className=\"bg-white rounded-lg shadow-lg p-6\">\n                  <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">\n                    Demandes d'amitié ({pendingRequests.length})\n                  </h3>\n                  <div className=\"space-y-3\">\n                    {pendingRequests.map(request => (\n                      <div key={request.id} className=\"flex items-center justify-between p-3 border border-gray-200 rounded-lg\">\n                        <div className=\"flex items-center space-x-3\">\n                          <img\n                            src={request.user1.avatar}\n                            alt={`${request.user1.firstName} ${request.user1.lastName}`}\n                            className=\"w-10 h-10 rounded-full\"\n                          />\n                          <div>\n                            <h4 className=\"font-medium text-gray-900 text-sm\">\n                              {request.user1.firstName} {request.user1.lastName}\n                            </h4>\n                            <p className=\"text-xs text-gray-600\">{request.user1.city}</p>\n                          </div>\n                        </div>\n                        <div className=\"flex space-x-1\">\n                          <button\n                            onClick={() => handleAcceptFriend(request.id)}\n                            className=\"p-1 text-green-600 hover:bg-green-50 rounded\"\n                          >\n                            <FiCheck className=\"h-4 w-4\" />\n                          </button>\n                          <button\n                            onClick={() => handleRejectFriend(request.id)}\n                            className=\"p-1 text-red-600 hover:bg-red-50 rounded\"\n                          >\n                            <FiX className=\"h-4 w-4\" />\n                          </button>\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              )}\n\n              {/* Friend Suggestions */}\n              <div className=\"bg-white rounded-lg shadow-lg p-6\">\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">\n                  Suggestions d'amis\n                </h3>\n                <div className=\"space-y-3\">\n                  {friendSuggestions.slice(0, 5).map(suggestion => (\n                    <div key={suggestion.user.id} className=\"flex items-center justify-between p-3 border border-gray-200 rounded-lg\">\n                      <div className=\"flex items-center space-x-3\">\n                        <img\n                          src={suggestion.user.avatar}\n                          alt={`${suggestion.user.firstName} ${suggestion.user.lastName}`}\n                          className=\"w-10 h-10 rounded-full\"\n                        />\n                        <div>\n                          <h4 className=\"font-medium text-gray-900 text-sm\">\n                            {suggestion.user.firstName} {suggestion.user.lastName}\n                          </h4>\n                          <p className=\"text-xs text-gray-600\">{suggestion.reason}</p>\n                        </div>\n                      </div>\n                      <button className=\"p-1 text-blue-600 hover:bg-blue-50 rounded\">\n                        <FiUserPlus className=\"h-4 w-4\" />\n                      </button>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Challenges Tab */}\n        {activeTab === 'challenges' && (\n          <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n            {/* Active Challenges */}\n            <div className=\"lg:col-span-2\">\n              <div className=\"bg-white rounded-lg shadow-lg p-6\">\n                <div className=\"flex items-center justify-between mb-6\">\n                  <h2 className=\"text-xl font-semibold text-gray-900\">\n                    Défis Actifs ({activeChallenges.length})\n                  </h2>\n                  <button\n                    onClick={() => setShowCreateChallenge(true)}\n                    className=\"bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center\"\n                  >\n                    <FiPlus className=\"mr-2\" />\n                    Créer un défi\n                  </button>\n                </div>\n                \n                <div className=\"grid gap-6\">\n                  {activeChallenges.map(challenge => (\n                    <div key={challenge.id} className=\"border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow\">\n                      <div className=\"flex items-start justify-between mb-4\">\n                        <div className=\"flex items-center space-x-3\">\n                          <span className=\"text-2xl\">{getChallengeIcon(challenge.type)}</span>\n                          <div>\n                            <h3 className=\"font-semibold text-gray-900\">{challenge.name}</h3>\n                            <p className=\"text-sm text-gray-600\">{challenge.description}</p>\n                          </div>\n                        </div>\n                        <span className={`px-3 py-1 rounded-full text-xs font-medium ${\n                          challenge.category === 'team' ? 'bg-purple-100 text-purple-700' : 'bg-blue-100 text-blue-700'\n                        }`}>\n                          {challenge.category === 'team' ? 'Équipe' : 'Individuel'}\n                        </span>\n                      </div>\n                      \n                      <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 mb-4\">\n                        <div className=\"text-center\">\n                          <div className=\"text-lg font-bold text-gray-900\">{challenge.participants.length}</div>\n                          <div className=\"text-xs text-gray-600\">Participants</div>\n                        </div>\n                        <div className=\"text-center\">\n                          <div className=\"text-lg font-bold text-gray-900\">{challenge.target}</div>\n                          <div className=\"text-xs text-gray-600\">{challenge.unit}</div>\n                        </div>\n                        <div className=\"text-center\">\n                          <div className=\"text-lg font-bold text-gray-900\">{challenge.duration}</div>\n                          <div className=\"text-xs text-gray-600\">jours</div>\n                        </div>\n                        <div className=\"text-center\">\n                          <div className=\"text-lg font-bold text-gray-900\">\n                            {Math.ceil((challenge.endDate - new Date()) / (24 * 60 * 60 * 1000))}\n                          </div>\n                          <div className=\"text-xs text-gray-600\">jours restants</div>\n                        </div>\n                      </div>\n                      \n                      <div className=\"flex items-center justify-between\">\n                        <div className=\"text-sm text-gray-600\">\n                          <span className=\"font-medium\">Prix :</span> {challenge.prize}\n                        </div>\n                        <button\n                          onClick={() => handleJoinChallenge(challenge.id)}\n                          className=\"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm\"\n                        >\n                          Rejoindre\n                        </button>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </div>\n\n            {/* Challenge Details Sidebar */}\n            <div className=\"space-y-6\">\n              <div className=\"bg-white rounded-lg shadow-lg p-6\">\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">\n                  Mes Défis en Cours\n                </h3>\n                <div className=\"space-y-3\">\n                  {activeChallenges.slice(0, 3).map(challenge => {\n                    const userParticipation = challenge.participants.find(p => p.user.id === user?.id);\n                    if (!userParticipation) return null;\n                    \n                    const progressPercentage = (userParticipation.progress / challenge.target) * 100;\n                    \n                    return (\n                      <div key={challenge.id} className=\"p-3 border border-gray-200 rounded-lg\">\n                        <div className=\"flex items-center justify-between mb-2\">\n                          <h4 className=\"font-medium text-gray-900 text-sm\">{challenge.name}</h4>\n                          <span className=\"text-xs text-gray-600\">#{userParticipation.rank}</span>\n                        </div>\n                        <div className=\"w-full bg-gray-200 rounded-full h-2 mb-2\">\n                          <div\n                            className=\"bg-blue-600 h-2 rounded-full\"\n                            style={{ width: `${Math.min(progressPercentage, 100)}%` }}\n                          ></div>\n                        </div>\n                        <div className=\"text-xs text-gray-600\">\n                          {userParticipation.progress} / {challenge.target} {challenge.unit}\n                        </div>\n                      </div>\n                    );\n                  })}\n                </div>\n              </div>\n\n              <div className=\"bg-white rounded-lg shadow-lg p-6\">\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">\n                  Défis Terminés\n                </h3>\n                <div className=\"space-y-3\">\n                  {completedChallenges.slice(0, 3).map(challenge => (\n                    <div key={challenge.id} className=\"p-3 border border-gray-200 rounded-lg\">\n                      <h4 className=\"font-medium text-gray-900 text-sm\">{challenge.name}</h4>\n                      <div className=\"flex items-center justify-between mt-2\">\n                        <span className=\"text-xs text-gray-600\">\n                          {challenge.participants.length} participants\n                        </span>\n                        <span className=\"text-xs text-green-600 font-medium\">Terminé</span>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Leaderboards Tab */}\n        {activeTab === 'leaderboards' && (\n          <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n            {/* Segments Leaderboards */}\n            <div className=\"lg:col-span-2\">\n              <div className=\"bg-white rounded-lg shadow-lg p-6\">\n                <h2 className=\"text-xl font-semibold text-gray-900 mb-6\">\n                  Classements des Segments\n                </h2>\n                \n                <div className=\"space-y-6\">\n                  {segments.slice(0, 5).map(segment => (\n                    <div key={segment.id} className=\"border border-gray-200 rounded-lg p-6\">\n                      <div className=\"flex items-start justify-between mb-4\">\n                        <div className=\"flex items-center space-x-3\">\n                          <span className=\"text-xl\">{getTypeIcon(segment.type)}</span>\n                          <div>\n                            <h3 className=\"font-semibold text-gray-900\">{segment.name}</h3>\n                            <p className=\"text-sm text-gray-600\">{segment.description}</p>\n                            <div className=\"flex items-center space-x-4 mt-1\">\n                              <span className=\"text-xs text-gray-500\">{segment.distance.toFixed(1)} km</span>\n                              <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(segment.difficulty)}`}>\n                                {segment.difficulty}\n                              </span>\n                              <span className=\"text-xs text-gray-500\">{segment.totalAttempts} tentatives</span>\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                      \n                      <div className=\"space-y-2\">\n                        <h4 className=\"font-medium text-gray-900 text-sm mb-3\">Top 5</h4>\n                        {segment.leaderboard.slice(0, 5).map((entry, index) => (\n                          <div key={index} className=\"flex items-center justify-between p-2 rounded-lg hover:bg-gray-50\">\n                            <div className=\"flex items-center space-x-3\">\n                              <span className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold ${\n                                index === 0 ? 'bg-yellow-100 text-yellow-800' :\n                                index === 1 ? 'bg-gray-100 text-gray-800' :\n                                index === 2 ? 'bg-orange-100 text-orange-800' :\n                                'bg-blue-100 text-blue-800'\n                              }`}>\n                                {entry.rank}\n                              </span>\n                              <div>\n                                <span className=\"font-medium text-gray-900 text-sm\">{entry.athlete}</span>\n                                {entry.isLocalLegend && (\n                                  <FiAward className=\"inline ml-2 h-4 w-4 text-yellow-500\" />\n                                )}\n                              </div>\n                            </div>\n                            <div className=\"text-right\">\n                              <div className=\"font-medium text-gray-900 text-sm\">\n                                {formatTime(entry.time)}\n                              </div>\n                              <div className=\"text-xs text-gray-500\">\n                                {formatPace(entry.pace)}\n                              </div>\n                            </div>\n                          </div>\n                        ))}\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </div>\n\n            {/* Leaderboard Stats */}\n            <div className=\"space-y-6\">\n              <div className=\"bg-white rounded-lg shadow-lg p-6\">\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">\n                  Mes Performances\n                </h3>\n                <div className=\"space-y-4\">\n                  <div className=\"flex items-center justify-between p-3 bg-yellow-50 rounded-lg\">\n                    <div className=\"flex items-center space-x-2\">\n                      <FiAward className=\"h-5 w-5 text-yellow-600\" />\n                      <span className=\"font-medium text-gray-900\">Légendes Locales</span>\n                    </div>\n                    <span className=\"text-lg font-bold text-yellow-600\">2</span>\n                  </div>\n                  \n                  <div className=\"flex items-center justify-between p-3 bg-blue-50 rounded-lg\">\n                    <div className=\"flex items-center space-x-2\">\n                      <FiTrendingUp className=\"h-5 w-5 text-blue-600\" />\n                      <span className=\"font-medium text-gray-900\">Top 10</span>\n                    </div>\n                    <span className=\"text-lg font-bold text-blue-600\">8</span>\n                  </div>\n                  \n                  <div className=\"flex items-center justify-between p-3 bg-green-50 rounded-lg\">\n                    <div className=\"flex items-center space-x-2\">\n                      <FiAward className=\"h-5 w-5 text-green-600\" />\n                      <span className=\"font-medium text-gray-900\">Records Personnels</span>\n                    </div>\n                    <span className=\"text-lg font-bold text-green-600\">15</span>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"bg-white rounded-lg shadow-lg p-6\">\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">\n                  Segments Populaires\n                </h3>\n                <div className=\"space-y-3\">\n                  {segments.slice(0, 5).map(segment => (\n                    <div key={segment.id} className=\"flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer\">\n                      <div>\n                        <h4 className=\"font-medium text-gray-900 text-sm\">{segment.name}</h4>\n                        <div className=\"flex items-center space-x-2 text-xs text-gray-600\">\n                          <span>{segment.distance.toFixed(1)} km</span>\n                          <span>•</span>\n                          <span>{segment.totalAttempts} tentatives</span>\n                        </div>\n                      </div>\n                      <div className=\"text-right\">\n                        <div className=\"text-sm font-medium text-gray-900\">\n                          {formatTime(segment.leaderboard[0]?.time || 0)}\n                        </div>\n                        <div className=\"text-xs text-gray-500\">Record</div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default Social;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,OAAO,EACPC,YAAY,EACZC,OAAO,EACPC,UAAU,EACVC,eAAe,EACfC,QAAQ,EACRC,OAAO,EACPC,QAAQ,EACRC,OAAO,EACPC,GAAG,EACHC,QAAQ,EACRC,MAAM,EACNC,OAAO,EACPC,QAAQ,EACRC,QAAQ,EACRC,OAAO,EACPC,UAAU,QACL,gBAAgB;AACvB,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SACEC,iBAAiB,EACjBC,kBAAkB,EAClBC,mBAAmB,EACnBC,yBAAyB,EACzBC,UAAU,EACVC,UAAU,QACL,sBAAsB;AAC7B,SAASC,gBAAgB,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,MAAMC,MAAM,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnB,MAAM;IAAEC;EAAK,CAAC,GAAGZ,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACa,SAAS,EAAEC,YAAY,CAAC,GAAGjC,QAAQ,CAAC,SAAS,CAAC;EAErD,MAAM,CAACkC,UAAU,EAAEC,aAAa,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACoC,QAAQ,EAAEC,WAAW,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACsC,WAAW,EAAEC,cAAc,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACwC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAAC0C,UAAU,EAAEC,aAAa,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC4C,cAAc,EAAEC,iBAAiB,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC8C,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACgD,YAAY,EAAEC,eAAe,CAAC,GAAGjD,QAAQ,CAAC;IAC/CkD,IAAI,EAAE,EAAE;IACRC,WAAW,EAAE,EAAE;IACfC,IAAI,EAAE,UAAU;IAChBC,MAAM,EAAE,EAAE;IACVC,QAAQ,EAAE,CAAC;IACXC,UAAU,EAAE;EACd,CAAC,CAAC;EAGFtD,SAAS,CAAC,MAAM;IACd;IACA,IAAI;MACF,MAAMuD,eAAe,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,kBAAkB,CAAC,IAAI,IAAI,CAAC;MACpF,MAAMC,eAAe,GAAGJ,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC,IAAI,IAAI,CAAC;MAClF,MAAME,gBAAgB,GAAGL,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC;MAEhF,IAAIJ,eAAe,CAACO,MAAM,GAAG,CAAC,EAAE;QAC9B5B,aAAa,CAACqB,eAAe,CAAC;MAChC;MACAX,iBAAiB,CAACgB,eAAe,CAAC;MAClC,IAAIC,gBAAgB,CAACC,MAAM,GAAG,CAAC,EAAE;QAC/BxB,cAAc,CAACuB,gBAAgB,CAAC;MAClC;IACF,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iDAAiD,EAAEA,KAAK,CAAC;IACzE;;IAEA;IACA,MAAME,SAAS,GAAG9C,iBAAiB,CAAC,EAAE,CAAC;IACvC,MAAM+C,cAAc,GAAG9C,kBAAkB,CAAC6C,SAAS,CAAC;IACpD,MAAME,YAAY,GAAG1C,gBAAgB,CAAC;IACpC;IACA;MACEwB,IAAI,EAAE,kBAAkB;MACxBmB,MAAM,EAAEC,KAAK,CAACC,IAAI,CAAC;QAAER,MAAM,EAAE;MAAG,CAAC,EAAE,CAACS,CAAC,EAAEC,CAAC,MAAM;QAC5CC,GAAG,EAAE,OAAO,GAAID,CAAC,GAAG,KAAM;QAC1BE,GAAG,EAAE,MAAM,GAAIF,CAAC,GAAG,KAAM;QACzBG,SAAS,EAAE,GAAG,GAAGC,IAAI,CAACC,GAAG,CAACL,CAAC,CAAC,GAAG;MACjC,CAAC,CAAC,CAAC;MACHrB,IAAI,EAAE;IACR,CAAC,EACD;MACEF,IAAI,EAAE,qBAAqB;MAC3BmB,MAAM,EAAEC,KAAK,CAACC,IAAI,CAAC;QAAER,MAAM,EAAE;MAAG,CAAC,EAAE,CAACS,CAAC,EAAEC,CAAC,MAAM;QAC5CC,GAAG,EAAE,OAAO,GAAID,CAAC,GAAG,MAAO;QAC3BE,GAAG,EAAE,MAAM,GAAIF,CAAC,GAAG,MAAO;QAC1BG,SAAS,EAAE,GAAG,GAAGC,IAAI,CAACE,GAAG,CAACN,CAAC,CAAC,GAAG;MACjC,CAAC,CAAC,CAAC;MACHrB,IAAI,EAAE;IACR,CAAC,CACF,CAAC;;IAEF;IACA,IAAIlB,UAAU,CAAC6B,MAAM,KAAK,CAAC,EAAE;MAC3B5B,aAAa,CAACgC,cAAc,CAAC;IAC/B;IACA9B,WAAW,CAAC+B,YAAY,CAAC;IAEzB,IAAIrC,IAAI,IAAIO,WAAW,CAACyB,MAAM,KAAK,CAAC,EAAE;MACpC,MAAMiB,eAAe,GAAG1D,mBAAmB,CAAC4C,SAAS,EAAEnC,IAAI,CAACkD,EAAE,CAAC;MAC/D,MAAMC,WAAW,GAAG3D,yBAAyB,CAAC2C,SAAS,EAAEnC,IAAI,CAACkD,EAAE,EAAED,eAAe,CAAC;MAClFzC,cAAc,CAACyC,eAAe,CAAC;MAC/BvC,oBAAoB,CAACyC,WAAW,CAAC;IACnC;EACF,CAAC,EAAE,CAACnD,IAAI,CAAC,CAAC;EAEV,MAAMoD,eAAe,GAAG7C,WAAW,CAAC8C,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAK,UAAU,CAAC;EACxE,MAAMC,eAAe,GAAGjD,WAAW,CAAC8C,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAK,SAAS,CAAC;EACvE,MAAME,gBAAgB,GAAGtD,UAAU,CAACkD,MAAM,CAACK,CAAC,IAAIA,CAAC,CAACC,QAAQ,CAAC;EAC3D,MAAMC,mBAAmB,GAAGzD,UAAU,CAACkD,MAAM,CAACK,CAAC,IAAI,CAACA,CAAC,CAACC,QAAQ,CAAC;EAE/D,MAAME,kBAAkB,GAAIC,YAAY,IAAK;IAC3CtD,cAAc,CAACuD,IAAI,IAAIA,IAAI,CAACC,GAAG,CAACV,CAAC,IAC/BA,CAAC,CAACJ,EAAE,KAAKY,YAAY,GAAG;MAAE,GAAGR,CAAC;MAAEC,MAAM,EAAE;IAAW,CAAC,GAAGD,CACzD,CAAC,CAAC;EACJ,CAAC;EAED,MAAMW,kBAAkB,GAAIH,YAAY,IAAK;IAC3CtD,cAAc,CAACuD,IAAI,IAAIA,IAAI,CAACV,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACJ,EAAE,KAAKY,YAAY,CAAC,CAAC;EACjE,CAAC;EAED,MAAMI,mBAAmB,GAAIC,WAAW,IAAK;IAC3C,MAAMC,iBAAiB,GAAGjE,UAAU,CAAC6D,GAAG,CAACK,SAAS,IAAI;MACpD,IAAIA,SAAS,CAACnB,EAAE,KAAKiB,WAAW,EAAE;QAChC,MAAMG,cAAc,GAAG;UACrBtE,IAAI,EAAE;YACJkD,EAAE,EAAElD,IAAI,CAACkD,EAAE;YACXqB,SAAS,EAAEvE,IAAI,CAACuE,SAAS;YACzBC,QAAQ,EAAExE,IAAI,CAACwE,QAAQ;YACvBC,MAAM,EAAEzE,IAAI,CAACyE,MAAM,IAAI,oCAAoCzE,IAAI,CAACuE,SAAS,IAAIvE,IAAI,CAACwE,QAAQ;UAC5F,CAAC;UACDE,QAAQ,EAAE,CAAC;UACXC,IAAI,EAAEN,SAAS,CAACO,YAAY,CAAC5C,MAAM,GAAG,CAAC;UACvC6C,QAAQ,EAAE,IAAIC,IAAI,CAAC,CAAC;UACpBC,WAAW,EAAE,KAAK;UAClBC,YAAY,EAAE,IAAIF,IAAI,CAAC;QACzB,CAAC;QAED,OAAO;UACL,GAAGT,SAAS;UACZO,YAAY,EAAE,CAAC,GAAGP,SAAS,CAACO,YAAY,EAAEN,cAAc;QAC1D,CAAC;MACH;MACA,OAAOD,SAAS;IAClB,CAAC,CAAC;IAEFjE,aAAa,CAACgE,iBAAiB,CAAC;IAChCxC,YAAY,CAACqD,OAAO,CAAC,kBAAkB,EAAEvD,IAAI,CAACwD,SAAS,CAACd,iBAAiB,CAAC,CAAC;EAC7E,CAAC;EAED,MAAMe,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAI,CAAClE,YAAY,CAACE,IAAI,IAAI,CAACF,YAAY,CAACK,MAAM,EAAE;IAEhD,MAAM+C,SAAS,GAAG;MAChBnB,EAAE,EAAE,aAAa4B,IAAI,CAACM,GAAG,CAAC,CAAC,EAAE;MAC7BjE,IAAI,EAAEF,YAAY,CAACE,IAAI;MACvBC,WAAW,EAAEH,YAAY,CAACG,WAAW;MACrCC,IAAI,EAAEJ,YAAY,CAACI,IAAI;MACvBC,MAAM,EAAE+D,UAAU,CAACpE,YAAY,CAACK,MAAM,CAAC;MACvCC,QAAQ,EAAEN,YAAY,CAACM,QAAQ;MAC/BC,UAAU,EAAEP,YAAY,CAACO,UAAU;MACnCmC,QAAQ,EAAE,IAAI;MACd2B,SAAS,EAAE,IAAIR,IAAI,CAAC,CAAC;MACrBS,OAAO,EAAE,IAAIT,IAAI,CAACA,IAAI,CAACM,GAAG,CAAC,CAAC,GAAGnE,YAAY,CAACM,QAAQ,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;MAC3EiE,SAAS,EAAE,CAAAxF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEuE,SAAS,KAAI,aAAa;MAC3CK,YAAY,EAAE,CAAC;QACb5E,IAAI,EAAE;UACJkD,EAAE,EAAE,CAAAlD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkD,EAAE,KAAI,OAAO;UACvBqB,SAAS,EAAE,CAAAvE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEuE,SAAS,KAAI,aAAa;UAC3CC,QAAQ,EAAE,CAAAxE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwE,QAAQ,KAAI,EAAE;UAC9BC,MAAM,EAAE,CAAAzE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyE,MAAM,KAAI,oCAAoCzE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEuE,SAAS,IAAIvE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwE,QAAQ;QAC/F,CAAC;QACDE,QAAQ,EAAE,CAAC;QACXC,IAAI,EAAE,CAAC;QACPE,QAAQ,EAAE,IAAIC,IAAI,CAAC,CAAC;QACpBC,WAAW,EAAE,KAAK;QAClBC,YAAY,EAAE,IAAIF,IAAI,CAAC;MACzB,CAAC,CAAC;MACFW,IAAI,EAAEC,gBAAgB,CAACzE,YAAY,CAACI,IAAI;IAC1C,CAAC;IAED,MAAM+C,iBAAiB,GAAG,CAACC,SAAS,EAAE,GAAGlE,UAAU,CAAC;IACpDC,aAAa,CAACgE,iBAAiB,CAAC;IAChCxC,YAAY,CAACqD,OAAO,CAAC,kBAAkB,EAAEvD,IAAI,CAACwD,SAAS,CAACd,iBAAiB,CAAC,CAAC;IAE3EpD,sBAAsB,CAAC,KAAK,CAAC;IAC7BE,eAAe,CAAC;MACdC,IAAI,EAAE,EAAE;MACRC,WAAW,EAAE,EAAE;MACfC,IAAI,EAAE,UAAU;MAChBC,MAAM,EAAE,EAAE;MACVC,QAAQ,EAAE,CAAC;MACXC,UAAU,EAAE;IACd,CAAC,CAAC;EACJ,CAAC;EAED,MAAMmE,eAAe,GAAIxB,WAAW,IAAK;IACvC,MAAMC,iBAAiB,GAAGjE,UAAU,CAACkD,MAAM,CAACK,CAAC,IAAIA,CAAC,CAACR,EAAE,KAAKiB,WAAW,CAAC;IACtE/D,aAAa,CAACgE,iBAAiB,CAAC;IAChCxC,YAAY,CAACqD,OAAO,CAAC,kBAAkB,EAAEvD,IAAI,CAACwD,SAAS,CAACd,iBAAiB,CAAC,CAAC;EAC7E,CAAC;EAED,MAAMwB,uBAAuB,GAAGA,CAACzB,WAAW,EAAEO,QAAQ,KAAK;IACzD,MAAMN,iBAAiB,GAAGjE,UAAU,CAAC6D,GAAG,CAACK,SAAS,IAAI;MACpD,IAAIA,SAAS,CAACnB,EAAE,KAAKiB,WAAW,EAAE;QAChC,MAAM0B,mBAAmB,GAAGxB,SAAS,CAACO,YAAY,CAACZ,GAAG,CAAC8B,CAAC,IAAI;UAC1D,IAAIA,CAAC,CAAC9F,IAAI,CAACkD,EAAE,MAAKlD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkD,EAAE,GAAE;YAC1B,OAAO;cACL,GAAG4C,CAAC;cACJpB,QAAQ,EAAE5B,IAAI,CAACiD,GAAG,CAACrB,QAAQ,EAAEL,SAAS,CAAC/C,MAAM,CAAC;cAC9CyD,WAAW,EAAEL,QAAQ,IAAIL,SAAS,CAAC/C,MAAM;cACzC0D,YAAY,EAAE,IAAIF,IAAI,CAAC;YACzB,CAAC;UACH;UACA,OAAOgB,CAAC;QACV,CAAC,CAAC;;QAEF;QACA,MAAME,kBAAkB,GAAGH,mBAAmB,CAACI,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACzB,QAAQ,GAAGwB,CAAC,CAACxB,QAAQ,CAAC;QACtFsB,kBAAkB,CAACI,OAAO,CAAC,CAACN,CAAC,EAAEO,KAAK,KAAK;UACvCP,CAAC,CAACnB,IAAI,GAAG0B,KAAK,GAAG,CAAC;QACpB,CAAC,CAAC;QAEF,OAAO;UACL,GAAGhC,SAAS;UACZO,YAAY,EAAEoB;QAChB,CAAC;MACH;MACA,OAAO3B,SAAS;IAClB,CAAC,CAAC;IAEFjE,aAAa,CAACgE,iBAAiB,CAAC;IAChCxC,YAAY,CAACqD,OAAO,CAAC,kBAAkB,EAAEvD,IAAI,CAACwD,SAAS,CAACd,iBAAiB,CAAC,CAAC;EAC7E,CAAC;EAED,MAAMkC,eAAe,GAAGlD,eAAe,CAACC,MAAM,CAACkD,UAAU,IACvDA,UAAU,CAACC,KAAK,CAACjC,SAAS,CAACkC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC/F,UAAU,CAAC8F,WAAW,CAAC,CAAC,CAAC,IAC3EF,UAAU,CAACC,KAAK,CAAChC,QAAQ,CAACiC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC/F,UAAU,CAAC8F,WAAW,CAAC,CAAC,CAC3E,CAAC;EAED,MAAME,kBAAkB,GAAInF,UAAU,IAAK;IACzC,QAAQA,UAAU;MAChB,KAAK,QAAQ;QAAE,OAAO,6BAA6B;MACnD,KAAK,QAAQ;QAAE,OAAO,+BAA+B;MACrD,KAAK,WAAW;QAAE,OAAO,yBAAyB;MAClD;QAAS,OAAO,2BAA2B;IAC7C;EACF,CAAC;EAED,MAAMoF,WAAW,GAAIvF,IAAI,IAAK;IAC5B,QAAQA,IAAI;MACV,KAAK,SAAS;QAAE,OAAO,OAAO;MAC9B,KAAK,SAAS;QAAE,OAAO,OAAO;MAC9B,KAAK,QAAQ;QAAE,OAAO,IAAI;MAC1B,KAAK,SAAS;QAAE,OAAO,OAAO;MAC9B;QAAS,OAAO,IAAI;IACtB;EACF,CAAC;EAED,MAAMqE,gBAAgB,GAAIrE,IAAI,IAAK;IACjC,QAAQA,IAAI;MACV,KAAK,UAAU;QAAE,OAAO,IAAI;MAC5B,KAAK,YAAY;QAAE,OAAO,OAAO;MACjC,KAAK,MAAM;QAAE,OAAO,IAAI;MACxB,KAAK,QAAQ;QAAE,OAAO,IAAI;MAC1B;QAAS,OAAO,IAAI;IACtB;EACF,CAAC;EAED,MAAMwF,WAAW,GAAIC,QAAQ,IAAK;IAChC,MAAMC,WAAW,GAAG;MAClB7D,EAAE,EAAE4B,IAAI,CAACM,GAAG,CAAC,CAAC;MACdpF,IAAI,EAAE;QACJkD,EAAE,EAAE,CAAAlD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkD,EAAE,KAAI,OAAO;QACvBqB,SAAS,EAAE,CAAAvE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEuE,SAAS,KAAI,aAAa;QAC3CC,QAAQ,EAAE,CAAAxE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwE,QAAQ,KAAI,EAAE;QAC9BC,MAAM,EAAE,CAAAzE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyE,MAAM,KAAI,oCAAoCzE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEuE,SAAS,IAAIvE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwE,QAAQ;MAC/F,CAAC;MACD,GAAGsC,QAAQ;MACXE,SAAS,EAAE,IAAIlC,IAAI,CAAC,CAAC;MACrBmC,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE;IACZ,CAAC;IAED,MAAMC,iBAAiB,GAAG,CAACJ,WAAW,EAAE,GAAGlG,cAAc,CAAC;IAC1DC,iBAAiB,CAACqG,iBAAiB,CAAC;IACpCvF,YAAY,CAACqD,OAAO,CAAC,gBAAgB,EAAEvD,IAAI,CAACwD,SAAS,CAACiC,iBAAiB,CAAC,CAAC;EAC3E,CAAC;EAED,MAAMC,YAAY,GAAIC,UAAU,IAAK;IACnC,MAAMF,iBAAiB,GAAGtG,cAAc,CAACmD,GAAG,CAAC8C,QAAQ,IAAI;MACvD,IAAIA,QAAQ,CAAC5D,EAAE,KAAKmE,UAAU,EAAE;QAC9B,MAAMC,QAAQ,GAAGR,QAAQ,CAACG,KAAK,CAACP,QAAQ,CAAC1G,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkD,EAAE,CAAC;QAClD,OAAO;UACL,GAAG4D,QAAQ;UACXG,KAAK,EAAEK,QAAQ,GACXR,QAAQ,CAACG,KAAK,CAAC5D,MAAM,CAACH,EAAE,IAAIA,EAAE,MAAKlD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkD,EAAE,EAAC,GAC5C,CAAC,GAAG4D,QAAQ,CAACG,KAAK,EAAEjH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkD,EAAE;QAClC,CAAC;MACH;MACA,OAAO4D,QAAQ;IACjB,CAAC,CAAC;IAEFhG,iBAAiB,CAACqG,iBAAiB,CAAC;IACpCvF,YAAY,CAACqD,OAAO,CAAC,gBAAgB,EAAEvD,IAAI,CAACwD,SAAS,CAACiC,iBAAiB,CAAC,CAAC;EAC3E,CAAC;EAED,MAAMI,UAAU,GAAGA,CAACF,UAAU,EAAEG,OAAO,KAAK;IAC1C,MAAML,iBAAiB,GAAGtG,cAAc,CAACmD,GAAG,CAAC8C,QAAQ,IAAI;MACvD,IAAIA,QAAQ,CAAC5D,EAAE,KAAKmE,UAAU,EAAE;QAC9B,MAAMI,UAAU,GAAG;UACjBvE,EAAE,EAAE4B,IAAI,CAACM,GAAG,CAAC,CAAC;UACdpF,IAAI,EAAE;YACJkD,EAAE,EAAE,CAAAlD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkD,EAAE,KAAI,OAAO;YACvBqB,SAAS,EAAE,CAAAvE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEuE,SAAS,KAAI,aAAa;YAC3CC,QAAQ,EAAE,CAAAxE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwE,QAAQ,KAAI;UAC9B,CAAC;UACDkD,IAAI,EAAEF,OAAO;UACbR,SAAS,EAAE,IAAIlC,IAAI,CAAC;QACtB,CAAC;QAED,OAAO;UACL,GAAGgC,QAAQ;UACXI,QAAQ,EAAE,CAAC,GAAGJ,QAAQ,CAACI,QAAQ,EAAEO,UAAU;QAC7C,CAAC;MACH;MACA,OAAOX,QAAQ;IACjB,CAAC,CAAC;IAEFhG,iBAAiB,CAACqG,iBAAiB,CAAC;IACpCvF,YAAY,CAACqD,OAAO,CAAC,gBAAgB,EAAEvD,IAAI,CAACwD,SAAS,CAACiC,iBAAiB,CAAC,CAAC;EAC3E,CAAC;EAED,oBACEtH,OAAA;IAAK8H,SAAS,EAAC,yBAAyB;IAAAC,QAAA,eACtC/H,OAAA;MAAK8H,SAAS,EAAC,6CAA6C;MAAAC,QAAA,gBAE1D/H,OAAA;QAAK8H,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/B/H,OAAA;UAAI8H,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAEtD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLnI,OAAA;UAAG8H,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EAAC;QAGvD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGNnI,OAAA;QAAK8H,SAAS,EAAC,gDAAgD;QAAAC,QAAA,gBAC7D/H,OAAA;UACEoI,OAAO,EAAEA,CAAA,KAAM/H,YAAY,CAAC,SAAS,CAAE;UACvCyH,SAAS,EAAE,qEACT1H,SAAS,KAAK,SAAS,GACnB,kCAAkC,GAClC,mCAAmC,EACtC;UAAA2H,QAAA,gBAEH/H,OAAA,CAAC1B,OAAO;YAACwJ,SAAS,EAAC;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,UAC7B,EAAC5E,eAAe,CAACpB,MAAM,EAAC,GAChC;QAAA;UAAA6F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTnI,OAAA;UACEoI,OAAO,EAAEA,CAAA,KAAM/H,YAAY,CAAC,YAAY,CAAE;UAC1CyH,SAAS,EAAE,qEACT1H,SAAS,KAAK,YAAY,GACtB,kCAAkC,GAClC,mCAAmC,EACtC;UAAA2H,QAAA,gBAEH/H,OAAA,CAACrB,QAAQ;YAACmJ,SAAS,EAAC;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,cAC7B,EAACvE,gBAAgB,CAACzB,MAAM,EAAC,GAClC;QAAA;UAAA6F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTnI,OAAA;UACEoI,OAAO,EAAEA,CAAA,KAAM/H,YAAY,CAAC,cAAc,CAAE;UAC5CyH,SAAS,EAAE,qEACT1H,SAAS,KAAK,cAAc,GACxB,kCAAkC,GAClC,mCAAmC,EACtC;UAAA2H,QAAA,gBAEH/H,OAAA,CAACzB,YAAY;YAACuJ,SAAS,EAAC;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAE1C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAGL/H,SAAS,KAAK,SAAS,iBACtBJ,OAAA;QAAK8H,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBAEpD/H,OAAA;UAAK8H,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5B/H,OAAA;YAAK8H,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChD/H,OAAA;cAAK8H,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrD/H,OAAA;gBAAI8H,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,GAAC,YACxC,EAACxE,eAAe,CAACpB,MAAM,EAAC,GACpC;cAAA;gBAAA6F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLnI,OAAA;gBAAK8H,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACvB/H,OAAA,CAAChB,QAAQ;kBAAC8I,SAAS,EAAC;gBAA6C;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACpEnI,OAAA;kBACEwB,IAAI,EAAC,MAAM;kBACX6G,WAAW,EAAC,sBAAsB;kBAClCC,KAAK,EAAExH,UAAW;kBAClByH,QAAQ,EAAGC,CAAC,IAAKzH,aAAa,CAACyH,CAAC,CAAC/G,MAAM,CAAC6G,KAAK,CAAE;kBAC/CR,SAAS,EAAC;gBAAuG;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENnI,OAAA;cAAK8H,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvBtB,eAAe,CAACtC,GAAG,CAACuC,UAAU,iBAC7B1G,OAAA;gBAAyB8H,SAAS,EAAC,0FAA0F;gBAAAC,QAAA,gBAC3H/H,OAAA;kBAAK8H,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1C/H,OAAA;oBACEyI,GAAG,EAAE/B,UAAU,CAACC,KAAK,CAAC/B,MAAO;oBAC7B8D,GAAG,EAAE,GAAGhC,UAAU,CAACC,KAAK,CAACjC,SAAS,IAAIgC,UAAU,CAACC,KAAK,CAAChC,QAAQ,EAAG;oBAClEmD,SAAS,EAAC;kBAAwB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC,CAAC,eACFnI,OAAA;oBAAA+H,QAAA,gBACE/H,OAAA;sBAAI8H,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,GACtCrB,UAAU,CAACC,KAAK,CAACjC,SAAS,EAAC,GAAC,EAACgC,UAAU,CAACC,KAAK,CAAChC,QAAQ;oBAAA;sBAAAqD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrD,CAAC,eACLnI,OAAA;sBAAK8H,SAAS,EAAC,mDAAmD;sBAAAC,QAAA,gBAChE/H,OAAA;wBAAM8H,SAAS,EAAC,mBAAmB;wBAAAC,QAAA,gBACjC/H,OAAA,CAACnB,QAAQ;0BAACiJ,SAAS,EAAC;wBAAc;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,EACpCzB,UAAU,CAACC,KAAK,CAACgC,IAAI;sBAAA;wBAAAX,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClB,CAAC,eACPnI,OAAA;wBAAM8H,SAAS,EAAC,mBAAmB;wBAAAC,QAAA,gBACjC/H,OAAA,CAACpB,OAAO;0BAACkJ,SAAS,EAAC;wBAAc;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,EACnCzB,UAAU,CAACC,KAAK,CAACiC,QAAQ,GAAG,UAAU,GAAG,YAAY;sBAAA;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClD,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACNnI,OAAA;sBAAK8H,SAAS,EAAC,4BAA4B;sBAAAC,QAAA,GACxCrB,UAAU,CAACmC,aAAa,EAAC,yBAAkB,EAACnC,UAAU,CAACoC,gBAAgB,EAAC,4BAC3E;oBAAA;sBAAAd,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNnI,OAAA;kBAAK8H,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1C/H,OAAA;oBAAQ8H,SAAS,EAAC,+CAA+C;oBAAAC,QAAA,eAC/D/H,OAAA,CAACtB,eAAe;sBAACoJ,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjC,CAAC,eACTnI,OAAA;oBAAK8H,SAAS,EAAC,YAAY;oBAAAC,QAAA,gBACzB/H,OAAA;sBAAK8H,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,GAC/CrB,UAAU,CAACC,KAAK,CAACoC,KAAK,CAACC,aAAa,EAAC,KACxC;oBAAA;sBAAAhB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNnI,OAAA;sBAAK8H,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,GACnCrB,UAAU,CAACC,KAAK,CAACoC,KAAK,CAACE,eAAe,EAAC,eAC1C;oBAAA;sBAAAjB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA,GAtCEzB,UAAU,CAACrD,EAAE;gBAAA2E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAuClB,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNnI,OAAA;UAAK8H,SAAS,EAAC,WAAW;UAAAC,QAAA,GAEvBpE,eAAe,CAACxB,MAAM,GAAG,CAAC,iBACzBnC,OAAA;YAAK8H,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChD/H,OAAA;cAAI8H,SAAS,EAAC,0CAA0C;cAAAC,QAAA,GAAC,wBACpC,EAACpE,eAAe,CAACxB,MAAM,EAAC,GAC7C;YAAA;cAAA6F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLnI,OAAA;cAAK8H,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvBpE,eAAe,CAACQ,GAAG,CAAC+E,OAAO,iBAC1BlJ,OAAA;gBAAsB8H,SAAS,EAAC,yEAAyE;gBAAAC,QAAA,gBACvG/H,OAAA;kBAAK8H,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1C/H,OAAA;oBACEyI,GAAG,EAAES,OAAO,CAACC,KAAK,CAACvE,MAAO;oBAC1B8D,GAAG,EAAE,GAAGQ,OAAO,CAACC,KAAK,CAACzE,SAAS,IAAIwE,OAAO,CAACC,KAAK,CAACxE,QAAQ,EAAG;oBAC5DmD,SAAS,EAAC;kBAAwB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC,CAAC,eACFnI,OAAA;oBAAA+H,QAAA,gBACE/H,OAAA;sBAAI8H,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,GAC9CmB,OAAO,CAACC,KAAK,CAACzE,SAAS,EAAC,GAAC,EAACwE,OAAO,CAACC,KAAK,CAACxE,QAAQ;oBAAA;sBAAAqD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/C,CAAC,eACLnI,OAAA;sBAAG8H,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAEmB,OAAO,CAACC,KAAK,CAACR;oBAAI;sBAAAX,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1D,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNnI,OAAA;kBAAK8H,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC7B/H,OAAA;oBACEoI,OAAO,EAAEA,CAAA,KAAMpE,kBAAkB,CAACkF,OAAO,CAAC7F,EAAE,CAAE;oBAC9CyE,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,eAExD/H,OAAA,CAAClB,OAAO;sBAACgJ,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzB,CAAC,eACTnI,OAAA;oBACEoI,OAAO,EAAEA,CAAA,KAAMhE,kBAAkB,CAAC8E,OAAO,CAAC7F,EAAE,CAAE;oBAC9CyE,SAAS,EAAC,0CAA0C;oBAAAC,QAAA,eAEpD/H,OAAA,CAACjB,GAAG;sBAAC+I,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA,GA3BEe,OAAO,CAAC7F,EAAE;gBAAA2E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA4Bf,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eAGDnI,OAAA;YAAK8H,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChD/H,OAAA;cAAI8H,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAEzD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLnI,OAAA;cAAK8H,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvBnH,iBAAiB,CAACwI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACjF,GAAG,CAACkF,UAAU,iBAC3CrJ,OAAA;gBAA8B8H,SAAS,EAAC,yEAAyE;gBAAAC,QAAA,gBAC/G/H,OAAA;kBAAK8H,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1C/H,OAAA;oBACEyI,GAAG,EAAEY,UAAU,CAAClJ,IAAI,CAACyE,MAAO;oBAC5B8D,GAAG,EAAE,GAAGW,UAAU,CAAClJ,IAAI,CAACuE,SAAS,IAAI2E,UAAU,CAAClJ,IAAI,CAACwE,QAAQ,EAAG;oBAChEmD,SAAS,EAAC;kBAAwB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC,CAAC,eACFnI,OAAA;oBAAA+H,QAAA,gBACE/H,OAAA;sBAAI8H,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,GAC9CsB,UAAU,CAAClJ,IAAI,CAACuE,SAAS,EAAC,GAAC,EAAC2E,UAAU,CAAClJ,IAAI,CAACwE,QAAQ;oBAAA;sBAAAqD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnD,CAAC,eACLnI,OAAA;sBAAG8H,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAEsB,UAAU,CAACC;oBAAM;sBAAAtB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNnI,OAAA;kBAAQ8H,SAAS,EAAC,4CAA4C;kBAAAC,QAAA,eAC5D/H,OAAA,CAACvB,UAAU;oBAACqJ,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B,CAAC;cAAA,GAhBDkB,UAAU,CAAClJ,IAAI,CAACkD,EAAE;gBAAA2E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAiBvB,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA/H,SAAS,KAAK,YAAY,iBACzBJ,OAAA;QAAK8H,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBAEpD/H,OAAA;UAAK8H,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5B/H,OAAA;YAAK8H,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChD/H,OAAA;cAAK8H,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrD/H,OAAA;gBAAI8H,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,GAAC,mBACpC,EAACnE,gBAAgB,CAACzB,MAAM,EAAC,GACzC;cAAA;gBAAA6F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLnI,OAAA;gBACEoI,OAAO,EAAEA,CAAA,KAAMjH,sBAAsB,CAAC,IAAI,CAAE;gBAC5C2G,SAAS,EAAC,mGAAmG;gBAAAC,QAAA,gBAE7G/H,OAAA,CAACf,MAAM;kBAAC6I,SAAS,EAAC;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,uBAE7B;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENnI,OAAA;cAAK8H,SAAS,EAAC,YAAY;cAAAC,QAAA,EACxBnE,gBAAgB,CAACO,GAAG,CAACK,SAAS,iBAC7BxE,OAAA;gBAAwB8H,SAAS,EAAC,yEAAyE;gBAAAC,QAAA,gBACzG/H,OAAA;kBAAK8H,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,gBACpD/H,OAAA;oBAAK8H,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,gBAC1C/H,OAAA;sBAAM8H,SAAS,EAAC,UAAU;sBAAAC,QAAA,EAAElC,gBAAgB,CAACrB,SAAS,CAAChD,IAAI;oBAAC;sBAAAwG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACpEnI,OAAA;sBAAA+H,QAAA,gBACE/H,OAAA;wBAAI8H,SAAS,EAAC,6BAA6B;wBAAAC,QAAA,EAAEvD,SAAS,CAAClD;sBAAI;wBAAA0G,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACjEnI,OAAA;wBAAG8H,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EAAEvD,SAAS,CAACjD;sBAAW;wBAAAyG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7D,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNnI,OAAA;oBAAM8H,SAAS,EAAE,8CACftD,SAAS,CAAC+E,QAAQ,KAAK,MAAM,GAAG,+BAA+B,GAAG,2BAA2B,EAC5F;oBAAAxB,QAAA,EACAvD,SAAS,CAAC+E,QAAQ,KAAK,MAAM,GAAG,QAAQ,GAAG;kBAAY;oBAAAvB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eAENnI,OAAA;kBAAK8H,SAAS,EAAC,4CAA4C;kBAAAC,QAAA,gBACzD/H,OAAA;oBAAK8H,SAAS,EAAC,aAAa;oBAAAC,QAAA,gBAC1B/H,OAAA;sBAAK8H,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,EAAEvD,SAAS,CAACO,YAAY,CAAC5C;oBAAM;sBAAA6F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACtFnI,OAAA;sBAAK8H,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAAY;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtD,CAAC,eACNnI,OAAA;oBAAK8H,SAAS,EAAC,aAAa;oBAAAC,QAAA,gBAC1B/H,OAAA;sBAAK8H,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,EAAEvD,SAAS,CAAC/C;oBAAM;sBAAAuG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACzEnI,OAAA;sBAAK8H,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAEvD,SAAS,CAACgF;oBAAI;sBAAAxB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1D,CAAC,eACNnI,OAAA;oBAAK8H,SAAS,EAAC,aAAa;oBAAAC,QAAA,gBAC1B/H,OAAA;sBAAK8H,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,EAAEvD,SAAS,CAAC9C;oBAAQ;sBAAAsG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC3EnI,OAAA;sBAAK8H,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAAK;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/C,CAAC,eACNnI,OAAA;oBAAK8H,SAAS,EAAC,aAAa;oBAAAC,QAAA,gBAC1B/H,OAAA;sBAAK8H,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,EAC7C9E,IAAI,CAACwG,IAAI,CAAC,CAACjF,SAAS,CAACkB,OAAO,GAAG,IAAIT,IAAI,CAAC,CAAC,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;oBAAC;sBAAA+C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjE,CAAC,eACNnI,OAAA;sBAAK8H,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAAc;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENnI,OAAA;kBAAK8H,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,gBAChD/H,OAAA;oBAAK8H,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,gBACpC/H,OAAA;sBAAM8H,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAM;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAAC3D,SAAS,CAACkF,KAAK;kBAAA;oBAAA1B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzD,CAAC,eACNnI,OAAA;oBACEoI,OAAO,EAAEA,CAAA,KAAM/D,mBAAmB,CAACG,SAAS,CAACnB,EAAE,CAAE;oBACjDyE,SAAS,EAAC,yFAAyF;oBAAAC,QAAA,EACpG;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA,GA/CE3D,SAAS,CAACnB,EAAE;gBAAA2E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAgDjB,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNnI,OAAA;UAAK8H,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB/H,OAAA;YAAK8H,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChD/H,OAAA;cAAI8H,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAEzD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLnI,OAAA;cAAK8H,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvBnE,gBAAgB,CAACwF,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACjF,GAAG,CAACK,SAAS,IAAI;gBAC7C,MAAMmF,iBAAiB,GAAGnF,SAAS,CAACO,YAAY,CAAC6E,IAAI,CAAC3D,CAAC,IAAIA,CAAC,CAAC9F,IAAI,CAACkD,EAAE,MAAKlD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkD,EAAE,EAAC;gBAClF,IAAI,CAACsG,iBAAiB,EAAE,OAAO,IAAI;gBAEnC,MAAME,kBAAkB,GAAIF,iBAAiB,CAAC9E,QAAQ,GAAGL,SAAS,CAAC/C,MAAM,GAAI,GAAG;gBAEhF,oBACEzB,OAAA;kBAAwB8H,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,gBACvE/H,OAAA;oBAAK8H,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,gBACrD/H,OAAA;sBAAI8H,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAEvD,SAAS,CAAClD;oBAAI;sBAAA0G,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACvEnI,OAAA;sBAAM8H,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,GAAC,GAAC,EAAC4B,iBAAiB,CAAC7E,IAAI;oBAAA;sBAAAkD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrE,CAAC,eACNnI,OAAA;oBAAK8H,SAAS,EAAC,0CAA0C;oBAAAC,QAAA,eACvD/H,OAAA;sBACE8H,SAAS,EAAC,8BAA8B;sBACxCgC,KAAK,EAAE;wBAAEC,KAAK,EAAE,GAAG9G,IAAI,CAACiD,GAAG,CAAC2D,kBAAkB,EAAE,GAAG,CAAC;sBAAI;oBAAE;sBAAA7B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACNnI,OAAA;oBAAK8H,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,GACnC4B,iBAAiB,CAAC9E,QAAQ,EAAC,KAAG,EAACL,SAAS,CAAC/C,MAAM,EAAC,GAAC,EAAC+C,SAAS,CAACgF,IAAI;kBAAA;oBAAAxB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9D,CAAC;gBAAA,GAbE3D,SAAS,CAACnB,EAAE;kBAAA2E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAcjB,CAAC;cAEV,CAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENnI,OAAA;YAAK8H,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChD/H,OAAA;cAAI8H,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAEzD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLnI,OAAA;cAAK8H,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvBhE,mBAAmB,CAACqF,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACjF,GAAG,CAACK,SAAS,iBAC5CxE,OAAA;gBAAwB8H,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,gBACvE/H,OAAA;kBAAI8H,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAEvD,SAAS,CAAClD;gBAAI;kBAAA0G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACvEnI,OAAA;kBAAK8H,SAAS,EAAC,wCAAwC;kBAAAC,QAAA,gBACrD/H,OAAA;oBAAM8H,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,GACpCvD,SAAS,CAACO,YAAY,CAAC5C,MAAM,EAAC,eACjC;kBAAA;oBAAA6F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACPnI,OAAA;oBAAM8H,SAAS,EAAC,oCAAoC;oBAAAC,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChE,CAAC;cAAA,GAPE3D,SAAS,CAACnB,EAAE;gBAAA2E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAQjB,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA/H,SAAS,KAAK,cAAc,iBAC3BJ,OAAA;QAAK8H,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBAEpD/H,OAAA;UAAK8H,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5B/H,OAAA;YAAK8H,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChD/H,OAAA;cAAI8H,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAEzD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAELnI,OAAA;cAAK8H,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvBvH,QAAQ,CAAC4I,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACjF,GAAG,CAAC6F,OAAO,iBAC/BhK,OAAA;gBAAsB8H,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,gBACrE/H,OAAA;kBAAK8H,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,eACpD/H,OAAA;oBAAK8H,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,gBAC1C/H,OAAA;sBAAM8H,SAAS,EAAC,SAAS;sBAAAC,QAAA,EAAEhB,WAAW,CAACiD,OAAO,CAACxI,IAAI;oBAAC;sBAAAwG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAC5DnI,OAAA;sBAAA+H,QAAA,gBACE/H,OAAA;wBAAI8H,SAAS,EAAC,6BAA6B;wBAAAC,QAAA,EAAEiC,OAAO,CAAC1I;sBAAI;wBAAA0G,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAC/DnI,OAAA;wBAAG8H,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EAAEiC,OAAO,CAACzI;sBAAW;wBAAAyG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAC9DnI,OAAA;wBAAK8H,SAAS,EAAC,kCAAkC;wBAAAC,QAAA,gBAC/C/H,OAAA;0BAAM8H,SAAS,EAAC,uBAAuB;0BAAAC,QAAA,GAAEiC,OAAO,CAACC,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC,EAAC,KAAG;wBAAA;0BAAAlC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eAC/EnI,OAAA;0BAAM8H,SAAS,EAAE,8CAA8ChB,kBAAkB,CAACkD,OAAO,CAACrI,UAAU,CAAC,EAAG;0BAAAoG,QAAA,EACrGiC,OAAO,CAACrI;wBAAU;0BAAAqG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACf,CAAC,eACPnI,OAAA;0BAAM8H,SAAS,EAAC,uBAAuB;0BAAAC,QAAA,GAAEiC,OAAO,CAACG,aAAa,EAAC,aAAW;wBAAA;0BAAAnC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9E,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENnI,OAAA;kBAAK8H,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBACxB/H,OAAA;oBAAI8H,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EAChE6B,OAAO,CAACI,WAAW,CAAChB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACjF,GAAG,CAAC,CAACkG,KAAK,EAAE7D,KAAK,kBAChDxG,OAAA;oBAAiB8H,SAAS,EAAC,mEAAmE;oBAAAC,QAAA,gBAC5F/H,OAAA;sBAAK8H,SAAS,EAAC,6BAA6B;sBAAAC,QAAA,gBAC1C/H,OAAA;wBAAM8H,SAAS,EAAE,2EACftB,KAAK,KAAK,CAAC,GAAG,+BAA+B,GAC7CA,KAAK,KAAK,CAAC,GAAG,2BAA2B,GACzCA,KAAK,KAAK,CAAC,GAAG,+BAA+B,GAC7C,2BAA2B,EAC1B;wBAAAuB,QAAA,EACAsC,KAAK,CAACvF;sBAAI;wBAAAkD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACP,CAAC,eACPnI,OAAA;wBAAA+H,QAAA,gBACE/H,OAAA;0BAAM8H,SAAS,EAAC,mCAAmC;0BAAAC,QAAA,EAAEsC,KAAK,CAACC;wBAAO;0BAAAtC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC,EACzEkC,KAAK,CAACE,aAAa,iBAClBvK,OAAA,CAACxB,OAAO;0BAACsJ,SAAS,EAAC;wBAAqC;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAC3D;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNnI,OAAA;sBAAK8H,SAAS,EAAC,YAAY;sBAAAC,QAAA,gBACzB/H,OAAA;wBAAK8H,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,EAC/CnI,UAAU,CAACyK,KAAK,CAACG,IAAI;sBAAC;wBAAAxC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpB,CAAC,eACNnI,OAAA;wBAAK8H,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EACnClI,UAAU,CAACwK,KAAK,CAACI,IAAI;sBAAC;wBAAAzC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA,GAxBE3B,KAAK;oBAAAwB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAyBV,CACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA,GAhDE6B,OAAO,CAAC3G,EAAE;gBAAA2E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAiDf,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNnI,OAAA;UAAK8H,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB/H,OAAA;YAAK8H,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChD/H,OAAA;cAAI8H,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAEzD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLnI,OAAA;cAAK8H,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxB/H,OAAA;gBAAK8H,SAAS,EAAC,+DAA+D;gBAAAC,QAAA,gBAC5E/H,OAAA;kBAAK8H,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1C/H,OAAA,CAACxB,OAAO;oBAACsJ,SAAS,EAAC;kBAAyB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC/CnI,OAAA;oBAAM8H,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,EAAC;kBAAgB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChE,CAAC,eACNnI,OAAA;kBAAM8H,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC,eAENnI,OAAA;gBAAK8H,SAAS,EAAC,6DAA6D;gBAAAC,QAAA,gBAC1E/H,OAAA;kBAAK8H,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1C/H,OAAA,CAACzB,YAAY;oBAACuJ,SAAS,EAAC;kBAAuB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAClDnI,OAAA;oBAAM8H,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD,CAAC,eACNnI,OAAA;kBAAM8H,SAAS,EAAC,iCAAiC;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD,CAAC,eAENnI,OAAA;gBAAK8H,SAAS,EAAC,8DAA8D;gBAAAC,QAAA,gBAC3E/H,OAAA;kBAAK8H,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1C/H,OAAA,CAACxB,OAAO;oBAACsJ,SAAS,EAAC;kBAAwB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC9CnI,OAAA;oBAAM8H,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,EAAC;kBAAkB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClE,CAAC,eACNnI,OAAA;kBAAM8H,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENnI,OAAA;YAAK8H,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChD/H,OAAA;cAAI8H,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAEzD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLnI,OAAA;cAAK8H,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvBvH,QAAQ,CAAC4I,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACjF,GAAG,CAAC6F,OAAO;gBAAA,IAAAU,qBAAA;gBAAA,oBAC/B1K,OAAA;kBAAsB8H,SAAS,EAAC,yGAAyG;kBAAAC,QAAA,gBACvI/H,OAAA;oBAAA+H,QAAA,gBACE/H,OAAA;sBAAI8H,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAEiC,OAAO,CAAC1I;oBAAI;sBAAA0G,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACrEnI,OAAA;sBAAK8H,SAAS,EAAC,mDAAmD;sBAAAC,QAAA,gBAChE/H,OAAA;wBAAA+H,QAAA,GAAOiC,OAAO,CAACC,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC,EAAC,KAAG;sBAAA;wBAAAlC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAC7CnI,OAAA;wBAAA+H,QAAA,EAAM;sBAAC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACdnI,OAAA;wBAAA+H,QAAA,GAAOiC,OAAO,CAACG,aAAa,EAAC,aAAW;sBAAA;wBAAAnC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5C,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNnI,OAAA;oBAAK8H,SAAS,EAAC,YAAY;oBAAAC,QAAA,gBACzB/H,OAAA;sBAAK8H,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAC/CnI,UAAU,CAAC,EAAA8K,qBAAA,GAAAV,OAAO,CAACI,WAAW,CAAC,CAAC,CAAC,cAAAM,qBAAA,uBAAtBA,qBAAA,CAAwBF,IAAI,KAAI,CAAC;oBAAC;sBAAAxC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3C,CAAC,eACNnI,OAAA;sBAAK8H,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAAM;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChD,CAAC;gBAAA,GAdE6B,OAAO,CAAC3G,EAAE;kBAAA2E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAef,CAAC;cAAA,CACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjI,EAAA,CA3vBID,MAAM;EAAA,QACOV,OAAO;AAAA;AAAAoL,EAAA,GADpB1K,MAAM;AA6vBZ,eAAeA,MAAM;AAAC,IAAA0K,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}