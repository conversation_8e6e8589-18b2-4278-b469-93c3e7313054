{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projet fitness\\\\DATA\\\\fitness-ui\\\\src\\\\pages\\\\Exercises.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { FiPlay, FiClock, FiZap, FiFilter, FiSearch, FiTarget, FiHeart, FiStar, FiInfo, FiPlus, FiTrendingUp, FiBookmark, FiVideo, FiBarChart2, FiCalendar, FiX } from 'react-icons/fi';\nimport { useAuth } from '../contexts/AuthContext';\nimport { exercises, exerciseCategories, muscleGroups, equipmentTypes, getDifficultyColor, getRecommendedExercises, searchExercises, createWorkoutPlan, toggleFavorite, loadFavorites, addToHistory, getExerciseHistory, calculateProgressStats, createCustomProgram, getExerciseVideo } from '../utils/exerciseUtils';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Exercises = () => {\n  _s();\n  var _exerciseCategories$s, _exerciseCategories$s2, _equipmentTypes$selec;\n  const {\n    user\n  } = useAuth();\n  const [selectedExercise, setSelectedExercise] = useState(null);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [showFilters, setShowFilters] = useState(false);\n  const [filters, setFilters] = useState({\n    category: 'all',\n    muscleGroup: 'all',\n    equipment: 'all',\n    difficulty: 'all',\n    maxDuration: null\n  });\n  const [activeTab, setActiveTab] = useState('all');\n  const [recommendedExercises, setRecommendedExercises] = useState([]);\n  const [favoriteExercises, setFavoriteExercises] = useState([]);\n  const [filteredExercises, setFilteredExercises] = useState(exercises);\n\n  // Nouveaux états pour les fonctionnalités avancées\n  const [exerciseHistory, setExerciseHistory] = useState([]);\n  const [showCreateProgram, setShowCreateProgram] = useState(false);\n  const [selectedExercisesForProgram, setSelectedExercisesForProgram] = useState([]);\n  const [showProgressModal, setShowProgressModal] = useState(false);\n  const [progressData, setProgressData] = useState(null);\n  const [showPerformanceModal, setShowPerformanceModal] = useState(false);\n  const [performanceData, setPerformanceData] = useState({\n    sets: 3,\n    reps: 12,\n    weight: '',\n    duration: '',\n    notes: ''\n  });\n  const [exerciseRatings, setExerciseRatings] = useState({});\n  const [exerciseComments, setExerciseComments] = useState({});\n  const [showRatingModal, setShowRatingModal] = useState(false);\n  const [ratingExercise, setRatingExercise] = useState(null);\n  useEffect(() => {\n    // Générer des recommandations basées sur le profil utilisateur\n    if (user) {\n      const recommendations = getRecommendedExercises(user, user.primaryGoal || 'general_fitness');\n      setRecommendedExercises(recommendations);\n    }\n\n    // Charger les favoris depuis localStorage\n    const savedFavorites = loadFavorites();\n    const favoriteExerciseObjects = exercises.filter(ex => savedFavorites.includes(ex.id));\n    setFavoriteExercises(favoriteExerciseObjects);\n\n    // Charger l'historique des exercices\n    const history = getExerciseHistory();\n    setExerciseHistory(history);\n\n    // Charger les notes et commentaires\n    try {\n      const savedRatings = JSON.parse(localStorage.getItem('exerciseRatings') || '{}');\n      const savedComments = JSON.parse(localStorage.getItem('exerciseComments') || '{}');\n      setExerciseRatings(savedRatings);\n      setExerciseComments(savedComments);\n    } catch (error) {\n      console.error('Erreur lors du chargement des notes:', error);\n    }\n  }, [user]);\n  useEffect(() => {\n    // Appliquer les filtres et la recherche\n    let results = exercises;\n    if (activeTab === 'recommended') {\n      results = recommendedExercises;\n    } else if (activeTab === 'favorites') {\n      results = favoriteExercises;\n    }\n    if (searchQuery || Object.values(filters).some(f => f !== 'all' && f !== null)) {\n      results = searchExercises(searchQuery, filters);\n    }\n    setFilteredExercises(results);\n  }, [searchQuery, filters, activeTab, recommendedExercises, favoriteExercises]);\n  const handleFilterChange = (filterType, value) => {\n    setFilters(prev => ({\n      ...prev,\n      [filterType]: value\n    }));\n  };\n  const clearFilters = () => {\n    setFilters({\n      category: 'all',\n      muscleGroup: 'all',\n      equipment: 'all',\n      difficulty: 'all',\n      maxDuration: null\n    });\n    setSearchQuery('');\n  };\n  const generateQuickWorkout = () => {\n    const selectedExercises = filteredExercises.slice(0, 4);\n    const workout = createWorkoutPlan(selectedExercises, 30);\n\n    // Ici on pourrait ouvrir une modal avec le programme généré\n    alert(`Programme généré !\\nDurée: ${workout.totalDuration} min\\nCalories estimées: ${workout.estimatedCalories}\\nExercices: ${workout.exercises.length}`);\n  };\n  const handleToggleFavorite = exerciseId => {\n    const savedFavorites = loadFavorites();\n    const isNowFavorite = toggleFavorite(exerciseId, savedFavorites, () => {});\n\n    // Mettre à jour l'état local\n    if (isNowFavorite) {\n      const exercise = exercises.find(ex => ex.id === exerciseId);\n      setFavoriteExercises(prev => [...prev, exercise]);\n    } else {\n      setFavoriteExercises(prev => prev.filter(ex => ex.id !== exerciseId));\n    }\n  };\n\n  // Ajouter un exercice à l'historique avec performance\n  const handleAddToHistory = exercise => {\n    const entry = addToHistory(exercise, performanceData);\n    if (entry) {\n      setExerciseHistory(prev => [entry, ...prev]);\n      setShowPerformanceModal(false);\n      setPerformanceData({\n        sets: 3,\n        reps: 12,\n        weight: '',\n        duration: '',\n        notes: ''\n      });\n      alert('Exercice ajouté à votre historique !');\n    }\n  };\n\n  // Afficher les statistiques de progression\n  const handleShowProgress = exerciseId => {\n    const stats = calculateProgressStats(exerciseId);\n    setProgressData(stats);\n    setShowProgressModal(true);\n  };\n\n  // Ajouter/retirer un exercice de la sélection pour programme\n  const handleToggleExerciseSelection = exercise => {\n    setSelectedExercisesForProgram(prev => {\n      const isSelected = prev.some(ex => ex.id === exercise.id);\n      if (isSelected) {\n        return prev.filter(ex => ex.id !== exercise.id);\n      } else {\n        return [...prev, exercise];\n      }\n    });\n  };\n\n  // Créer un programme personnalisé\n  const handleCreateProgram = programName => {\n    if (selectedExercisesForProgram.length === 0) {\n      alert('Veuillez sélectionner au moins un exercice');\n      return;\n    }\n    const program = createCustomProgram(programName, selectedExercisesForProgram);\n    setSelectedExercisesForProgram([]);\n    setShowCreateProgram(false);\n    alert(`Programme \"${program.name}\" créé avec succès !`);\n  };\n  const rateExercise = (exerciseId, rating, comment = '') => {\n    const updatedRatings = {\n      ...exerciseRatings,\n      [exerciseId]: rating\n    };\n    const updatedComments = {\n      ...exerciseComments,\n      [exerciseId]: comment\n    };\n    setExerciseRatings(updatedRatings);\n    setExerciseComments(updatedComments);\n    localStorage.setItem('exerciseRatings', JSON.stringify(updatedRatings));\n    localStorage.setItem('exerciseComments', JSON.stringify(updatedComments));\n    setShowRatingModal(false);\n    setRatingExercise(null);\n  };\n  const openRatingModal = exercise => {\n    setRatingExercise(exercise);\n    setShowRatingModal(true);\n  };\n  const getAverageRating = exerciseId => {\n    return exerciseRatings[exerciseId] || 0;\n  };\n  const renderStars = (rating, interactive = false, onRate = null) => {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center space-x-1\",\n      children: [1, 2, 3, 4, 5].map(star => /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => interactive && onRate && onRate(star),\n        className: `${interactive ? 'cursor-pointer hover:scale-110' : 'cursor-default'} transition-transform`,\n        disabled: !interactive,\n        children: /*#__PURE__*/_jsxDEV(FiStar, {\n          className: `h-4 w-4 ${star <= rating ? 'text-yellow-400 fill-current' : 'text-gray-300'}`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 13\n        }, this)\n      }, star, false, {\n        fileName: _jsxFileName,\n        lineNumber: 236,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 234,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold text-gray-900 mb-4\",\n          children: \"Biblioth\\xE8que d'Exercices\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg text-gray-600 max-w-3xl mx-auto\",\n          children: \"D\\xE9couvrez notre collection compl\\xE8te d'exercices avec instructions d\\xE9taill\\xE9es, recommandations personnalis\\xE9es et programmes adapt\\xE9s \\xE0 vos objectifs.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 259,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-lg p-6 mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col lg:flex-row gap-4 items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 relative\",\n            children: [/*#__PURE__*/_jsxDEV(FiSearch, {\n              className: \"absolute left-3 top-3 h-5 w-5 text-gray-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"Rechercher un exercice, groupe musculaire...\",\n              value: searchQuery,\n              onChange: e => setSearchQuery(e.target.value),\n              className: \"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-wrap gap-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowFilters(!showFilters),\n              className: `px-4 py-3 rounded-lg font-medium transition-colors flex items-center ${showFilters ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`,\n              children: [/*#__PURE__*/_jsxDEV(FiFilter, {\n                className: \"h-4 w-4 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 289,\n                columnNumber: 17\n              }, this), \"Filtres\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: generateQuickWorkout,\n              className: \"px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(FiTarget, {\n                className: \"h-4 w-4 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 17\n              }, this), \"Programme rapide\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowCreateProgram(true),\n              className: \"px-4 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(FiPlus, {\n                className: \"h-4 w-4 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 303,\n                columnNumber: 17\n              }, this), \"Cr\\xE9er programme\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setActiveTab('history'),\n              className: \"px-4 py-3 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(FiCalendar, {\n                className: \"h-4 w-4 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 310,\n                columnNumber: 17\n              }, this), \"Historique\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 282,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 11\n        }, this), showFilters && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-6 pt-6 border-t border-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Cat\\xE9gorie\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 321,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: filters.category,\n                onChange: e => handleFilterChange('category', e.target.value),\n                className: \"w-full p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"all\",\n                  children: \"Toutes les cat\\xE9gories\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 327,\n                  columnNumber: 21\n                }, this), Object.entries(exerciseCategories).map(([key, category]) => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: key,\n                  children: category.name\n                }, key, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 329,\n                  columnNumber: 23\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 322,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 320,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Groupe musculaire\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 335,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: filters.muscleGroup,\n                onChange: e => handleFilterChange('muscleGroup', e.target.value),\n                className: \"w-full p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"all\",\n                  children: \"Tous les groupes\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 341,\n                  columnNumber: 21\n                }, this), Object.entries(muscleGroups).map(([key, group]) => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: key,\n                  children: group.name\n                }, key, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 343,\n                  columnNumber: 23\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 336,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 334,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"\\xC9quipement\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 349,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: filters.equipment,\n                onChange: e => handleFilterChange('equipment', e.target.value),\n                className: \"w-full p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"all\",\n                  children: \"Tout \\xE9quipement\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 355,\n                  columnNumber: 21\n                }, this), Object.entries(equipmentTypes).map(([key, equipment]) => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: key,\n                  children: equipment.name\n                }, key, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 357,\n                  columnNumber: 23\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 350,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 348,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Difficult\\xE9\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 363,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: filters.difficulty,\n                onChange: e => handleFilterChange('difficulty', e.target.value),\n                className: \"w-full p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"all\",\n                  children: \"Toutes difficult\\xE9s\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 369,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Facile\",\n                  children: \"Facile\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 370,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Mod\\xE9r\\xE9\",\n                  children: \"Mod\\xE9r\\xE9\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 371,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Difficile\",\n                  children: \"Difficile\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 372,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 364,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 362,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 319,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-4 flex justify-between items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700\",\n                children: \"Dur\\xE9e max (min):\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 379,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                value: filters.maxDuration || '',\n                onChange: e => handleFilterChange('maxDuration', e.target.value ? parseInt(e.target.value) : null),\n                placeholder: \"60\",\n                className: \"w-20 p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 380,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 378,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: clearFilters,\n              className: \"px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors\",\n              children: \"Effacer les filtres\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 388,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 377,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 318,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 270,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex space-x-1 bg-gray-100 p-1 rounded-lg mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setActiveTab('all'),\n          className: `flex-1 py-3 px-4 rounded-md text-sm font-medium transition-colors ${activeTab === 'all' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'}`,\n          children: [\"Tous (\", exercises.length, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 401,\n          columnNumber: 11\n        }, this), user && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setActiveTab('recommended'),\n            className: `flex-1 py-3 px-4 rounded-md text-sm font-medium transition-colors ${activeTab === 'recommended' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'}`,\n            children: [/*#__PURE__*/_jsxDEV(FiStar, {\n              className: \"inline mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 421,\n              columnNumber: 17\n            }, this), \"Recommand\\xE9s (\", recommendedExercises.length, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 413,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setActiveTab('favorites'),\n            className: `flex-1 py-3 px-4 rounded-md text-sm font-medium transition-colors ${activeTab === 'favorites' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'}`,\n            children: [/*#__PURE__*/_jsxDEV(FiHeart, {\n              className: \"inline mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 432,\n              columnNumber: 17\n            }, this), \"Favoris (\", favoriteExercises.length, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 424,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setActiveTab('history'),\n            className: `flex-1 py-3 px-4 rounded-md text-sm font-medium transition-colors ${activeTab === 'history' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'}`,\n            children: [/*#__PURE__*/_jsxDEV(FiCalendar, {\n              className: \"inline mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 443,\n              columnNumber: 17\n            }, this), \"Historique (\", exerciseHistory.length, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 435,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 400,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-6\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: [filteredExercises.length, \" exercice\", filteredExercises.length > 1 ? 's' : '', \" trouv\\xE9\", filteredExercises.length > 1 ? 's' : '', searchQuery && ` pour \"${searchQuery}\"`]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 452,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 451,\n        columnNumber: 9\n      }, this), activeTab === 'history' ?\n      /*#__PURE__*/\n      /* Exercise History */\n      _jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-lg p-6 mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-2xl font-bold text-gray-900 mb-6\",\n          children: \"Historique des exercices\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 462,\n          columnNumber: 13\n        }, this), exerciseHistory.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center py-12\",\n          children: [/*#__PURE__*/_jsxDEV(FiCalendar, {\n            className: \"h-16 w-16 mx-auto text-gray-400 mb-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 465,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-medium text-gray-900 mb-2\",\n            children: \"Aucun exercice dans l'historique\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 466,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600\",\n            children: \"Commencez un exercice pour voir votre progression ici.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 467,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 464,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: exerciseHistory.slice(0, 10).map(entry => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"border border-gray-200 rounded-lg p-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"font-semibold text-gray-900\",\n                children: entry.exerciseName\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 474,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-500\",\n                children: new Date(entry.date).toLocaleDateString('fr-FR')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 475,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 473,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-600\",\n                  children: \"S\\xE9ries:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 481,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ml-2 font-medium\",\n                  children: entry.performance.sets\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 482,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 480,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-600\",\n                  children: \"R\\xE9p\\xE9titions:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 485,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ml-2 font-medium\",\n                  children: entry.performance.reps\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 486,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 484,\n                columnNumber: 23\n              }, this), entry.performance.weight && /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-600\",\n                  children: \"Poids:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 490,\n                  columnNumber: 27\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ml-2 font-medium\",\n                  children: [entry.performance.weight, \" kg\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 491,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 489,\n                columnNumber: 25\n              }, this), entry.performance.duration && /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-600\",\n                  children: \"Dur\\xE9e:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 496,\n                  columnNumber: 27\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ml-2 font-medium\",\n                  children: [entry.performance.duration, \" min\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 497,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 495,\n                columnNumber: 25\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 479,\n              columnNumber: 21\n            }, this), entry.performance.notes && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-2 text-sm text-gray-600\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium\",\n                children: \"Notes:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 503,\n                columnNumber: 25\n              }, this), \" \", entry.performance.notes]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 502,\n              columnNumber: 23\n            }, this)]\n          }, entry.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 472,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 470,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 461,\n        columnNumber: 11\n      }, this) :\n      /*#__PURE__*/\n      /* Exercise Grid */\n      _jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8\",\n        children: (activeTab === 'all' ? filteredExercises : activeTab === 'recommended' ? recommendedExercises : favoriteExercises).map(exercise => {\n          var _exerciseCategories$e, _exerciseCategories$e2, _equipmentTypes$exerc, _equipmentTypes$exerc2;\n          const isFavorite = favoriteExercises.some(fav => fav.id === exercise.id);\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start justify-between mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-xl font-semibold text-gray-900 mb-2\",\n                    children: exercise.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 527,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-2 mb-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `px-3 py-1 rounded-full text-sm font-medium ${getDifficultyColor(exercise.difficulty)}`,\n                      children: exercise.difficulty\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 531,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-xs text-gray-500\",\n                      children: [(_exerciseCategories$e = exerciseCategories[exercise.category]) === null || _exerciseCategories$e === void 0 ? void 0 : _exerciseCategories$e.icon, \" \", (_exerciseCategories$e2 = exerciseCategories[exercise.category]) === null || _exerciseCategories$e2 === void 0 ? void 0 : _exerciseCategories$e2.name]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 534,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 530,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 526,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: e => {\n                      e.stopPropagation();\n                      handleShowProgress(exercise.id);\n                    },\n                    className: \"p-2 rounded-full text-gray-400 hover:text-blue-500 transition-colors\",\n                    title: \"Voir les statistiques\",\n                    children: /*#__PURE__*/_jsxDEV(FiBarChart2, {\n                      className: \"h-5 w-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 548,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 540,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: e => {\n                      e.stopPropagation();\n                      handleToggleFavorite(exercise.id);\n                    },\n                    className: `p-2 rounded-full transition-colors ${isFavorite ? 'text-red-500 hover:text-red-600' : 'text-gray-400 hover:text-red-500'}`,\n                    title: isFavorite ? 'Retirer des favoris' : 'Ajouter aux favoris',\n                    children: /*#__PURE__*/_jsxDEV(FiHeart, {\n                      className: `h-5 w-5 ${isFavorite ? 'fill-current' : ''}`\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 562,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 550,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 539,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 525,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 mb-4 line-clamp-2\",\n                children: exercise.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 567,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2\",\n                  children: [renderStars(getAverageRating(exercise.id)), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm text-gray-500\",\n                    children: [\"(\", getAverageRating(exercise.id) > 0 ? getAverageRating(exercise.id).toFixed(1) : 'Non noté', \")\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 575,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 573,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: e => {\n                    e.stopPropagation();\n                    openRatingModal(exercise);\n                  },\n                  className: \"text-xs text-blue-600 hover:text-blue-800 transition-colors\",\n                  children: \"Noter\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 579,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 572,\n                columnNumber: 19\n              }, this), exerciseComments[exercise.id] && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-3 p-2 bg-gray-50 rounded text-sm text-gray-700\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium\",\n                  children: \"Votre note: \"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 593,\n                  columnNumber: 23\n                }, this), exerciseComments[exercise.id].length > 50 ? `${exerciseComments[exercise.id].substring(0, 50)}...` : exerciseComments[exercise.id]]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 592,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-wrap gap-1 mb-4\",\n                children: exercise.muscleGroups.map(mg => {\n                  var _muscleGroups$mg;\n                  return /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full\",\n                    children: (_muscleGroups$mg = muscleGroups[mg]) === null || _muscleGroups$mg === void 0 ? void 0 : _muscleGroups$mg.name\n                  }, mg, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 604,\n                    columnNumber: 23\n                  }, this);\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 602,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center mb-4 text-sm text-gray-600\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"mr-2\",\n                  children: (_equipmentTypes$exerc = equipmentTypes[exercise.equipment]) === null || _equipmentTypes$exerc === void 0 ? void 0 : _equipmentTypes$exerc.icon\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 612,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: (_equipmentTypes$exerc2 = equipmentTypes[exercise.equipment]) === null || _equipmentTypes$exerc2 === void 0 ? void 0 : _equipmentTypes$exerc2.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 613,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 611,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between text-sm text-gray-500 mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(FiClock, {\n                    className: \"h-4 w-4 mr-1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 618,\n                    columnNumber: 23\n                  }, this), exercise.duration, \" min\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 617,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(FiZap, {\n                    className: \"h-4 w-4 mr-1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 622,\n                    columnNumber: 23\n                  }, this), exercise.calories, \" cal\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 621,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 616,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex gap-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => setSelectedExercise(exercise),\n                    className: \"flex-1 bg-gray-100 text-gray-700 py-2 px-3 rounded-lg hover:bg-gray-200 transition-colors flex items-center justify-center text-sm\",\n                    children: [/*#__PURE__*/_jsxDEV(FiInfo, {\n                      className: \"h-4 w-4 mr-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 633,\n                      columnNumber: 25\n                    }, this), \"D\\xE9tails\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 629,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => {\n                      setSelectedExercise(exercise);\n                      setShowPerformanceModal(true);\n                    },\n                    className: \"flex-1 bg-blue-600 text-white py-2 px-3 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center text-sm\",\n                    children: [/*#__PURE__*/_jsxDEV(FiPlay, {\n                      className: \"h-4 w-4 mr-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 643,\n                      columnNumber: 25\n                    }, this), \"Commencer\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 636,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 628,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex gap-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: e => {\n                      e.stopPropagation();\n                      handleToggleExerciseSelection(exercise);\n                    },\n                    className: `flex-1 py-2 px-3 rounded-lg transition-colors flex items-center justify-center text-sm ${selectedExercisesForProgram.some(ex => ex.id === exercise.id) ? 'bg-purple-100 text-purple-700 border border-purple-300' : 'bg-gray-50 text-gray-600 hover:bg-gray-100'}`,\n                    children: [/*#__PURE__*/_jsxDEV(FiBookmark, {\n                      className: \"h-4 w-4 mr-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 659,\n                      columnNumber: 25\n                    }, this), selectedExercisesForProgram.some(ex => ex.id === exercise.id) ? 'Sélectionné' : 'Sélectionner']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 648,\n                    columnNumber: 23\n                  }, this), getExerciseVideo(exercise.id) && /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => window.open(getExerciseVideo(exercise.id), '_blank'),\n                    className: \"flex-1 bg-green-100 text-green-700 py-2 px-3 rounded-lg hover:bg-green-200 transition-colors flex items-center justify-center text-sm\",\n                    children: [/*#__PURE__*/_jsxDEV(FiVideo, {\n                      className: \"h-4 w-4 mr-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 667,\n                      columnNumber: 27\n                    }, this), \"Vid\\xE9o\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 663,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 647,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 627,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 524,\n              columnNumber: 17\n            }, this)\n          }, exercise.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 520,\n            columnNumber: 15\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 513,\n        columnNumber: 11\n      }, this), filteredExercises.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-gray-400 mb-4\",\n          children: /*#__PURE__*/_jsxDEV(FiSearch, {\n            className: \"h-16 w-16 mx-auto\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 684,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 683,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium text-gray-900 mb-2\",\n          children: \"Aucun exercice trouv\\xE9\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 686,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mb-4\",\n          children: \"Essayez de modifier vos crit\\xE8res de recherche ou vos filtres.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 689,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: clearFilters,\n          className: \"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors\",\n          children: \"Effacer les filtres\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 692,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 682,\n        columnNumber: 11\n      }, this), showPerformanceModal && selectedExercise && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg max-w-md w-full\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-xl font-bold text-gray-900\",\n                children: \"Enregistrer la performance\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 707,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setShowPerformanceModal(false),\n                className: \"text-gray-400 hover:text-gray-600\",\n                children: /*#__PURE__*/_jsxDEV(FiX, {\n                  className: \"h-6 w-6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 714,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 710,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 706,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Nombre de s\\xE9ries\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 720,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  value: performanceData.sets,\n                  onChange: e => setPerformanceData(prev => ({\n                    ...prev,\n                    sets: parseInt(e.target.value) || 0\n                  })),\n                  className: \"w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 723,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 719,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"R\\xE9p\\xE9titions par s\\xE9rie\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 732,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  value: performanceData.reps,\n                  onChange: e => setPerformanceData(prev => ({\n                    ...prev,\n                    reps: parseInt(e.target.value) || 0\n                  })),\n                  className: \"w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 735,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 731,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Poids (kg) - optionnel\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 744,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  value: performanceData.weight,\n                  onChange: e => setPerformanceData(prev => ({\n                    ...prev,\n                    weight: e.target.value\n                  })),\n                  className: \"w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                  placeholder: \"Ex: 20\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 747,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 743,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Dur\\xE9e (min) - optionnel\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 757,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  value: performanceData.duration,\n                  onChange: e => setPerformanceData(prev => ({\n                    ...prev,\n                    duration: e.target.value\n                  })),\n                  className: \"w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                  placeholder: \"Ex: 15\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 760,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 756,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Notes - optionnel\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 770,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  value: performanceData.notes,\n                  onChange: e => setPerformanceData(prev => ({\n                    ...prev,\n                    notes: e.target.value\n                  })),\n                  className: \"w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                  rows: \"3\",\n                  placeholder: \"Comment vous \\xEAtes-vous senti ?\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 773,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 769,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 718,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex gap-3 mt-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setShowPerformanceModal(false),\n                className: \"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\",\n                children: \"Annuler\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 784,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleAddToHistory(selectedExercise),\n                className: \"flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                children: \"Enregistrer\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 790,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 783,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 705,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 704,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 703,\n        columnNumber: 11\n      }, this), showProgressModal && progressData && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg max-w-lg w-full\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-xl font-bold text-gray-900\",\n                children: \"Statistiques de progression\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 808,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setShowProgressModal(false),\n                className: \"text-gray-400 hover:text-gray-600\",\n                children: /*#__PURE__*/_jsxDEV(FiX, {\n                  className: \"h-6 w-6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 815,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 811,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 807,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-2 gap-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-blue-50 p-4 rounded-lg\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-2xl font-bold text-blue-600\",\n                    children: progressData.totalSessions\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 822,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm text-gray-600\",\n                    children: \"Sessions totales\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 823,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 821,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-green-50 p-4 rounded-lg\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-2xl font-bold text-green-600\",\n                    children: progressData.lastPerformed ? new Date(progressData.lastPerformed).toLocaleDateString('fr-FR') : 'Jamais'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 826,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm text-gray-600\",\n                    children: \"Derni\\xE8re fois\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 832,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 825,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 820,\n                columnNumber: 19\n              }, this), progressData.bestPerformance && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-yellow-50 p-4 rounded-lg\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"font-semibold text-gray-900 mb-2\",\n                  children: \"Meilleure performance\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 838,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-gray-600\",\n                  children: [progressData.bestPerformance.performance.sets, \" s\\xE9ries \\xD7 \", progressData.bestPerformance.performance.reps, \" reps\", progressData.bestPerformance.performance.weight && ` à ${progressData.bestPerformance.performance.weight} kg`]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 839,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 837,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-gray-50 p-4 rounded-lg\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"font-semibold text-gray-900 mb-2\",\n                  children: \"Tendance\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 849,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `flex items-center ${progressData.progressTrend === 'improving' ? 'text-green-600' : progressData.progressTrend === 'declining' ? 'text-red-600' : 'text-gray-600'}`,\n                  children: [/*#__PURE__*/_jsxDEV(FiTrendingUp, {\n                    className: \"h-4 w-4 mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 855,\n                    columnNumber: 23\n                  }, this), progressData.progressTrend === 'improving' ? 'En progression' : progressData.progressTrend === 'declining' ? 'En baisse' : 'Stable']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 850,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 848,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 819,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 806,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 805,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 804,\n        columnNumber: 11\n      }, this), showCreateProgram && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg max-w-md w-full\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-xl font-bold text-gray-900\",\n                children: \"Cr\\xE9er un programme\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 873,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setShowCreateProgram(false),\n                className: \"text-gray-400 hover:text-gray-600\",\n                children: /*#__PURE__*/_jsxDEV(FiX, {\n                  className: \"h-6 w-6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 880,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 876,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 872,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 mb-4\",\n                children: [selectedExercisesForProgram.length, \" exercice(s) s\\xE9lectionn\\xE9(s)\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 885,\n                columnNumber: 19\n              }, this), selectedExercisesForProgram.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-2 mb-4 max-h-40 overflow-y-auto\",\n                children: selectedExercisesForProgram.map(exercise => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between bg-gray-50 p-2 rounded\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm\",\n                    children: exercise.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 893,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleToggleExerciseSelection(exercise),\n                    className: \"text-red-500 hover:text-red-700\",\n                    children: /*#__PURE__*/_jsxDEV(FiX, {\n                      className: \"h-4 w-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 898,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 894,\n                    columnNumber: 27\n                  }, this)]\n                }, exercise.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 892,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 890,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"Nom du programme\",\n                className: \"w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                onKeyPress: e => {\n                  if (e.key === 'Enter') {\n                    handleCreateProgram(e.target.value);\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 905,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 884,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex gap-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setShowCreateProgram(false),\n                className: \"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\",\n                children: \"Annuler\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 918,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: e => {\n                  const input = e.target.parentElement.parentElement.querySelector('input');\n                  handleCreateProgram(input.value);\n                },\n                className: \"flex-1 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors\",\n                children: \"Cr\\xE9er\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 924,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 917,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 871,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 870,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 869,\n        columnNumber: 11\n      }, this), selectedExercise && !showPerformanceModal && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"text-3xl font-bold text-gray-900 mb-2\",\n                  children: selectedExercise.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 946,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `px-3 py-1 rounded-full text-sm font-medium ${getDifficultyColor(selectedExercise.difficulty)}`,\n                    children: selectedExercise.difficulty\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 950,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-600\",\n                    children: [(_exerciseCategories$s = exerciseCategories[selectedExercise.category]) === null || _exerciseCategories$s === void 0 ? void 0 : _exerciseCategories$s.icon, \" \", (_exerciseCategories$s2 = exerciseCategories[selectedExercise.category]) === null || _exerciseCategories$s2 === void 0 ? void 0 : _exerciseCategories$s2.name]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 953,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 949,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 945,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setSelectedExercise(null),\n                className: \"text-gray-400 hover:text-gray-600 text-2xl\",\n                children: \"\\u2715\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 958,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 944,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"lg:col-span-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-xl font-semibold text-gray-900 mb-3\",\n                    children: \"Description\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 970,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-gray-600 leading-relaxed\",\n                    children: selectedExercise.description\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 971,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 969,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-xl font-semibold text-gray-900 mb-4\",\n                    children: \"Instructions d\\xE9taill\\xE9es\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 975,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"ol\", {\n                    className: \"list-decimal list-inside space-y-3\",\n                    children: selectedExercise.instructions.map((instruction, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n                      className: \"text-gray-600 leading-relaxed pl-2\",\n                      children: instruction\n                    }, index, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 978,\n                      columnNumber: 27\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 976,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 974,\n                  columnNumber: 21\n                }, this), selectedExercise.tips && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-xl font-semibold text-gray-900 mb-4\",\n                    children: \"Conseils\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 987,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                    className: \"space-y-2\",\n                    children: selectedExercise.tips.map((tip, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n                      className: \"flex items-start text-gray-600\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-blue-500 mr-2 mt-1\",\n                        children: \"\\u2022\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 991,\n                        columnNumber: 31\n                      }, this), tip]\n                    }, index, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 990,\n                      columnNumber: 29\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 988,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 986,\n                  columnNumber: 23\n                }, this), selectedExercise.variations && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-xl font-semibold text-gray-900 mb-4\",\n                    children: \"Variations\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1001,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                    className: \"space-y-2\",\n                    children: selectedExercise.variations.map((variation, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n                      className: \"flex items-start text-gray-600\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-green-500 mr-2 mt-1\",\n                        children: \"\\u2022\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1005,\n                        columnNumber: 31\n                      }, this), variation]\n                    }, index, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1004,\n                      columnNumber: 29\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1002,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1000,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 968,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"lg:col-span-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-gray-50 rounded-lg p-6 mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-lg font-semibold text-gray-900 mb-4\",\n                    children: \"Informations\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1017,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"space-y-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex justify-between\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-gray-600\",\n                        children: \"Dur\\xE9e\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1021,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"font-medium\",\n                        children: [selectedExercise.duration, \" min\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1022,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1020,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex justify-between\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-gray-600\",\n                        children: \"Calories\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1025,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"font-medium\",\n                        children: [selectedExercise.calories, \" cal\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1026,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1024,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex justify-between\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-gray-600\",\n                        children: \"\\xC9quipement\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1029,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"font-medium\",\n                        children: (_equipmentTypes$selec = equipmentTypes[selectedExercise.equipment]) === null || _equipmentTypes$selec === void 0 ? void 0 : _equipmentTypes$selec.name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1030,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1028,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1019,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1016,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-gray-50 rounded-lg p-6 mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-lg font-semibold text-gray-900 mb-4\",\n                    children: \"Groupes musculaires\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1036,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex flex-wrap gap-2\",\n                    children: selectedExercise.muscleGroups.map(mg => {\n                      var _muscleGroups$mg2, _muscleGroups$mg3;\n                      return /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"px-3 py-1 bg-blue-100 text-blue-700 text-sm rounded-full\",\n                        children: [(_muscleGroups$mg2 = muscleGroups[mg]) === null || _muscleGroups$mg2 === void 0 ? void 0 : _muscleGroups$mg2.icon, \" \", (_muscleGroups$mg3 = muscleGroups[mg]) === null || _muscleGroups$mg3 === void 0 ? void 0 : _muscleGroups$mg3.name]\n                      }, mg, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1039,\n                        columnNumber: 27\n                      }, this);\n                    })\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1037,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1035,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => toggleFavorite(selectedExercise.id),\n                    className: `w-full py-3 px-6 rounded-lg font-medium transition-colors flex items-center justify-center ${favoriteExercises.some(fav => fav.id === selectedExercise.id) ? 'bg-red-100 text-red-700 hover:bg-red-200' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`,\n                    children: [/*#__PURE__*/_jsxDEV(FiHeart, {\n                      className: \"h-5 w-5 mr-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1055,\n                      columnNumber: 25\n                    }, this), favoriteExercises.some(fav => fav.id === selectedExercise.id) ? 'Retirer des favoris' : 'Ajouter aux favoris']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1047,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"w-full bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center font-medium\",\n                    children: [/*#__PURE__*/_jsxDEV(FiPlay, {\n                      className: \"h-5 w-5 mr-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1060,\n                      columnNumber: 25\n                    }, this), \"Commencer l'exercice\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1059,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1046,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1015,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 966,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 943,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 942,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 941,\n        columnNumber: 11\n      }, this), showRatingModal && ratingExercise && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg p-6 max-w-md w-full mx-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-gray-900 mb-4\",\n            children: [\"Noter l'exercice : \", ratingExercise.name]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1075,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Votre note\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1081,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-center space-x-2\",\n                children: renderStars(0, true, rating => {\n                  const comment = exerciseComments[ratingExercise.id] || '';\n                  rateExercise(ratingExercise.id, rating, comment);\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1084,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1080,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Commentaire (optionnel)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1093,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                defaultValue: exerciseComments[ratingExercise.id] || '',\n                onChange: e => {\n                  const updatedComments = {\n                    ...exerciseComments,\n                    [ratingExercise.id]: e.target.value\n                  };\n                  setExerciseComments(updatedComments);\n                },\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                rows: \"3\",\n                placeholder: \"Partagez votre exp\\xE9rience avec cet exercice...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1096,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1092,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1079,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex space-x-3 mt-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                const rating = exerciseRatings[ratingExercise.id] || 0;\n                const comment = exerciseComments[ratingExercise.id] || '';\n                if (rating > 0) {\n                  rateExercise(ratingExercise.id, rating, comment);\n                }\n              },\n              className: \"flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors\",\n              children: \"Sauvegarder\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1110,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                setShowRatingModal(false);\n                setRatingExercise(null);\n              },\n              className: \"flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-400 transition-colors\",\n              children: \"Annuler\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1122,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1109,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1074,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1073,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 257,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 256,\n    columnNumber: 5\n  }, this);\n};\n_s(Exercises, \"KVLMrNt+y/4o1tiNhxODrQlETQc=\", false, function () {\n  return [useAuth];\n});\n_c = Exercises;\nexport default Exercises;\nvar _c;\n$RefreshReg$(_c, \"Exercises\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "FiPlay", "<PERSON><PERSON><PERSON>", "FiZap", "<PERSON><PERSON><PERSON><PERSON>", "FiSearch", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "FiStar", "FiInfo", "FiPlus", "FiTrendingUp", "FiBookmark", "FiVideo", "FiBarChart2", "FiCalendar", "FiX", "useAuth", "exercises", "exerciseCategories", "muscleGroups", "equipmentTypes", "getDifficultyColor", "getRecommendedExercises", "searchExercises", "createWorkoutPlan", "toggleFavorite", "loadFavorites", "addToHistory", "getExerciseHistory", "calculateProgressStats", "createCustomProgram", "getExerciseVideo", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Exercises", "_s", "_exerciseCategories$s", "_exerciseCategories$s2", "_equipmentTypes$selec", "user", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "setSelectedExercise", "searchQuery", "setSearch<PERSON>uery", "showFilters", "setShowFilters", "filters", "setFilters", "category", "muscleGroup", "equipment", "difficulty", "maxDuration", "activeTab", "setActiveTab", "recommendedExercises", "setRecommendedExercises", "favoriteExercises", "setFavoriteExercises", "filteredExercises", "setFilteredExercises", "exerciseHistory", "setExerciseHistory", "showCreateProgram", "setShowCreateProgram", "selectedExercisesForProgram", "setSelectedExercisesForProgram", "showProgressModal", "setShowProgressModal", "progressData", "setProgressData", "showPerformanceModal", "setShowPerformanceModal", "performanceData", "setPerformanceData", "sets", "reps", "weight", "duration", "notes", "exerciseRatings", "setExerciseRatings", "exerciseComments", "setExerciseComments", "showRatingModal", "setShowRatingModal", "ratingExercise", "setRatingExercise", "recommendations", "primaryGoal", "savedFavorites", "favoriteExerciseObjects", "filter", "ex", "includes", "id", "history", "savedRatings", "JSON", "parse", "localStorage", "getItem", "savedComments", "error", "console", "results", "Object", "values", "some", "f", "handleFilterChange", "filterType", "value", "prev", "clearFilters", "generateQuickWorkout", "selectedExercises", "slice", "workout", "alert", "totalDuration", "estimatedCalories", "length", "handleToggleFavorite", "exerciseId", "isNowFavorite", "exercise", "find", "handleAddToHistory", "entry", "handleShowProgress", "stats", "handleToggleExerciseSelection", "isSelected", "handleCreateProgram", "programName", "program", "name", "rateExercise", "rating", "comment", "updatedRatings", "updatedComments", "setItem", "stringify", "openRatingModal", "getAverageRating", "renderStars", "interactive", "onRate", "className", "children", "map", "star", "onClick", "disabled", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "placeholder", "onChange", "e", "target", "entries", "key", "group", "parseInt", "exercise<PERSON>ame", "Date", "date", "toLocaleDateString", "performance", "_exerciseCategories$e", "_exerciseCategories$e2", "_equipmentTypes$exerc", "_equipmentTypes$exerc2", "isFavorite", "fav", "icon", "stopPropagation", "title", "description", "toFixed", "substring", "mg", "_muscleGroups$mg", "calories", "window", "open", "rows", "totalSessions", "lastPerformed", "bestPerformance", "progressTrend", "onKeyPress", "input", "parentElement", "querySelector", "instructions", "instruction", "index", "tips", "tip", "variations", "variation", "_muscleGroups$mg2", "_muscleGroups$mg3", "defaultValue", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/projet fitness/DATA/fitness-ui/src/pages/Exercises.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  <PERSON><PERSON>lay,\n  FiClock,\n  FiZap,\n  FiFilter,\n  FiSearch,\n  FiTarget,\n  FiHeart,\n  FiStar,\n  FiInfo,\n  FiPlus,\n  FiTrendingUp,\n  FiBookmark,\n  FiVideo,\n  FiBarChart2,\n  FiCalendar,\n  FiX,\n\n} from 'react-icons/fi';\nimport { useAuth } from '../contexts/AuthContext';\nimport {\n  exercises,\n  exerciseCategories,\n  muscleGroups,\n  equipmentTypes,\n  getDifficultyColor,\n  getRecommendedExercises,\n  searchExercises,\n  createWorkoutPlan,\n  toggleFavorite,\n  loadFavorites,\n  addToHistory,\n  getExerciseHistory,\n  calculateProgressStats,\n  createCustomProgram,\n  getExerciseVideo\n} from '../utils/exerciseUtils';\n\nconst Exercises = () => {\n  const { user } = useAuth();\n\n  const [selectedExercise, setSelectedExercise] = useState(null);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [showFilters, setShowFilters] = useState(false);\n  const [filters, setFilters] = useState({\n    category: 'all',\n    muscleGroup: 'all',\n    equipment: 'all',\n    difficulty: 'all',\n    maxDuration: null\n  });\n  const [activeTab, setActiveTab] = useState('all');\n  const [recommendedExercises, setRecommendedExercises] = useState([]);\n  const [favoriteExercises, setFavoriteExercises] = useState([]);\n  const [filteredExercises, setFilteredExercises] = useState(exercises);\n\n  // Nouveaux états pour les fonctionnalités avancées\n  const [exerciseHistory, setExerciseHistory] = useState([]);\n  const [showCreateProgram, setShowCreateProgram] = useState(false);\n  const [selectedExercisesForProgram, setSelectedExercisesForProgram] = useState([]);\n  const [showProgressModal, setShowProgressModal] = useState(false);\n  const [progressData, setProgressData] = useState(null);\n  const [showPerformanceModal, setShowPerformanceModal] = useState(false);\n  const [performanceData, setPerformanceData] = useState({\n    sets: 3,\n    reps: 12,\n    weight: '',\n    duration: '',\n    notes: ''\n  });\n  const [exerciseRatings, setExerciseRatings] = useState({});\n  const [exerciseComments, setExerciseComments] = useState({});\n  const [showRatingModal, setShowRatingModal] = useState(false);\n  const [ratingExercise, setRatingExercise] = useState(null);\n\n  useEffect(() => {\n    // Générer des recommandations basées sur le profil utilisateur\n    if (user) {\n      const recommendations = getRecommendedExercises(user, user.primaryGoal || 'general_fitness');\n      setRecommendedExercises(recommendations);\n    }\n\n    // Charger les favoris depuis localStorage\n    const savedFavorites = loadFavorites();\n    const favoriteExerciseObjects = exercises.filter(ex => savedFavorites.includes(ex.id));\n    setFavoriteExercises(favoriteExerciseObjects);\n\n    // Charger l'historique des exercices\n    const history = getExerciseHistory();\n    setExerciseHistory(history);\n\n    // Charger les notes et commentaires\n    try {\n      const savedRatings = JSON.parse(localStorage.getItem('exerciseRatings') || '{}');\n      const savedComments = JSON.parse(localStorage.getItem('exerciseComments') || '{}');\n      setExerciseRatings(savedRatings);\n      setExerciseComments(savedComments);\n    } catch (error) {\n      console.error('Erreur lors du chargement des notes:', error);\n    }\n  }, [user]);\n\n  useEffect(() => {\n    // Appliquer les filtres et la recherche\n    let results = exercises;\n    \n    if (activeTab === 'recommended') {\n      results = recommendedExercises;\n    } else if (activeTab === 'favorites') {\n      results = favoriteExercises;\n    }\n    \n    if (searchQuery || Object.values(filters).some(f => f !== 'all' && f !== null)) {\n      results = searchExercises(searchQuery, filters);\n    }\n    \n    setFilteredExercises(results);\n  }, [searchQuery, filters, activeTab, recommendedExercises, favoriteExercises]);\n\n  const handleFilterChange = (filterType, value) => {\n    setFilters(prev => ({\n      ...prev,\n      [filterType]: value\n    }));\n  };\n\n  const clearFilters = () => {\n    setFilters({\n      category: 'all',\n      muscleGroup: 'all',\n      equipment: 'all',\n      difficulty: 'all',\n      maxDuration: null\n    });\n    setSearchQuery('');\n  };\n\n  const generateQuickWorkout = () => {\n    const selectedExercises = filteredExercises.slice(0, 4);\n    const workout = createWorkoutPlan(selectedExercises, 30);\n    \n    // Ici on pourrait ouvrir une modal avec le programme généré\n    alert(`Programme généré !\\nDurée: ${workout.totalDuration} min\\nCalories estimées: ${workout.estimatedCalories}\\nExercices: ${workout.exercises.length}`);\n  };\n\n  const handleToggleFavorite = (exerciseId) => {\n    const savedFavorites = loadFavorites();\n    const isNowFavorite = toggleFavorite(exerciseId, savedFavorites, () => {});\n\n    // Mettre à jour l'état local\n    if (isNowFavorite) {\n      const exercise = exercises.find(ex => ex.id === exerciseId);\n      setFavoriteExercises(prev => [...prev, exercise]);\n    } else {\n      setFavoriteExercises(prev => prev.filter(ex => ex.id !== exerciseId));\n    }\n  };\n\n  // Ajouter un exercice à l'historique avec performance\n  const handleAddToHistory = (exercise) => {\n    const entry = addToHistory(exercise, performanceData);\n    if (entry) {\n      setExerciseHistory(prev => [entry, ...prev]);\n      setShowPerformanceModal(false);\n      setPerformanceData({\n        sets: 3,\n        reps: 12,\n        weight: '',\n        duration: '',\n        notes: ''\n      });\n      alert('Exercice ajouté à votre historique !');\n    }\n  };\n\n  // Afficher les statistiques de progression\n  const handleShowProgress = (exerciseId) => {\n    const stats = calculateProgressStats(exerciseId);\n    setProgressData(stats);\n    setShowProgressModal(true);\n  };\n\n  // Ajouter/retirer un exercice de la sélection pour programme\n  const handleToggleExerciseSelection = (exercise) => {\n    setSelectedExercisesForProgram(prev => {\n      const isSelected = prev.some(ex => ex.id === exercise.id);\n      if (isSelected) {\n        return prev.filter(ex => ex.id !== exercise.id);\n      } else {\n        return [...prev, exercise];\n      }\n    });\n  };\n\n  // Créer un programme personnalisé\n  const handleCreateProgram = (programName) => {\n    if (selectedExercisesForProgram.length === 0) {\n      alert('Veuillez sélectionner au moins un exercice');\n      return;\n    }\n\n    const program = createCustomProgram(programName, selectedExercisesForProgram);\n    setSelectedExercisesForProgram([]);\n    setShowCreateProgram(false);\n    alert(`Programme \"${program.name}\" créé avec succès !`);\n  };\n\n  const rateExercise = (exerciseId, rating, comment = '') => {\n    const updatedRatings = { ...exerciseRatings, [exerciseId]: rating };\n    const updatedComments = { ...exerciseComments, [exerciseId]: comment };\n\n    setExerciseRatings(updatedRatings);\n    setExerciseComments(updatedComments);\n\n    localStorage.setItem('exerciseRatings', JSON.stringify(updatedRatings));\n    localStorage.setItem('exerciseComments', JSON.stringify(updatedComments));\n\n    setShowRatingModal(false);\n    setRatingExercise(null);\n  };\n\n  const openRatingModal = (exercise) => {\n    setRatingExercise(exercise);\n    setShowRatingModal(true);\n  };\n\n  const getAverageRating = (exerciseId) => {\n    return exerciseRatings[exerciseId] || 0;\n  };\n\n  const renderStars = (rating, interactive = false, onRate = null) => {\n    return (\n      <div className=\"flex items-center space-x-1\">\n        {[1, 2, 3, 4, 5].map(star => (\n          <button\n            key={star}\n            onClick={() => interactive && onRate && onRate(star)}\n            className={`${interactive ? 'cursor-pointer hover:scale-110' : 'cursor-default'} transition-transform`}\n            disabled={!interactive}\n          >\n            <FiStar\n              className={`h-4 w-4 ${\n                star <= rating\n                  ? 'text-yellow-400 fill-current'\n                  : 'text-gray-300'\n              }`}\n            />\n          </button>\n        ))}\n      </div>\n    );\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Header */}\n        <div className=\"text-center mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-4\">\n            Bibliothèque d'Exercices\n          </h1>\n          <p className=\"text-lg text-gray-600 max-w-3xl mx-auto\">\n            Découvrez notre collection complète d'exercices avec instructions détaillées, \n            recommandations personnalisées et programmes adaptés à vos objectifs.\n          </p>\n        </div>\n\n        {/* Search and Quick Actions */}\n        <div className=\"bg-white rounded-lg shadow-lg p-6 mb-8\">\n          <div className=\"flex flex-col lg:flex-row gap-4 items-center\">\n            <div className=\"flex-1 relative\">\n              <FiSearch className=\"absolute left-3 top-3 h-5 w-5 text-gray-400\" />\n              <input\n                type=\"text\"\n                placeholder=\"Rechercher un exercice, groupe musculaire...\"\n                value={searchQuery}\n                onChange={(e) => setSearchQuery(e.target.value)}\n                className=\"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              />\n            </div>\n            <div className=\"flex flex-wrap gap-3\">\n              <button\n                onClick={() => setShowFilters(!showFilters)}\n                className={`px-4 py-3 rounded-lg font-medium transition-colors flex items-center ${\n                  showFilters ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'\n                }`}\n              >\n                <FiFilter className=\"h-4 w-4 mr-2\" />\n                Filtres\n              </button>\n              <button\n                onClick={generateQuickWorkout}\n                className=\"px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center\"\n              >\n                <FiTarget className=\"h-4 w-4 mr-2\" />\n                Programme rapide\n              </button>\n              <button\n                onClick={() => setShowCreateProgram(true)}\n                className=\"px-4 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors flex items-center\"\n              >\n                <FiPlus className=\"h-4 w-4 mr-2\" />\n                Créer programme\n              </button>\n              <button\n                onClick={() => setActiveTab('history')}\n                className=\"px-4 py-3 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors flex items-center\"\n              >\n                <FiCalendar className=\"h-4 w-4 mr-2\" />\n                Historique\n              </button>\n            </div>\n          </div>\n\n          {/* Advanced Filters */}\n          {showFilters && (\n            <div className=\"mt-6 pt-6 border-t border-gray-200\">\n              <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Catégorie</label>\n                  <select\n                    value={filters.category}\n                    onChange={(e) => handleFilterChange('category', e.target.value)}\n                    className=\"w-full p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  >\n                    <option value=\"all\">Toutes les catégories</option>\n                    {Object.entries(exerciseCategories).map(([key, category]) => (\n                      <option key={key} value={key}>{category.name}</option>\n                    ))}\n                  </select>\n                </div>\n                \n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Groupe musculaire</label>\n                  <select\n                    value={filters.muscleGroup}\n                    onChange={(e) => handleFilterChange('muscleGroup', e.target.value)}\n                    className=\"w-full p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  >\n                    <option value=\"all\">Tous les groupes</option>\n                    {Object.entries(muscleGroups).map(([key, group]) => (\n                      <option key={key} value={key}>{group.name}</option>\n                    ))}\n                  </select>\n                </div>\n                \n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Équipement</label>\n                  <select\n                    value={filters.equipment}\n                    onChange={(e) => handleFilterChange('equipment', e.target.value)}\n                    className=\"w-full p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  >\n                    <option value=\"all\">Tout équipement</option>\n                    {Object.entries(equipmentTypes).map(([key, equipment]) => (\n                      <option key={key} value={key}>{equipment.name}</option>\n                    ))}\n                  </select>\n                </div>\n                \n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Difficulté</label>\n                  <select\n                    value={filters.difficulty}\n                    onChange={(e) => handleFilterChange('difficulty', e.target.value)}\n                    className=\"w-full p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  >\n                    <option value=\"all\">Toutes difficultés</option>\n                    <option value=\"Facile\">Facile</option>\n                    <option value=\"Modéré\">Modéré</option>\n                    <option value=\"Difficile\">Difficile</option>\n                  </select>\n                </div>\n              </div>\n              \n              <div className=\"mt-4 flex justify-between items-center\">\n                <div className=\"flex items-center space-x-4\">\n                  <label className=\"block text-sm font-medium text-gray-700\">Durée max (min):</label>\n                  <input\n                    type=\"number\"\n                    value={filters.maxDuration || ''}\n                    onChange={(e) => handleFilterChange('maxDuration', e.target.value ? parseInt(e.target.value) : null)}\n                    placeholder=\"60\"\n                    className=\"w-20 p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  />\n                </div>\n                <button\n                  onClick={clearFilters}\n                  className=\"px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors\"\n                >\n                  Effacer les filtres\n                </button>\n              </div>\n            </div>\n          )}\n        </div>\n\n        {/* Tabs */}\n        <div className=\"flex space-x-1 bg-gray-100 p-1 rounded-lg mb-8\">\n          <button\n            onClick={() => setActiveTab('all')}\n            className={`flex-1 py-3 px-4 rounded-md text-sm font-medium transition-colors ${\n              activeTab === 'all'\n                ? 'bg-white text-blue-600 shadow-sm'\n                : 'text-gray-600 hover:text-gray-900'\n            }`}\n          >\n            Tous ({exercises.length})\n          </button>\n          {user && (\n            <>\n              <button\n                onClick={() => setActiveTab('recommended')}\n                className={`flex-1 py-3 px-4 rounded-md text-sm font-medium transition-colors ${\n                  activeTab === 'recommended'\n                    ? 'bg-white text-blue-600 shadow-sm'\n                    : 'text-gray-600 hover:text-gray-900'\n                }`}\n              >\n                <FiStar className=\"inline mr-2\" />\n                Recommandés ({recommendedExercises.length})\n              </button>\n              <button\n                onClick={() => setActiveTab('favorites')}\n                className={`flex-1 py-3 px-4 rounded-md text-sm font-medium transition-colors ${\n                  activeTab === 'favorites'\n                    ? 'bg-white text-blue-600 shadow-sm'\n                    : 'text-gray-600 hover:text-gray-900'\n                }`}\n              >\n                <FiHeart className=\"inline mr-2\" />\n                Favoris ({favoriteExercises.length})\n              </button>\n              <button\n                onClick={() => setActiveTab('history')}\n                className={`flex-1 py-3 px-4 rounded-md text-sm font-medium transition-colors ${\n                  activeTab === 'history'\n                    ? 'bg-white text-blue-600 shadow-sm'\n                    : 'text-gray-600 hover:text-gray-900'\n                }`}\n              >\n                <FiCalendar className=\"inline mr-2\" />\n                Historique ({exerciseHistory.length})\n              </button>\n            </>\n          )}\n        </div>\n\n        {/* Results Summary */}\n        <div className=\"mb-6\">\n          <p className=\"text-gray-600\">\n            {filteredExercises.length} exercice{filteredExercises.length > 1 ? 's' : ''} trouvé{filteredExercises.length > 1 ? 's' : ''}\n            {searchQuery && ` pour \"${searchQuery}\"`}\n          </p>\n        </div>\n\n        {/* Content based on active tab */}\n        {activeTab === 'history' ? (\n          /* Exercise History */\n          <div className=\"bg-white rounded-lg shadow-lg p-6 mb-8\">\n            <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">Historique des exercices</h2>\n            {exerciseHistory.length === 0 ? (\n              <div className=\"text-center py-12\">\n                <FiCalendar className=\"h-16 w-16 mx-auto text-gray-400 mb-4\" />\n                <h3 className=\"text-lg font-medium text-gray-900 mb-2\">Aucun exercice dans l'historique</h3>\n                <p className=\"text-gray-600\">Commencez un exercice pour voir votre progression ici.</p>\n              </div>\n            ) : (\n              <div className=\"space-y-4\">\n                {exerciseHistory.slice(0, 10).map(entry => (\n                  <div key={entry.id} className=\"border border-gray-200 rounded-lg p-4\">\n                    <div className=\"flex items-center justify-between mb-2\">\n                      <h3 className=\"font-semibold text-gray-900\">{entry.exerciseName}</h3>\n                      <span className=\"text-sm text-gray-500\">\n                        {new Date(entry.date).toLocaleDateString('fr-FR')}\n                      </span>\n                    </div>\n                    <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm\">\n                      <div>\n                        <span className=\"text-gray-600\">Séries:</span>\n                        <span className=\"ml-2 font-medium\">{entry.performance.sets}</span>\n                      </div>\n                      <div>\n                        <span className=\"text-gray-600\">Répétitions:</span>\n                        <span className=\"ml-2 font-medium\">{entry.performance.reps}</span>\n                      </div>\n                      {entry.performance.weight && (\n                        <div>\n                          <span className=\"text-gray-600\">Poids:</span>\n                          <span className=\"ml-2 font-medium\">{entry.performance.weight} kg</span>\n                        </div>\n                      )}\n                      {entry.performance.duration && (\n                        <div>\n                          <span className=\"text-gray-600\">Durée:</span>\n                          <span className=\"ml-2 font-medium\">{entry.performance.duration} min</span>\n                        </div>\n                      )}\n                    </div>\n                    {entry.performance.notes && (\n                      <div className=\"mt-2 text-sm text-gray-600\">\n                        <span className=\"font-medium\">Notes:</span> {entry.performance.notes}\n                      </div>\n                    )}\n                  </div>\n                ))}\n              </div>\n            )}\n          </div>\n        ) : (\n          /* Exercise Grid */\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8\">\n            {(activeTab === 'all' ? filteredExercises :\n              activeTab === 'recommended' ? recommendedExercises :\n              favoriteExercises).map(exercise => {\n            const isFavorite = favoriteExercises.some(fav => fav.id === exercise.id);\n\n            return (\n              <div\n                key={exercise.id}\n                className=\"bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow\"\n              >\n                <div className=\"p-6\">\n                  <div className=\"flex items-start justify-between mb-4\">\n                    <div className=\"flex-1\">\n                      <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">\n                        {exercise.name}\n                      </h3>\n                      <div className=\"flex items-center space-x-2 mb-3\">\n                        <span className={`px-3 py-1 rounded-full text-sm font-medium ${getDifficultyColor(exercise.difficulty)}`}>\n                          {exercise.difficulty}\n                        </span>\n                        <span className=\"text-xs text-gray-500\">\n                          {exerciseCategories[exercise.category]?.icon} {exerciseCategories[exercise.category]?.name}\n                        </span>\n                      </div>\n                    </div>\n                    <div className=\"flex space-x-2\">\n                      <button\n                        onClick={(e) => {\n                          e.stopPropagation();\n                          handleShowProgress(exercise.id);\n                        }}\n                        className=\"p-2 rounded-full text-gray-400 hover:text-blue-500 transition-colors\"\n                        title=\"Voir les statistiques\"\n                      >\n                        <FiBarChart2 className=\"h-5 w-5\" />\n                      </button>\n                      <button\n                        onClick={(e) => {\n                          e.stopPropagation();\n                          handleToggleFavorite(exercise.id);\n                        }}\n                        className={`p-2 rounded-full transition-colors ${\n                          isFavorite\n                            ? 'text-red-500 hover:text-red-600'\n                            : 'text-gray-400 hover:text-red-500'\n                        }`}\n                        title={isFavorite ? 'Retirer des favoris' : 'Ajouter aux favoris'}\n                      >\n                        <FiHeart className={`h-5 w-5 ${isFavorite ? 'fill-current' : ''}`} />\n                      </button>\n                    </div>\n                  </div>\n\n                  <p className=\"text-gray-600 mb-4 line-clamp-2\">\n                    {exercise.description}\n                  </p>\n\n                  {/* Rating and Comments */}\n                  <div className=\"flex items-center justify-between mb-3\">\n                    <div className=\"flex items-center space-x-2\">\n                      {renderStars(getAverageRating(exercise.id))}\n                      <span className=\"text-sm text-gray-500\">\n                        ({getAverageRating(exercise.id) > 0 ? getAverageRating(exercise.id).toFixed(1) : 'Non noté'})\n                      </span>\n                    </div>\n                    <button\n                      onClick={(e) => {\n                        e.stopPropagation();\n                        openRatingModal(exercise);\n                      }}\n                      className=\"text-xs text-blue-600 hover:text-blue-800 transition-colors\"\n                    >\n                      Noter\n                    </button>\n                  </div>\n\n                  {/* User Comment Preview */}\n                  {exerciseComments[exercise.id] && (\n                    <div className=\"mb-3 p-2 bg-gray-50 rounded text-sm text-gray-700\">\n                      <span className=\"font-medium\">Votre note: </span>\n                      {exerciseComments[exercise.id].length > 50\n                        ? `${exerciseComments[exercise.id].substring(0, 50)}...`\n                        : exerciseComments[exercise.id]\n                      }\n                    </div>\n                  )}\n\n                  {/* Muscle Groups */}\n                  <div className=\"flex flex-wrap gap-1 mb-4\">\n                    {exercise.muscleGroups.map(mg => (\n                      <span key={mg} className=\"px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full\">\n                        {muscleGroups[mg]?.name}\n                      </span>\n                    ))}\n                  </div>\n\n                  {/* Equipment */}\n                  <div className=\"flex items-center mb-4 text-sm text-gray-600\">\n                    <span className=\"mr-2\">{equipmentTypes[exercise.equipment]?.icon}</span>\n                    <span>{equipmentTypes[exercise.equipment]?.name}</span>\n                  </div>\n\n                  <div className=\"flex items-center justify-between text-sm text-gray-500 mb-4\">\n                    <div className=\"flex items-center\">\n                      <FiClock className=\"h-4 w-4 mr-1\" />\n                      {exercise.duration} min\n                    </div>\n                    <div className=\"flex items-center\">\n                      <FiZap className=\"h-4 w-4 mr-1\" />\n                      {exercise.calories} cal\n                    </div>\n                  </div>\n\n                  <div className=\"space-y-2\">\n                    <div className=\"flex gap-2\">\n                      <button\n                        onClick={() => setSelectedExercise(exercise)}\n                        className=\"flex-1 bg-gray-100 text-gray-700 py-2 px-3 rounded-lg hover:bg-gray-200 transition-colors flex items-center justify-center text-sm\"\n                      >\n                        <FiInfo className=\"h-4 w-4 mr-1\" />\n                        Détails\n                      </button>\n                      <button\n                        onClick={() => {\n                          setSelectedExercise(exercise);\n                          setShowPerformanceModal(true);\n                        }}\n                        className=\"flex-1 bg-blue-600 text-white py-2 px-3 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center text-sm\"\n                      >\n                        <FiPlay className=\"h-4 w-4 mr-1\" />\n                        Commencer\n                      </button>\n                    </div>\n                    <div className=\"flex gap-2\">\n                      <button\n                        onClick={(e) => {\n                          e.stopPropagation();\n                          handleToggleExerciseSelection(exercise);\n                        }}\n                        className={`flex-1 py-2 px-3 rounded-lg transition-colors flex items-center justify-center text-sm ${\n                          selectedExercisesForProgram.some(ex => ex.id === exercise.id)\n                            ? 'bg-purple-100 text-purple-700 border border-purple-300'\n                            : 'bg-gray-50 text-gray-600 hover:bg-gray-100'\n                        }`}\n                      >\n                        <FiBookmark className=\"h-4 w-4 mr-1\" />\n                        {selectedExercisesForProgram.some(ex => ex.id === exercise.id) ? 'Sélectionné' : 'Sélectionner'}\n                      </button>\n                      {getExerciseVideo(exercise.id) && (\n                        <button\n                          onClick={() => window.open(getExerciseVideo(exercise.id), '_blank')}\n                          className=\"flex-1 bg-green-100 text-green-700 py-2 px-3 rounded-lg hover:bg-green-200 transition-colors flex items-center justify-center text-sm\"\n                        >\n                          <FiVideo className=\"h-4 w-4 mr-1\" />\n                          Vidéo\n                        </button>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              </div>\n            );\n          })}\n          </div>\n        )}\n\n        {/* Empty State */}\n        {filteredExercises.length === 0 && (\n          <div className=\"text-center py-12\">\n            <div className=\"text-gray-400 mb-4\">\n              <FiSearch className=\"h-16 w-16 mx-auto\" />\n            </div>\n            <h3 className=\"text-lg font-medium text-gray-900 mb-2\">\n              Aucun exercice trouvé\n            </h3>\n            <p className=\"text-gray-600 mb-4\">\n              Essayez de modifier vos critères de recherche ou vos filtres.\n            </p>\n            <button\n              onClick={clearFilters}\n              className=\"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors\"\n            >\n              Effacer les filtres\n            </button>\n          </div>\n        )}\n\n        {/* Performance Modal */}\n        {showPerformanceModal && selectedExercise && (\n          <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\">\n            <div className=\"bg-white rounded-lg max-w-md w-full\">\n              <div className=\"p-6\">\n                <div className=\"flex items-center justify-between mb-6\">\n                  <h2 className=\"text-xl font-bold text-gray-900\">\n                    Enregistrer la performance\n                  </h2>\n                  <button\n                    onClick={() => setShowPerformanceModal(false)}\n                    className=\"text-gray-400 hover:text-gray-600\"\n                  >\n                    <FiX className=\"h-6 w-6\" />\n                  </button>\n                </div>\n\n                <div className=\"space-y-4\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Nombre de séries\n                    </label>\n                    <input\n                      type=\"number\"\n                      value={performanceData.sets}\n                      onChange={(e) => setPerformanceData(prev => ({...prev, sets: parseInt(e.target.value) || 0}))}\n                      className=\"w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    />\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Répétitions par série\n                    </label>\n                    <input\n                      type=\"number\"\n                      value={performanceData.reps}\n                      onChange={(e) => setPerformanceData(prev => ({...prev, reps: parseInt(e.target.value) || 0}))}\n                      className=\"w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    />\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Poids (kg) - optionnel\n                    </label>\n                    <input\n                      type=\"number\"\n                      value={performanceData.weight}\n                      onChange={(e) => setPerformanceData(prev => ({...prev, weight: e.target.value}))}\n                      className=\"w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                      placeholder=\"Ex: 20\"\n                    />\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Durée (min) - optionnel\n                    </label>\n                    <input\n                      type=\"number\"\n                      value={performanceData.duration}\n                      onChange={(e) => setPerformanceData(prev => ({...prev, duration: e.target.value}))}\n                      className=\"w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                      placeholder=\"Ex: 15\"\n                    />\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Notes - optionnel\n                    </label>\n                    <textarea\n                      value={performanceData.notes}\n                      onChange={(e) => setPerformanceData(prev => ({...prev, notes: e.target.value}))}\n                      className=\"w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                      rows=\"3\"\n                      placeholder=\"Comment vous êtes-vous senti ?\"\n                    />\n                  </div>\n                </div>\n\n                <div className=\"flex gap-3 mt-6\">\n                  <button\n                    onClick={() => setShowPerformanceModal(false)}\n                    className=\"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\"\n                  >\n                    Annuler\n                  </button>\n                  <button\n                    onClick={() => handleAddToHistory(selectedExercise)}\n                    className=\"flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n                  >\n                    Enregistrer\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Progress Modal */}\n        {showProgressModal && progressData && (\n          <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\">\n            <div className=\"bg-white rounded-lg max-w-lg w-full\">\n              <div className=\"p-6\">\n                <div className=\"flex items-center justify-between mb-6\">\n                  <h2 className=\"text-xl font-bold text-gray-900\">\n                    Statistiques de progression\n                  </h2>\n                  <button\n                    onClick={() => setShowProgressModal(false)}\n                    className=\"text-gray-400 hover:text-gray-600\"\n                  >\n                    <FiX className=\"h-6 w-6\" />\n                  </button>\n                </div>\n\n                <div className=\"space-y-4\">\n                  <div className=\"grid grid-cols-2 gap-4\">\n                    <div className=\"bg-blue-50 p-4 rounded-lg\">\n                      <div className=\"text-2xl font-bold text-blue-600\">{progressData.totalSessions}</div>\n                      <div className=\"text-sm text-gray-600\">Sessions totales</div>\n                    </div>\n                    <div className=\"bg-green-50 p-4 rounded-lg\">\n                      <div className=\"text-2xl font-bold text-green-600\">\n                        {progressData.lastPerformed ?\n                          new Date(progressData.lastPerformed).toLocaleDateString('fr-FR') :\n                          'Jamais'\n                        }\n                      </div>\n                      <div className=\"text-sm text-gray-600\">Dernière fois</div>\n                    </div>\n                  </div>\n\n                  {progressData.bestPerformance && (\n                    <div className=\"bg-yellow-50 p-4 rounded-lg\">\n                      <h3 className=\"font-semibold text-gray-900 mb-2\">Meilleure performance</h3>\n                      <div className=\"text-sm text-gray-600\">\n                        {progressData.bestPerformance.performance.sets} séries × {progressData.bestPerformance.performance.reps} reps\n                        {progressData.bestPerformance.performance.weight &&\n                          ` à ${progressData.bestPerformance.performance.weight} kg`\n                        }\n                      </div>\n                    </div>\n                  )}\n\n                  <div className=\"bg-gray-50 p-4 rounded-lg\">\n                    <h3 className=\"font-semibold text-gray-900 mb-2\">Tendance</h3>\n                    <div className={`flex items-center ${\n                      progressData.progressTrend === 'improving' ? 'text-green-600' :\n                      progressData.progressTrend === 'declining' ? 'text-red-600' :\n                      'text-gray-600'\n                    }`}>\n                      <FiTrendingUp className=\"h-4 w-4 mr-2\" />\n                      {progressData.progressTrend === 'improving' ? 'En progression' :\n                       progressData.progressTrend === 'declining' ? 'En baisse' :\n                       'Stable'}\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Create Program Modal */}\n        {showCreateProgram && (\n          <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\">\n            <div className=\"bg-white rounded-lg max-w-md w-full\">\n              <div className=\"p-6\">\n                <div className=\"flex items-center justify-between mb-6\">\n                  <h2 className=\"text-xl font-bold text-gray-900\">\n                    Créer un programme\n                  </h2>\n                  <button\n                    onClick={() => setShowCreateProgram(false)}\n                    className=\"text-gray-400 hover:text-gray-600\"\n                  >\n                    <FiX className=\"h-6 w-6\" />\n                  </button>\n                </div>\n\n                <div className=\"mb-4\">\n                  <p className=\"text-gray-600 mb-4\">\n                    {selectedExercisesForProgram.length} exercice(s) sélectionné(s)\n                  </p>\n\n                  {selectedExercisesForProgram.length > 0 && (\n                    <div className=\"space-y-2 mb-4 max-h-40 overflow-y-auto\">\n                      {selectedExercisesForProgram.map(exercise => (\n                        <div key={exercise.id} className=\"flex items-center justify-between bg-gray-50 p-2 rounded\">\n                          <span className=\"text-sm\">{exercise.name}</span>\n                          <button\n                            onClick={() => handleToggleExerciseSelection(exercise)}\n                            className=\"text-red-500 hover:text-red-700\"\n                          >\n                            <FiX className=\"h-4 w-4\" />\n                          </button>\n                        </div>\n                      ))}\n                    </div>\n                  )}\n\n                  <input\n                    type=\"text\"\n                    placeholder=\"Nom du programme\"\n                    className=\"w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    onKeyPress={(e) => {\n                      if (e.key === 'Enter') {\n                        handleCreateProgram(e.target.value);\n                      }\n                    }}\n                  />\n                </div>\n\n                <div className=\"flex gap-3\">\n                  <button\n                    onClick={() => setShowCreateProgram(false)}\n                    className=\"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\"\n                  >\n                    Annuler\n                  </button>\n                  <button\n                    onClick={(e) => {\n                      const input = e.target.parentElement.parentElement.querySelector('input');\n                      handleCreateProgram(input.value);\n                    }}\n                    className=\"flex-1 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors\"\n                  >\n                    Créer\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Exercise Detail Modal */}\n        {selectedExercise && !showPerformanceModal && (\n          <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\">\n            <div className=\"bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto\">\n              <div className=\"p-6\">\n                <div className=\"flex items-center justify-between mb-6\">\n                  <div>\n                    <h2 className=\"text-3xl font-bold text-gray-900 mb-2\">\n                      {selectedExercise.name}\n                    </h2>\n                    <div className=\"flex items-center space-x-3\">\n                      <span className={`px-3 py-1 rounded-full text-sm font-medium ${getDifficultyColor(selectedExercise.difficulty)}`}>\n                        {selectedExercise.difficulty}\n                      </span>\n                      <span className=\"text-gray-600\">\n                        {exerciseCategories[selectedExercise.category]?.icon} {exerciseCategories[selectedExercise.category]?.name}\n                      </span>\n                    </div>\n                  </div>\n                  <button\n                    onClick={() => setSelectedExercise(null)}\n                    className=\"text-gray-400 hover:text-gray-600 text-2xl\"\n                  >\n                    ✕\n                  </button>\n                </div>\n\n                <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n                  {/* Main Content */}\n                  <div className=\"lg:col-span-2\">\n                    <div className=\"mb-6\">\n                      <h3 className=\"text-xl font-semibold text-gray-900 mb-3\">Description</h3>\n                      <p className=\"text-gray-600 leading-relaxed\">{selectedExercise.description}</p>\n                    </div>\n\n                    <div className=\"mb-6\">\n                      <h3 className=\"text-xl font-semibold text-gray-900 mb-4\">Instructions détaillées</h3>\n                      <ol className=\"list-decimal list-inside space-y-3\">\n                        {selectedExercise.instructions.map((instruction, index) => (\n                          <li key={index} className=\"text-gray-600 leading-relaxed pl-2\">\n                            {instruction}\n                          </li>\n                        ))}\n                      </ol>\n                    </div>\n\n                    {selectedExercise.tips && (\n                      <div className=\"mb-6\">\n                        <h3 className=\"text-xl font-semibold text-gray-900 mb-4\">Conseils</h3>\n                        <ul className=\"space-y-2\">\n                          {selectedExercise.tips.map((tip, index) => (\n                            <li key={index} className=\"flex items-start text-gray-600\">\n                              <span className=\"text-blue-500 mr-2 mt-1\">•</span>\n                              {tip}\n                            </li>\n                          ))}\n                        </ul>\n                      </div>\n                    )}\n\n                    {selectedExercise.variations && (\n                      <div className=\"mb-6\">\n                        <h3 className=\"text-xl font-semibold text-gray-900 mb-4\">Variations</h3>\n                        <ul className=\"space-y-2\">\n                          {selectedExercise.variations.map((variation, index) => (\n                            <li key={index} className=\"flex items-start text-gray-600\">\n                              <span className=\"text-green-500 mr-2 mt-1\">•</span>\n                              {variation}\n                            </li>\n                          ))}\n                        </ul>\n                      </div>\n                    )}\n                  </div>\n\n                  {/* Sidebar */}\n                  <div className=\"lg:col-span-1\">\n                    <div className=\"bg-gray-50 rounded-lg p-6 mb-6\">\n                      <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Informations</h3>\n\n                      <div className=\"space-y-4\">\n                        <div className=\"flex justify-between\">\n                          <span className=\"text-gray-600\">Durée</span>\n                          <span className=\"font-medium\">{selectedExercise.duration} min</span>\n                        </div>\n                        <div className=\"flex justify-between\">\n                          <span className=\"text-gray-600\">Calories</span>\n                          <span className=\"font-medium\">{selectedExercise.calories} cal</span>\n                        </div>\n                        <div className=\"flex justify-between\">\n                          <span className=\"text-gray-600\">Équipement</span>\n                          <span className=\"font-medium\">{equipmentTypes[selectedExercise.equipment]?.name}</span>\n                        </div>\n                      </div>\n                    </div>\n\n                    <div className=\"bg-gray-50 rounded-lg p-6 mb-6\">\n                      <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Groupes musculaires</h3>\n                      <div className=\"flex flex-wrap gap-2\">\n                        {selectedExercise.muscleGroups.map(mg => (\n                          <span key={mg} className=\"px-3 py-1 bg-blue-100 text-blue-700 text-sm rounded-full\">\n                            {muscleGroups[mg]?.icon} {muscleGroups[mg]?.name}\n                          </span>\n                        ))}\n                      </div>\n                    </div>\n\n                    <div className=\"space-y-3\">\n                      <button\n                        onClick={() => toggleFavorite(selectedExercise.id)}\n                        className={`w-full py-3 px-6 rounded-lg font-medium transition-colors flex items-center justify-center ${\n                          favoriteExercises.some(fav => fav.id === selectedExercise.id)\n                            ? 'bg-red-100 text-red-700 hover:bg-red-200'\n                            : 'bg-gray-100 text-gray-700 hover:bg-gray-200'\n                        }`}\n                      >\n                        <FiHeart className=\"h-5 w-5 mr-2\" />\n                        {favoriteExercises.some(fav => fav.id === selectedExercise.id) ? 'Retirer des favoris' : 'Ajouter aux favoris'}\n                      </button>\n\n                      <button className=\"w-full bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center font-medium\">\n                        <FiPlay className=\"h-5 w-5 mr-2\" />\n                        Commencer l'exercice\n                      </button>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Modal de notation */}\n        {showRatingModal && ratingExercise && (\n          <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n            <div className=\"bg-white rounded-lg p-6 max-w-md w-full mx-4\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">\n                Noter l'exercice : {ratingExercise.name}\n              </h3>\n\n              <div className=\"space-y-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Votre note\n                  </label>\n                  <div className=\"flex items-center justify-center space-x-2\">\n                    {renderStars(0, true, (rating) => {\n                      const comment = exerciseComments[ratingExercise.id] || '';\n                      rateExercise(ratingExercise.id, rating, comment);\n                    })}\n                  </div>\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Commentaire (optionnel)\n                  </label>\n                  <textarea\n                    defaultValue={exerciseComments[ratingExercise.id] || ''}\n                    onChange={(e) => {\n                      const updatedComments = { ...exerciseComments, [ratingExercise.id]: e.target.value };\n                      setExerciseComments(updatedComments);\n                    }}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    rows=\"3\"\n                    placeholder=\"Partagez votre expérience avec cet exercice...\"\n                  />\n                </div>\n              </div>\n\n              <div className=\"flex space-x-3 mt-6\">\n                <button\n                  onClick={() => {\n                    const rating = exerciseRatings[ratingExercise.id] || 0;\n                    const comment = exerciseComments[ratingExercise.id] || '';\n                    if (rating > 0) {\n                      rateExercise(ratingExercise.id, rating, comment);\n                    }\n                  }}\n                  className=\"flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors\"\n                >\n                  Sauvegarder\n                </button>\n                <button\n                  onClick={() => {\n                    setShowRatingModal(false);\n                    setRatingExercise(null);\n                  }}\n                  className=\"flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-400 transition-colors\"\n                >\n                  Annuler\n                </button>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default Exercises;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,MAAM,EACNC,OAAO,EACPC,KAAK,EACLC,QAAQ,EACRC,QAAQ,EACRC,QAAQ,EACRC,OAAO,EACPC,MAAM,EACNC,MAAM,EACNC,MAAM,EACNC,YAAY,EACZC,UAAU,EACVC,OAAO,EACPC,WAAW,EACXC,UAAU,EACVC,GAAG,QAEE,gBAAgB;AACvB,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SACEC,SAAS,EACTC,kBAAkB,EAClBC,YAAY,EACZC,cAAc,EACdC,kBAAkB,EAClBC,uBAAuB,EACvBC,eAAe,EACfC,iBAAiB,EACjBC,cAAc,EACdC,aAAa,EACbC,YAAY,EACZC,kBAAkB,EAClBC,sBAAsB,EACtBC,mBAAmB,EACnBC,gBAAgB,QACX,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEhC,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA;EACtB,MAAM;IAAEC;EAAK,CAAC,GAAGzB,OAAO,CAAC,CAAC;EAE1B,MAAM,CAAC0B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG7C,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAAC8C,WAAW,EAAEC,cAAc,CAAC,GAAG/C,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACgD,WAAW,EAAEC,cAAc,CAAC,GAAGjD,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACkD,OAAO,EAAEC,UAAU,CAAC,GAAGnD,QAAQ,CAAC;IACrCoD,QAAQ,EAAE,KAAK;IACfC,WAAW,EAAE,KAAK;IAClBC,SAAS,EAAE,KAAK;IAChBC,UAAU,EAAE,KAAK;IACjBC,WAAW,EAAE;EACf,CAAC,CAAC;EACF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG1D,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC2D,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG5D,QAAQ,CAAC,EAAE,CAAC;EACpE,MAAM,CAAC6D,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG9D,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAAC+D,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGhE,QAAQ,CAACmB,SAAS,CAAC;;EAErE;EACA,MAAM,CAAC8C,eAAe,EAAEC,kBAAkB,CAAC,GAAGlE,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACmE,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGpE,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACqE,2BAA2B,EAAEC,8BAA8B,CAAC,GAAGtE,QAAQ,CAAC,EAAE,CAAC;EAClF,MAAM,CAACuE,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGxE,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACyE,YAAY,EAAEC,eAAe,CAAC,GAAG1E,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC2E,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG5E,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAAC6E,eAAe,EAAEC,kBAAkB,CAAC,GAAG9E,QAAQ,CAAC;IACrD+E,IAAI,EAAE,CAAC;IACPC,IAAI,EAAE,EAAE;IACRC,MAAM,EAAE,EAAE;IACVC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGrF,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1D,MAAM,CAACsF,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvF,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5D,MAAM,CAACwF,eAAe,EAAEC,kBAAkB,CAAC,GAAGzF,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC0F,cAAc,EAAEC,iBAAiB,CAAC,GAAG3F,QAAQ,CAAC,IAAI,CAAC;EAE1DC,SAAS,CAAC,MAAM;IACd;IACA,IAAI0C,IAAI,EAAE;MACR,MAAMiD,eAAe,GAAGpE,uBAAuB,CAACmB,IAAI,EAAEA,IAAI,CAACkD,WAAW,IAAI,iBAAiB,CAAC;MAC5FjC,uBAAuB,CAACgC,eAAe,CAAC;IAC1C;;IAEA;IACA,MAAME,cAAc,GAAGlE,aAAa,CAAC,CAAC;IACtC,MAAMmE,uBAAuB,GAAG5E,SAAS,CAAC6E,MAAM,CAACC,EAAE,IAAIH,cAAc,CAACI,QAAQ,CAACD,EAAE,CAACE,EAAE,CAAC,CAAC;IACtFrC,oBAAoB,CAACiC,uBAAuB,CAAC;;IAE7C;IACA,MAAMK,OAAO,GAAGtE,kBAAkB,CAAC,CAAC;IACpCoC,kBAAkB,CAACkC,OAAO,CAAC;;IAE3B;IACA,IAAI;MACF,MAAMC,YAAY,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,iBAAiB,CAAC,IAAI,IAAI,CAAC;MAChF,MAAMC,aAAa,GAAGJ,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,kBAAkB,CAAC,IAAI,IAAI,CAAC;MAClFpB,kBAAkB,CAACgB,YAAY,CAAC;MAChCd,mBAAmB,CAACmB,aAAa,CAAC;IACpC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;IAC9D;EACF,CAAC,EAAE,CAAChE,IAAI,CAAC,CAAC;EAEV1C,SAAS,CAAC,MAAM;IACd;IACA,IAAI4G,OAAO,GAAG1F,SAAS;IAEvB,IAAIsC,SAAS,KAAK,aAAa,EAAE;MAC/BoD,OAAO,GAAGlD,oBAAoB;IAChC,CAAC,MAAM,IAAIF,SAAS,KAAK,WAAW,EAAE;MACpCoD,OAAO,GAAGhD,iBAAiB;IAC7B;IAEA,IAAIf,WAAW,IAAIgE,MAAM,CAACC,MAAM,CAAC7D,OAAO,CAAC,CAAC8D,IAAI,CAACC,CAAC,IAAIA,CAAC,KAAK,KAAK,IAAIA,CAAC,KAAK,IAAI,CAAC,EAAE;MAC9EJ,OAAO,GAAGpF,eAAe,CAACqB,WAAW,EAAEI,OAAO,CAAC;IACjD;IAEAc,oBAAoB,CAAC6C,OAAO,CAAC;EAC/B,CAAC,EAAE,CAAC/D,WAAW,EAAEI,OAAO,EAAEO,SAAS,EAAEE,oBAAoB,EAAEE,iBAAiB,CAAC,CAAC;EAE9E,MAAMqD,kBAAkB,GAAGA,CAACC,UAAU,EAAEC,KAAK,KAAK;IAChDjE,UAAU,CAACkE,IAAI,KAAK;MAClB,GAAGA,IAAI;MACP,CAACF,UAAU,GAAGC;IAChB,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAME,YAAY,GAAGA,CAAA,KAAM;IACzBnE,UAAU,CAAC;MACTC,QAAQ,EAAE,KAAK;MACfC,WAAW,EAAE,KAAK;MAClBC,SAAS,EAAE,KAAK;MAChBC,UAAU,EAAE,KAAK;MACjBC,WAAW,EAAE;IACf,CAAC,CAAC;IACFT,cAAc,CAAC,EAAE,CAAC;EACpB,CAAC;EAED,MAAMwE,oBAAoB,GAAGA,CAAA,KAAM;IACjC,MAAMC,iBAAiB,GAAGzD,iBAAiB,CAAC0D,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;IACvD,MAAMC,OAAO,GAAGhG,iBAAiB,CAAC8F,iBAAiB,EAAE,EAAE,CAAC;;IAExD;IACAG,KAAK,CAAC,8BAA8BD,OAAO,CAACE,aAAa,4BAA4BF,OAAO,CAACG,iBAAiB,gBAAgBH,OAAO,CAACvG,SAAS,CAAC2G,MAAM,EAAE,CAAC;EAC3J,CAAC;EAED,MAAMC,oBAAoB,GAAIC,UAAU,IAAK;IAC3C,MAAMlC,cAAc,GAAGlE,aAAa,CAAC,CAAC;IACtC,MAAMqG,aAAa,GAAGtG,cAAc,CAACqG,UAAU,EAAElC,cAAc,EAAE,MAAM,CAAC,CAAC,CAAC;;IAE1E;IACA,IAAImC,aAAa,EAAE;MACjB,MAAMC,QAAQ,GAAG/G,SAAS,CAACgH,IAAI,CAAClC,EAAE,IAAIA,EAAE,CAACE,EAAE,KAAK6B,UAAU,CAAC;MAC3DlE,oBAAoB,CAACuD,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEa,QAAQ,CAAC,CAAC;IACnD,CAAC,MAAM;MACLpE,oBAAoB,CAACuD,IAAI,IAAIA,IAAI,CAACrB,MAAM,CAACC,EAAE,IAAIA,EAAE,CAACE,EAAE,KAAK6B,UAAU,CAAC,CAAC;IACvE;EACF,CAAC;;EAED;EACA,MAAMI,kBAAkB,GAAIF,QAAQ,IAAK;IACvC,MAAMG,KAAK,GAAGxG,YAAY,CAACqG,QAAQ,EAAErD,eAAe,CAAC;IACrD,IAAIwD,KAAK,EAAE;MACTnE,kBAAkB,CAACmD,IAAI,IAAI,CAACgB,KAAK,EAAE,GAAGhB,IAAI,CAAC,CAAC;MAC5CzC,uBAAuB,CAAC,KAAK,CAAC;MAC9BE,kBAAkB,CAAC;QACjBC,IAAI,EAAE,CAAC;QACPC,IAAI,EAAE,EAAE;QACRC,MAAM,EAAE,EAAE;QACVC,QAAQ,EAAE,EAAE;QACZC,KAAK,EAAE;MACT,CAAC,CAAC;MACFwC,KAAK,CAAC,sCAAsC,CAAC;IAC/C;EACF,CAAC;;EAED;EACA,MAAMW,kBAAkB,GAAIN,UAAU,IAAK;IACzC,MAAMO,KAAK,GAAGxG,sBAAsB,CAACiG,UAAU,CAAC;IAChDtD,eAAe,CAAC6D,KAAK,CAAC;IACtB/D,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMgE,6BAA6B,GAAIN,QAAQ,IAAK;IAClD5D,8BAA8B,CAAC+C,IAAI,IAAI;MACrC,MAAMoB,UAAU,GAAGpB,IAAI,CAACL,IAAI,CAACf,EAAE,IAAIA,EAAE,CAACE,EAAE,KAAK+B,QAAQ,CAAC/B,EAAE,CAAC;MACzD,IAAIsC,UAAU,EAAE;QACd,OAAOpB,IAAI,CAACrB,MAAM,CAACC,EAAE,IAAIA,EAAE,CAACE,EAAE,KAAK+B,QAAQ,CAAC/B,EAAE,CAAC;MACjD,CAAC,MAAM;QACL,OAAO,CAAC,GAAGkB,IAAI,EAAEa,QAAQ,CAAC;MAC5B;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMQ,mBAAmB,GAAIC,WAAW,IAAK;IAC3C,IAAItE,2BAA2B,CAACyD,MAAM,KAAK,CAAC,EAAE;MAC5CH,KAAK,CAAC,4CAA4C,CAAC;MACnD;IACF;IAEA,MAAMiB,OAAO,GAAG5G,mBAAmB,CAAC2G,WAAW,EAAEtE,2BAA2B,CAAC;IAC7EC,8BAA8B,CAAC,EAAE,CAAC;IAClCF,oBAAoB,CAAC,KAAK,CAAC;IAC3BuD,KAAK,CAAC,cAAciB,OAAO,CAACC,IAAI,sBAAsB,CAAC;EACzD,CAAC;EAED,MAAMC,YAAY,GAAGA,CAACd,UAAU,EAAEe,MAAM,EAAEC,OAAO,GAAG,EAAE,KAAK;IACzD,MAAMC,cAAc,GAAG;MAAE,GAAG7D,eAAe;MAAE,CAAC4C,UAAU,GAAGe;IAAO,CAAC;IACnE,MAAMG,eAAe,GAAG;MAAE,GAAG5D,gBAAgB;MAAE,CAAC0C,UAAU,GAAGgB;IAAQ,CAAC;IAEtE3D,kBAAkB,CAAC4D,cAAc,CAAC;IAClC1D,mBAAmB,CAAC2D,eAAe,CAAC;IAEpC1C,YAAY,CAAC2C,OAAO,CAAC,iBAAiB,EAAE7C,IAAI,CAAC8C,SAAS,CAACH,cAAc,CAAC,CAAC;IACvEzC,YAAY,CAAC2C,OAAO,CAAC,kBAAkB,EAAE7C,IAAI,CAAC8C,SAAS,CAACF,eAAe,CAAC,CAAC;IAEzEzD,kBAAkB,CAAC,KAAK,CAAC;IACzBE,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAM0D,eAAe,GAAInB,QAAQ,IAAK;IACpCvC,iBAAiB,CAACuC,QAAQ,CAAC;IAC3BzC,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAM6D,gBAAgB,GAAItB,UAAU,IAAK;IACvC,OAAO5C,eAAe,CAAC4C,UAAU,CAAC,IAAI,CAAC;EACzC,CAAC;EAED,MAAMuB,WAAW,GAAGA,CAACR,MAAM,EAAES,WAAW,GAAG,KAAK,EAAEC,MAAM,GAAG,IAAI,KAAK;IAClE,oBACEtH,OAAA;MAAKuH,SAAS,EAAC,6BAA6B;MAAAC,QAAA,EACzC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAACC,IAAI,iBACvB1H,OAAA;QAEE2H,OAAO,EAAEA,CAAA,KAAMN,WAAW,IAAIC,MAAM,IAAIA,MAAM,CAACI,IAAI,CAAE;QACrDH,SAAS,EAAE,GAAGF,WAAW,GAAG,gCAAgC,GAAG,gBAAgB,uBAAwB;QACvGO,QAAQ,EAAE,CAACP,WAAY;QAAAG,QAAA,eAEvBxH,OAAA,CAAC1B,MAAM;UACLiJ,SAAS,EAAE,WACTG,IAAI,IAAId,MAAM,GACV,8BAA8B,GAC9B,eAAe;QAClB;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC,GAXGN,IAAI;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAYH,CACT;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEV,CAAC;EAED,oBACEhI,OAAA;IAAKuH,SAAS,EAAC,yBAAyB;IAAAC,QAAA,eACtCxH,OAAA;MAAKuH,SAAS,EAAC,6CAA6C;MAAAC,QAAA,gBAE1DxH,OAAA;QAAKuH,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BxH,OAAA;UAAIuH,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAEtD;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLhI,OAAA;UAAGuH,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EAAC;QAGvD;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGNhI,OAAA;QAAKuH,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDxH,OAAA;UAAKuH,SAAS,EAAC,8CAA8C;UAAAC,QAAA,gBAC3DxH,OAAA;YAAKuH,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9BxH,OAAA,CAAC7B,QAAQ;cAACoJ,SAAS,EAAC;YAA6C;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpEhI,OAAA;cACEiI,IAAI,EAAC,MAAM;cACXC,WAAW,EAAC,8CAA8C;cAC1DjD,KAAK,EAAEtE,WAAY;cACnBwH,QAAQ,EAAGC,CAAC,IAAKxH,cAAc,CAACwH,CAAC,CAACC,MAAM,CAACpD,KAAK,CAAE;cAChDsC,SAAS,EAAC;YAA8G;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNhI,OAAA;YAAKuH,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnCxH,OAAA;cACE2H,OAAO,EAAEA,CAAA,KAAM7G,cAAc,CAAC,CAACD,WAAW,CAAE;cAC5C0G,SAAS,EAAE,wEACT1G,WAAW,GAAG,wBAAwB,GAAG,6CAA6C,EACrF;cAAA2G,QAAA,gBAEHxH,OAAA,CAAC9B,QAAQ;gBAACqJ,SAAS,EAAC;cAAc;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,WAEvC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACThI,OAAA;cACE2H,OAAO,EAAEvC,oBAAqB;cAC9BmC,SAAS,EAAC,qGAAqG;cAAAC,QAAA,gBAE/GxH,OAAA,CAAC5B,QAAQ;gBAACmJ,SAAS,EAAC;cAAc;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,oBAEvC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACThI,OAAA;cACE2H,OAAO,EAAEA,CAAA,KAAM1F,oBAAoB,CAAC,IAAI,CAAE;cAC1CsF,SAAS,EAAC,uGAAuG;cAAAC,QAAA,gBAEjHxH,OAAA,CAACxB,MAAM;gBAAC+I,SAAS,EAAC;cAAc;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,sBAErC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACThI,OAAA;cACE2H,OAAO,EAAEA,CAAA,KAAMpG,YAAY,CAAC,SAAS,CAAE;cACvCgG,SAAS,EAAC,uGAAuG;cAAAC,QAAA,gBAEjHxH,OAAA,CAACnB,UAAU;gBAAC0I,SAAS,EAAC;cAAc;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,cAEzC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGLnH,WAAW,iBACVb,OAAA;UAAKuH,SAAS,EAAC,oCAAoC;UAAAC,QAAA,gBACjDxH,OAAA;YAAKuH,SAAS,EAAC,sDAAsD;YAAAC,QAAA,gBACnExH,OAAA;cAAAwH,QAAA,gBACExH,OAAA;gBAAOuH,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAS;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACjFhI,OAAA;gBACEiF,KAAK,EAAElE,OAAO,CAACE,QAAS;gBACxBkH,QAAQ,EAAGC,CAAC,IAAKrD,kBAAkB,CAAC,UAAU,EAAEqD,CAAC,CAACC,MAAM,CAACpD,KAAK,CAAE;gBAChEsC,SAAS,EAAC,kGAAkG;gBAAAC,QAAA,gBAE5GxH,OAAA;kBAAQiF,KAAK,EAAC,KAAK;kBAAAuC,QAAA,EAAC;gBAAqB;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EACjDrD,MAAM,CAAC2D,OAAO,CAACrJ,kBAAkB,CAAC,CAACwI,GAAG,CAAC,CAAC,CAACc,GAAG,EAAEtH,QAAQ,CAAC,kBACtDjB,OAAA;kBAAkBiF,KAAK,EAAEsD,GAAI;kBAAAf,QAAA,EAAEvG,QAAQ,CAACyF;gBAAI,GAA/B6B,GAAG;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAqC,CACtD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENhI,OAAA;cAAAwH,QAAA,gBACExH,OAAA;gBAAOuH,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAiB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACzFhI,OAAA;gBACEiF,KAAK,EAAElE,OAAO,CAACG,WAAY;gBAC3BiH,QAAQ,EAAGC,CAAC,IAAKrD,kBAAkB,CAAC,aAAa,EAAEqD,CAAC,CAACC,MAAM,CAACpD,KAAK,CAAE;gBACnEsC,SAAS,EAAC,kGAAkG;gBAAAC,QAAA,gBAE5GxH,OAAA;kBAAQiF,KAAK,EAAC,KAAK;kBAAAuC,QAAA,EAAC;gBAAgB;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EAC5CrD,MAAM,CAAC2D,OAAO,CAACpJ,YAAY,CAAC,CAACuI,GAAG,CAAC,CAAC,CAACc,GAAG,EAAEC,KAAK,CAAC,kBAC7CxI,OAAA;kBAAkBiF,KAAK,EAAEsD,GAAI;kBAAAf,QAAA,EAAEgB,KAAK,CAAC9B;gBAAI,GAA5B6B,GAAG;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAkC,CACnD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENhI,OAAA;cAAAwH,QAAA,gBACExH,OAAA;gBAAOuH,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAU;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAClFhI,OAAA;gBACEiF,KAAK,EAAElE,OAAO,CAACI,SAAU;gBACzBgH,QAAQ,EAAGC,CAAC,IAAKrD,kBAAkB,CAAC,WAAW,EAAEqD,CAAC,CAACC,MAAM,CAACpD,KAAK,CAAE;gBACjEsC,SAAS,EAAC,kGAAkG;gBAAAC,QAAA,gBAE5GxH,OAAA;kBAAQiF,KAAK,EAAC,KAAK;kBAAAuC,QAAA,EAAC;gBAAe;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EAC3CrD,MAAM,CAAC2D,OAAO,CAACnJ,cAAc,CAAC,CAACsI,GAAG,CAAC,CAAC,CAACc,GAAG,EAAEpH,SAAS,CAAC,kBACnDnB,OAAA;kBAAkBiF,KAAK,EAAEsD,GAAI;kBAAAf,QAAA,EAAErG,SAAS,CAACuF;gBAAI,GAAhC6B,GAAG;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAsC,CACvD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENhI,OAAA;cAAAwH,QAAA,gBACExH,OAAA;gBAAOuH,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAU;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAClFhI,OAAA;gBACEiF,KAAK,EAAElE,OAAO,CAACK,UAAW;gBAC1B+G,QAAQ,EAAGC,CAAC,IAAKrD,kBAAkB,CAAC,YAAY,EAAEqD,CAAC,CAACC,MAAM,CAACpD,KAAK,CAAE;gBAClEsC,SAAS,EAAC,kGAAkG;gBAAAC,QAAA,gBAE5GxH,OAAA;kBAAQiF,KAAK,EAAC,KAAK;kBAAAuC,QAAA,EAAC;gBAAkB;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC/ChI,OAAA;kBAAQiF,KAAK,EAAC,QAAQ;kBAAAuC,QAAA,EAAC;gBAAM;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtChI,OAAA;kBAAQiF,KAAK,EAAC,cAAQ;kBAAAuC,QAAA,EAAC;gBAAM;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtChI,OAAA;kBAAQiF,KAAK,EAAC,WAAW;kBAAAuC,QAAA,EAAC;gBAAS;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENhI,OAAA;YAAKuH,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACrDxH,OAAA;cAAKuH,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1CxH,OAAA;gBAAOuH,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,EAAC;cAAgB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACnFhI,OAAA;gBACEiI,IAAI,EAAC,QAAQ;gBACbhD,KAAK,EAAElE,OAAO,CAACM,WAAW,IAAI,EAAG;gBACjC8G,QAAQ,EAAGC,CAAC,IAAKrD,kBAAkB,CAAC,aAAa,EAAEqD,CAAC,CAACC,MAAM,CAACpD,KAAK,GAAGwD,QAAQ,CAACL,CAAC,CAACC,MAAM,CAACpD,KAAK,CAAC,GAAG,IAAI,CAAE;gBACrGiD,WAAW,EAAC,IAAI;gBAChBX,SAAS,EAAC;cAAgG;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3G,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNhI,OAAA;cACE2H,OAAO,EAAExC,YAAa;cACtBoC,SAAS,EAAC,+DAA+D;cAAAC,QAAA,EAC1E;YAED;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNhI,OAAA;QAAKuH,SAAS,EAAC,gDAAgD;QAAAC,QAAA,gBAC7DxH,OAAA;UACE2H,OAAO,EAAEA,CAAA,KAAMpG,YAAY,CAAC,KAAK,CAAE;UACnCgG,SAAS,EAAE,qEACTjG,SAAS,KAAK,KAAK,GACf,kCAAkC,GAClC,mCAAmC,EACtC;UAAAkG,QAAA,GACJ,QACO,EAACxI,SAAS,CAAC2G,MAAM,EAAC,GAC1B;QAAA;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EACRxH,IAAI,iBACHR,OAAA,CAAAE,SAAA;UAAAsH,QAAA,gBACExH,OAAA;YACE2H,OAAO,EAAEA,CAAA,KAAMpG,YAAY,CAAC,aAAa,CAAE;YAC3CgG,SAAS,EAAE,qEACTjG,SAAS,KAAK,aAAa,GACvB,kCAAkC,GAClC,mCAAmC,EACtC;YAAAkG,QAAA,gBAEHxH,OAAA,CAAC1B,MAAM;cAACiJ,SAAS,EAAC;YAAa;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,oBACrB,EAACxG,oBAAoB,CAACmE,MAAM,EAAC,GAC5C;UAAA;YAAAkC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACThI,OAAA;YACE2H,OAAO,EAAEA,CAAA,KAAMpG,YAAY,CAAC,WAAW,CAAE;YACzCgG,SAAS,EAAE,qEACTjG,SAAS,KAAK,WAAW,GACrB,kCAAkC,GAClC,mCAAmC,EACtC;YAAAkG,QAAA,gBAEHxH,OAAA,CAAC3B,OAAO;cAACkJ,SAAS,EAAC;YAAa;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,aAC1B,EAACtG,iBAAiB,CAACiE,MAAM,EAAC,GACrC;UAAA;YAAAkC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACThI,OAAA;YACE2H,OAAO,EAAEA,CAAA,KAAMpG,YAAY,CAAC,SAAS,CAAE;YACvCgG,SAAS,EAAE,qEACTjG,SAAS,KAAK,SAAS,GACnB,kCAAkC,GAClC,mCAAmC,EACtC;YAAAkG,QAAA,gBAEHxH,OAAA,CAACnB,UAAU;cAAC0I,SAAS,EAAC;YAAa;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAC1B,EAAClG,eAAe,CAAC6D,MAAM,EAAC,GACtC;UAAA;YAAAkC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA,eACT,CACH;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNhI,OAAA;QAAKuH,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnBxH,OAAA;UAAGuH,SAAS,EAAC,eAAe;UAAAC,QAAA,GACzB5F,iBAAiB,CAAC+D,MAAM,EAAC,WAAS,EAAC/D,iBAAiB,CAAC+D,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAC,YAAO,EAAC/D,iBAAiB,CAAC+D,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAC1HhF,WAAW,IAAI,UAAUA,WAAW,GAAG;QAAA;UAAAkH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,EAGL1G,SAAS,KAAK,SAAS;MAAA;MACtB;MACAtB,OAAA;QAAKuH,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDxH,OAAA;UAAIuH,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAwB;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAClFlG,eAAe,CAAC6D,MAAM,KAAK,CAAC,gBAC3B3F,OAAA;UAAKuH,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCxH,OAAA,CAACnB,UAAU;YAAC0I,SAAS,EAAC;UAAsC;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/DhI,OAAA;YAAIuH,SAAS,EAAC,wCAAwC;YAAAC,QAAA,EAAC;UAAgC;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5FhI,OAAA;YAAGuH,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAsD;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpF,CAAC,gBAENhI,OAAA;UAAKuH,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvB1F,eAAe,CAACwD,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAACmC,GAAG,CAACvB,KAAK,iBACrClG,OAAA;YAAoBuH,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACnExH,OAAA;cAAKuH,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrDxH,OAAA;gBAAIuH,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,EAAEtB,KAAK,CAACwC;cAAY;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACrEhI,OAAA;gBAAMuH,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EACpC,IAAImB,IAAI,CAACzC,KAAK,CAAC0C,IAAI,CAAC,CAACC,kBAAkB,CAAC,OAAO;cAAC;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNhI,OAAA;cAAKuH,SAAS,EAAC,+CAA+C;cAAAC,QAAA,gBAC5DxH,OAAA;gBAAAwH,QAAA,gBACExH,OAAA;kBAAMuH,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAO;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC9ChI,OAAA;kBAAMuH,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,EAAEtB,KAAK,CAAC4C,WAAW,CAAClG;gBAAI;kBAAAiF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D,CAAC,eACNhI,OAAA;gBAAAwH,QAAA,gBACExH,OAAA;kBAAMuH,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAY;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACnDhI,OAAA;kBAAMuH,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,EAAEtB,KAAK,CAAC4C,WAAW,CAACjG;gBAAI;kBAAAgF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D,CAAC,EACL9B,KAAK,CAAC4C,WAAW,CAAChG,MAAM,iBACvB9C,OAAA;gBAAAwH,QAAA,gBACExH,OAAA;kBAAMuH,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAM;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC7ChI,OAAA;kBAAMuH,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,GAAEtB,KAAK,CAAC4C,WAAW,CAAChG,MAAM,EAAC,KAAG;gBAAA;kBAAA+E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpE,CACN,EACA9B,KAAK,CAAC4C,WAAW,CAAC/F,QAAQ,iBACzB/C,OAAA;gBAAAwH,QAAA,gBACExH,OAAA;kBAAMuH,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAM;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC7ChI,OAAA;kBAAMuH,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,GAAEtB,KAAK,CAAC4C,WAAW,CAAC/F,QAAQ,EAAC,MAAI;gBAAA;kBAAA8E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvE,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,EACL9B,KAAK,CAAC4C,WAAW,CAAC9F,KAAK,iBACtBhD,OAAA;cAAKuH,SAAS,EAAC,4BAA4B;cAAAC,QAAA,gBACzCxH,OAAA;gBAAMuH,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAM;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,KAAC,EAAC9B,KAAK,CAAC4C,WAAW,CAAC9F,KAAK;YAAA;cAAA6E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjE,CACN;UAAA,GAjCO9B,KAAK,CAAClC,EAAE;YAAA6D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAkCb,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;MAAA;MAEN;MACAhI,OAAA;QAAKuH,SAAS,EAAC,2DAA2D;QAAAC,QAAA,EACvE,CAAClG,SAAS,KAAK,KAAK,GAAGM,iBAAiB,GACvCN,SAAS,KAAK,aAAa,GAAGE,oBAAoB,GAClDE,iBAAiB,EAAE+F,GAAG,CAAC1B,QAAQ,IAAI;UAAA,IAAAgD,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA;UACrC,MAAMC,UAAU,GAAGzH,iBAAiB,CAACmD,IAAI,CAACuE,GAAG,IAAIA,GAAG,CAACpF,EAAE,KAAK+B,QAAQ,CAAC/B,EAAE,CAAC;UAExE,oBACEhE,OAAA;YAEEuH,SAAS,EAAC,iFAAiF;YAAAC,QAAA,eAE3FxH,OAAA;cAAKuH,SAAS,EAAC,KAAK;cAAAC,QAAA,gBAClBxH,OAAA;gBAAKuH,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,gBACpDxH,OAAA;kBAAKuH,SAAS,EAAC,QAAQ;kBAAAC,QAAA,gBACrBxH,OAAA;oBAAIuH,SAAS,EAAC,0CAA0C;oBAAAC,QAAA,EACrDzB,QAAQ,CAACW;kBAAI;oBAAAmB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACZ,CAAC,eACLhI,OAAA;oBAAKuH,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,gBAC/CxH,OAAA;sBAAMuH,SAAS,EAAE,8CAA8CnI,kBAAkB,CAAC2G,QAAQ,CAAC3E,UAAU,CAAC,EAAG;sBAAAoG,QAAA,EACtGzB,QAAQ,CAAC3E;oBAAU;sBAAAyG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChB,CAAC,eACPhI,OAAA;sBAAMuH,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,IAAAuB,qBAAA,GACpC9J,kBAAkB,CAAC8G,QAAQ,CAAC9E,QAAQ,CAAC,cAAA8H,qBAAA,uBAArCA,qBAAA,CAAuCM,IAAI,EAAC,GAAC,GAAAL,sBAAA,GAAC/J,kBAAkB,CAAC8G,QAAQ,CAAC9E,QAAQ,CAAC,cAAA+H,sBAAA,uBAArCA,sBAAA,CAAuCtC,IAAI;oBAAA;sBAAAmB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNhI,OAAA;kBAAKuH,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC7BxH,OAAA;oBACE2H,OAAO,EAAGS,CAAC,IAAK;sBACdA,CAAC,CAACkB,eAAe,CAAC,CAAC;sBACnBnD,kBAAkB,CAACJ,QAAQ,CAAC/B,EAAE,CAAC;oBACjC,CAAE;oBACFuD,SAAS,EAAC,sEAAsE;oBAChFgC,KAAK,EAAC,uBAAuB;oBAAA/B,QAAA,eAE7BxH,OAAA,CAACpB,WAAW;sBAAC2I,SAAS,EAAC;oBAAS;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7B,CAAC,eACThI,OAAA;oBACE2H,OAAO,EAAGS,CAAC,IAAK;sBACdA,CAAC,CAACkB,eAAe,CAAC,CAAC;sBACnB1D,oBAAoB,CAACG,QAAQ,CAAC/B,EAAE,CAAC;oBACnC,CAAE;oBACFuD,SAAS,EAAE,sCACT4B,UAAU,GACN,iCAAiC,GACjC,kCAAkC,EACrC;oBACHI,KAAK,EAAEJ,UAAU,GAAG,qBAAqB,GAAG,qBAAsB;oBAAA3B,QAAA,eAElExH,OAAA,CAAC3B,OAAO;sBAACkJ,SAAS,EAAE,WAAW4B,UAAU,GAAG,cAAc,GAAG,EAAE;oBAAG;sBAAAtB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/D,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENhI,OAAA;gBAAGuH,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,EAC3CzB,QAAQ,CAACyD;cAAW;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC,eAGJhI,OAAA;gBAAKuH,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,gBACrDxH,OAAA;kBAAKuH,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,GACzCJ,WAAW,CAACD,gBAAgB,CAACpB,QAAQ,CAAC/B,EAAE,CAAC,CAAC,eAC3ChE,OAAA;oBAAMuH,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,GAAC,GACrC,EAACL,gBAAgB,CAACpB,QAAQ,CAAC/B,EAAE,CAAC,GAAG,CAAC,GAAGmD,gBAAgB,CAACpB,QAAQ,CAAC/B,EAAE,CAAC,CAACyF,OAAO,CAAC,CAAC,CAAC,GAAG,UAAU,EAAC,GAC9F;kBAAA;oBAAA5B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACNhI,OAAA;kBACE2H,OAAO,EAAGS,CAAC,IAAK;oBACdA,CAAC,CAACkB,eAAe,CAAC,CAAC;oBACnBpC,eAAe,CAACnB,QAAQ,CAAC;kBAC3B,CAAE;kBACFwB,SAAS,EAAC,6DAA6D;kBAAAC,QAAA,EACxE;gBAED;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,EAGL7E,gBAAgB,CAAC4C,QAAQ,CAAC/B,EAAE,CAAC,iBAC5BhE,OAAA;gBAAKuH,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,gBAChExH,OAAA;kBAAMuH,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAC;gBAAY;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,EAChD7E,gBAAgB,CAAC4C,QAAQ,CAAC/B,EAAE,CAAC,CAAC2B,MAAM,GAAG,EAAE,GACtC,GAAGxC,gBAAgB,CAAC4C,QAAQ,CAAC/B,EAAE,CAAC,CAAC0F,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,GACtDvG,gBAAgB,CAAC4C,QAAQ,CAAC/B,EAAE,CAAC;cAAA;gBAAA6D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAE9B,CACN,eAGDhI,OAAA;gBAAKuH,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EACvCzB,QAAQ,CAAC7G,YAAY,CAACuI,GAAG,CAACkC,EAAE;kBAAA,IAAAC,gBAAA;kBAAA,oBAC3B5J,OAAA;oBAAeuH,SAAS,EAAC,0DAA0D;oBAAAC,QAAA,GAAAoC,gBAAA,GAChF1K,YAAY,CAACyK,EAAE,CAAC,cAAAC,gBAAA,uBAAhBA,gBAAA,CAAkBlD;kBAAI,GADdiD,EAAE;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEP,CAAC;gBAAA,CACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAGNhI,OAAA;gBAAKuH,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,gBAC3DxH,OAAA;kBAAMuH,SAAS,EAAC,MAAM;kBAAAC,QAAA,GAAAyB,qBAAA,GAAE9J,cAAc,CAAC4G,QAAQ,CAAC5E,SAAS,CAAC,cAAA8H,qBAAA,uBAAlCA,qBAAA,CAAoCI;gBAAI;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACxEhI,OAAA;kBAAAwH,QAAA,GAAA0B,sBAAA,GAAO/J,cAAc,CAAC4G,QAAQ,CAAC5E,SAAS,CAAC,cAAA+H,sBAAA,uBAAlCA,sBAAA,CAAoCxC;gBAAI;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC,eAENhI,OAAA;gBAAKuH,SAAS,EAAC,8DAA8D;gBAAAC,QAAA,gBAC3ExH,OAAA;kBAAKuH,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChCxH,OAAA,CAAChC,OAAO;oBAACuJ,SAAS,EAAC;kBAAc;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EACnCjC,QAAQ,CAAChD,QAAQ,EAAC,MACrB;gBAAA;kBAAA8E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNhI,OAAA;kBAAKuH,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChCxH,OAAA,CAAC/B,KAAK;oBAACsJ,SAAS,EAAC;kBAAc;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EACjCjC,QAAQ,CAAC8D,QAAQ,EAAC,MACrB;gBAAA;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENhI,OAAA;gBAAKuH,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxBxH,OAAA;kBAAKuH,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBxH,OAAA;oBACE2H,OAAO,EAAEA,CAAA,KAAMjH,mBAAmB,CAACqF,QAAQ,CAAE;oBAC7CwB,SAAS,EAAC,oIAAoI;oBAAAC,QAAA,gBAE9IxH,OAAA,CAACzB,MAAM;sBAACgJ,SAAS,EAAC;oBAAc;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,cAErC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACThI,OAAA;oBACE2H,OAAO,EAAEA,CAAA,KAAM;sBACbjH,mBAAmB,CAACqF,QAAQ,CAAC;sBAC7BtD,uBAAuB,CAAC,IAAI,CAAC;oBAC/B,CAAE;oBACF8E,SAAS,EAAC,iIAAiI;oBAAAC,QAAA,gBAE3IxH,OAAA,CAACjC,MAAM;sBAACwJ,SAAS,EAAC;oBAAc;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,aAErC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACNhI,OAAA;kBAAKuH,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBxH,OAAA;oBACE2H,OAAO,EAAGS,CAAC,IAAK;sBACdA,CAAC,CAACkB,eAAe,CAAC,CAAC;sBACnBjD,6BAA6B,CAACN,QAAQ,CAAC;oBACzC,CAAE;oBACFwB,SAAS,EAAE,0FACTrF,2BAA2B,CAAC2C,IAAI,CAACf,EAAE,IAAIA,EAAE,CAACE,EAAE,KAAK+B,QAAQ,CAAC/B,EAAE,CAAC,GACzD,wDAAwD,GACxD,4CAA4C,EAC/C;oBAAAwD,QAAA,gBAEHxH,OAAA,CAACtB,UAAU;sBAAC6I,SAAS,EAAC;oBAAc;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EACtC9F,2BAA2B,CAAC2C,IAAI,CAACf,EAAE,IAAIA,EAAE,CAACE,EAAE,KAAK+B,QAAQ,CAAC/B,EAAE,CAAC,GAAG,aAAa,GAAG,cAAc;kBAAA;oBAAA6D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzF,CAAC,EACRlI,gBAAgB,CAACiG,QAAQ,CAAC/B,EAAE,CAAC,iBAC5BhE,OAAA;oBACE2H,OAAO,EAAEA,CAAA,KAAMmC,MAAM,CAACC,IAAI,CAACjK,gBAAgB,CAACiG,QAAQ,CAAC/B,EAAE,CAAC,EAAE,QAAQ,CAAE;oBACpEuD,SAAS,EAAC,uIAAuI;oBAAAC,QAAA,gBAEjJxH,OAAA,CAACrB,OAAO;sBAAC4I,SAAS,EAAC;oBAAc;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,YAEtC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CACT;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC,GAxJDjC,QAAQ,CAAC/B,EAAE;YAAA6D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAyJb,CAAC;QAEV,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CACN,EAGApG,iBAAiB,CAAC+D,MAAM,KAAK,CAAC,iBAC7B3F,OAAA;QAAKuH,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCxH,OAAA;UAAKuH,SAAS,EAAC,oBAAoB;UAAAC,QAAA,eACjCxH,OAAA,CAAC7B,QAAQ;YAACoJ,SAAS,EAAC;UAAmB;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC,eACNhI,OAAA;UAAIuH,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAEvD;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLhI,OAAA;UAAGuH,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAElC;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJhI,OAAA;UACE2H,OAAO,EAAExC,YAAa;UACtBoC,SAAS,EAAC,iFAAiF;UAAAC,QAAA,EAC5F;QAED;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN,EAGAxF,oBAAoB,IAAI/B,gBAAgB,iBACvCT,OAAA;QAAKuH,SAAS,EAAC,gFAAgF;QAAAC,QAAA,eAC7FxH,OAAA;UAAKuH,SAAS,EAAC,qCAAqC;UAAAC,QAAA,eAClDxH,OAAA;YAAKuH,SAAS,EAAC,KAAK;YAAAC,QAAA,gBAClBxH,OAAA;cAAKuH,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrDxH,OAAA;gBAAIuH,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,EAAC;cAEhD;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLhI,OAAA;gBACE2H,OAAO,EAAEA,CAAA,KAAMlF,uBAAuB,CAAC,KAAK,CAAE;gBAC9C8E,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,eAE7CxH,OAAA,CAAClB,GAAG;kBAACyI,SAAS,EAAC;gBAAS;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENhI,OAAA;cAAKuH,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBxH,OAAA;gBAAAwH,QAAA,gBACExH,OAAA;kBAAOuH,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRhI,OAAA;kBACEiI,IAAI,EAAC,QAAQ;kBACbhD,KAAK,EAAEvC,eAAe,CAACE,IAAK;kBAC5BuF,QAAQ,EAAGC,CAAC,IAAKzF,kBAAkB,CAACuC,IAAI,KAAK;oBAAC,GAAGA,IAAI;oBAAEtC,IAAI,EAAE6F,QAAQ,CAACL,CAAC,CAACC,MAAM,CAACpD,KAAK,CAAC,IAAI;kBAAC,CAAC,CAAC,CAAE;kBAC9FsC,SAAS,EAAC;gBAAkG;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7G,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENhI,OAAA;gBAAAwH,QAAA,gBACExH,OAAA;kBAAOuH,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRhI,OAAA;kBACEiI,IAAI,EAAC,QAAQ;kBACbhD,KAAK,EAAEvC,eAAe,CAACG,IAAK;kBAC5BsF,QAAQ,EAAGC,CAAC,IAAKzF,kBAAkB,CAACuC,IAAI,KAAK;oBAAC,GAAGA,IAAI;oBAAErC,IAAI,EAAE4F,QAAQ,CAACL,CAAC,CAACC,MAAM,CAACpD,KAAK,CAAC,IAAI;kBAAC,CAAC,CAAC,CAAE;kBAC9FsC,SAAS,EAAC;gBAAkG;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7G,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENhI,OAAA;gBAAAwH,QAAA,gBACExH,OAAA;kBAAOuH,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRhI,OAAA;kBACEiI,IAAI,EAAC,QAAQ;kBACbhD,KAAK,EAAEvC,eAAe,CAACI,MAAO;kBAC9BqF,QAAQ,EAAGC,CAAC,IAAKzF,kBAAkB,CAACuC,IAAI,KAAK;oBAAC,GAAGA,IAAI;oBAAEpC,MAAM,EAAEsF,CAAC,CAACC,MAAM,CAACpD;kBAAK,CAAC,CAAC,CAAE;kBACjFsC,SAAS,EAAC,kGAAkG;kBAC5GW,WAAW,EAAC;gBAAQ;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENhI,OAAA;gBAAAwH,QAAA,gBACExH,OAAA;kBAAOuH,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRhI,OAAA;kBACEiI,IAAI,EAAC,QAAQ;kBACbhD,KAAK,EAAEvC,eAAe,CAACK,QAAS;kBAChCoF,QAAQ,EAAGC,CAAC,IAAKzF,kBAAkB,CAACuC,IAAI,KAAK;oBAAC,GAAGA,IAAI;oBAAEnC,QAAQ,EAAEqF,CAAC,CAACC,MAAM,CAACpD;kBAAK,CAAC,CAAC,CAAE;kBACnFsC,SAAS,EAAC,kGAAkG;kBAC5GW,WAAW,EAAC;gBAAQ;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENhI,OAAA;gBAAAwH,QAAA,gBACExH,OAAA;kBAAOuH,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRhI,OAAA;kBACEiF,KAAK,EAAEvC,eAAe,CAACM,KAAM;kBAC7BmF,QAAQ,EAAGC,CAAC,IAAKzF,kBAAkB,CAACuC,IAAI,KAAK;oBAAC,GAAGA,IAAI;oBAAElC,KAAK,EAAEoF,CAAC,CAACC,MAAM,CAACpD;kBAAK,CAAC,CAAC,CAAE;kBAChFsC,SAAS,EAAC,kGAAkG;kBAC5GyC,IAAI,EAAC,GAAG;kBACR9B,WAAW,EAAC;gBAAgC;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENhI,OAAA;cAAKuH,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9BxH,OAAA;gBACE2H,OAAO,EAAEA,CAAA,KAAMlF,uBAAuB,CAAC,KAAK,CAAE;gBAC9C8E,SAAS,EAAC,qGAAqG;gBAAAC,QAAA,EAChH;cAED;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACThI,OAAA;gBACE2H,OAAO,EAAEA,CAAA,KAAM1B,kBAAkB,CAACxF,gBAAgB,CAAE;gBACpD8G,SAAS,EAAC,wFAAwF;gBAAAC,QAAA,EACnG;cAED;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA5F,iBAAiB,IAAIE,YAAY,iBAChCtC,OAAA;QAAKuH,SAAS,EAAC,gFAAgF;QAAAC,QAAA,eAC7FxH,OAAA;UAAKuH,SAAS,EAAC,qCAAqC;UAAAC,QAAA,eAClDxH,OAAA;YAAKuH,SAAS,EAAC,KAAK;YAAAC,QAAA,gBAClBxH,OAAA;cAAKuH,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrDxH,OAAA;gBAAIuH,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,EAAC;cAEhD;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLhI,OAAA;gBACE2H,OAAO,EAAEA,CAAA,KAAMtF,oBAAoB,CAAC,KAAK,CAAE;gBAC3CkF,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,eAE7CxH,OAAA,CAAClB,GAAG;kBAACyI,SAAS,EAAC;gBAAS;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENhI,OAAA;cAAKuH,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBxH,OAAA;gBAAKuH,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACrCxH,OAAA;kBAAKuH,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,gBACxCxH,OAAA;oBAAKuH,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAAElF,YAAY,CAAC2H;kBAAa;oBAAApC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACpFhI,OAAA;oBAAKuH,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAAgB;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1D,CAAC,eACNhI,OAAA;kBAAKuH,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,gBACzCxH,OAAA;oBAAKuH,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAC/ClF,YAAY,CAAC4H,aAAa,GACzB,IAAIvB,IAAI,CAACrG,YAAY,CAAC4H,aAAa,CAAC,CAACrB,kBAAkB,CAAC,OAAO,CAAC,GAChE;kBAAQ;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEP,CAAC,eACNhI,OAAA;oBAAKuH,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAAa;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAEL1F,YAAY,CAAC6H,eAAe,iBAC3BnK,OAAA;gBAAKuH,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1CxH,OAAA;kBAAIuH,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAC;gBAAqB;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC3EhI,OAAA;kBAAKuH,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,GACnClF,YAAY,CAAC6H,eAAe,CAACrB,WAAW,CAAClG,IAAI,EAAC,kBAAU,EAACN,YAAY,CAAC6H,eAAe,CAACrB,WAAW,CAACjG,IAAI,EAAC,OACxG,EAACP,YAAY,CAAC6H,eAAe,CAACrB,WAAW,CAAChG,MAAM,IAC9C,MAAMR,YAAY,CAAC6H,eAAe,CAACrB,WAAW,CAAChG,MAAM,KAAK;gBAAA;kBAAA+E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEzD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN,eAEDhI,OAAA;gBAAKuH,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,gBACxCxH,OAAA;kBAAIuH,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC9DhI,OAAA;kBAAKuH,SAAS,EAAE,qBACdjF,YAAY,CAAC8H,aAAa,KAAK,WAAW,GAAG,gBAAgB,GAC7D9H,YAAY,CAAC8H,aAAa,KAAK,WAAW,GAAG,cAAc,GAC3D,eAAe,EACd;kBAAA5C,QAAA,gBACDxH,OAAA,CAACvB,YAAY;oBAAC8I,SAAS,EAAC;kBAAc;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EACxC1F,YAAY,CAAC8H,aAAa,KAAK,WAAW,GAAG,gBAAgB,GAC7D9H,YAAY,CAAC8H,aAAa,KAAK,WAAW,GAAG,WAAW,GACxD,QAAQ;gBAAA;kBAAAvC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGAhG,iBAAiB,iBAChBhC,OAAA;QAAKuH,SAAS,EAAC,gFAAgF;QAAAC,QAAA,eAC7FxH,OAAA;UAAKuH,SAAS,EAAC,qCAAqC;UAAAC,QAAA,eAClDxH,OAAA;YAAKuH,SAAS,EAAC,KAAK;YAAAC,QAAA,gBAClBxH,OAAA;cAAKuH,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrDxH,OAAA;gBAAIuH,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,EAAC;cAEhD;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLhI,OAAA;gBACE2H,OAAO,EAAEA,CAAA,KAAM1F,oBAAoB,CAAC,KAAK,CAAE;gBAC3CsF,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,eAE7CxH,OAAA,CAAClB,GAAG;kBAACyI,SAAS,EAAC;gBAAS;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENhI,OAAA;cAAKuH,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBxH,OAAA;gBAAGuH,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,GAC9BtF,2BAA2B,CAACyD,MAAM,EAAC,mCACtC;cAAA;gBAAAkC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,EAEH9F,2BAA2B,CAACyD,MAAM,GAAG,CAAC,iBACrC3F,OAAA;gBAAKuH,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,EACrDtF,2BAA2B,CAACuF,GAAG,CAAC1B,QAAQ,iBACvC/F,OAAA;kBAAuBuH,SAAS,EAAC,0DAA0D;kBAAAC,QAAA,gBACzFxH,OAAA;oBAAMuH,SAAS,EAAC,SAAS;oBAAAC,QAAA,EAAEzB,QAAQ,CAACW;kBAAI;oBAAAmB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAChDhI,OAAA;oBACE2H,OAAO,EAAEA,CAAA,KAAMtB,6BAA6B,CAACN,QAAQ,CAAE;oBACvDwB,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,eAE3CxH,OAAA,CAAClB,GAAG;sBAACyI,SAAS,EAAC;oBAAS;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB,CAAC;gBAAA,GAPDjC,QAAQ,CAAC/B,EAAE;kBAAA6D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAQhB,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CACN,eAEDhI,OAAA;gBACEiI,IAAI,EAAC,MAAM;gBACXC,WAAW,EAAC,kBAAkB;gBAC9BX,SAAS,EAAC,kGAAkG;gBAC5G8C,UAAU,EAAGjC,CAAC,IAAK;kBACjB,IAAIA,CAAC,CAACG,GAAG,KAAK,OAAO,EAAE;oBACrBhC,mBAAmB,CAAC6B,CAAC,CAACC,MAAM,CAACpD,KAAK,CAAC;kBACrC;gBACF;cAAE;gBAAA4C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENhI,OAAA;cAAKuH,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBxH,OAAA;gBACE2H,OAAO,EAAEA,CAAA,KAAM1F,oBAAoB,CAAC,KAAK,CAAE;gBAC3CsF,SAAS,EAAC,qGAAqG;gBAAAC,QAAA,EAChH;cAED;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACThI,OAAA;gBACE2H,OAAO,EAAGS,CAAC,IAAK;kBACd,MAAMkC,KAAK,GAAGlC,CAAC,CAACC,MAAM,CAACkC,aAAa,CAACA,aAAa,CAACC,aAAa,CAAC,OAAO,CAAC;kBACzEjE,mBAAmB,CAAC+D,KAAK,CAACrF,KAAK,CAAC;gBAClC,CAAE;gBACFsC,SAAS,EAAC,4FAA4F;gBAAAC,QAAA,EACvG;cAED;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGAvH,gBAAgB,IAAI,CAAC+B,oBAAoB,iBACxCxC,OAAA;QAAKuH,SAAS,EAAC,gFAAgF;QAAAC,QAAA,eAC7FxH,OAAA;UAAKuH,SAAS,EAAC,mEAAmE;UAAAC,QAAA,eAChFxH,OAAA;YAAKuH,SAAS,EAAC,KAAK;YAAAC,QAAA,gBAClBxH,OAAA;cAAKuH,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrDxH,OAAA;gBAAAwH,QAAA,gBACExH,OAAA;kBAAIuH,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,EAClD/G,gBAAgB,CAACiG;gBAAI;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB,CAAC,eACLhI,OAAA;kBAAKuH,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1CxH,OAAA;oBAAMuH,SAAS,EAAE,8CAA8CnI,kBAAkB,CAACqB,gBAAgB,CAACW,UAAU,CAAC,EAAG;oBAAAoG,QAAA,EAC9G/G,gBAAgB,CAACW;kBAAU;oBAAAyG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB,CAAC,eACPhI,OAAA;oBAAMuH,SAAS,EAAC,eAAe;oBAAAC,QAAA,IAAAnH,qBAAA,GAC5BpB,kBAAkB,CAACwB,gBAAgB,CAACQ,QAAQ,CAAC,cAAAZ,qBAAA,uBAA7CA,qBAAA,CAA+CgJ,IAAI,EAAC,GAAC,GAAA/I,sBAAA,GAACrB,kBAAkB,CAACwB,gBAAgB,CAACQ,QAAQ,CAAC,cAAAX,sBAAA,uBAA7CA,sBAAA,CAA+CoG,IAAI;kBAAA;oBAAAmB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNhI,OAAA;gBACE2H,OAAO,EAAEA,CAAA,KAAMjH,mBAAmB,CAAC,IAAI,CAAE;gBACzC6G,SAAS,EAAC,4CAA4C;gBAAAC,QAAA,EACvD;cAED;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENhI,OAAA;cAAKuH,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBAEpDxH,OAAA;gBAAKuH,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5BxH,OAAA;kBAAKuH,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBACnBxH,OAAA;oBAAIuH,SAAS,EAAC,0CAA0C;oBAAAC,QAAA,EAAC;kBAAW;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACzEhI,OAAA;oBAAGuH,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,EAAE/G,gBAAgB,CAAC+I;kBAAW;oBAAA3B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5E,CAAC,eAENhI,OAAA;kBAAKuH,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBACnBxH,OAAA;oBAAIuH,SAAS,EAAC,0CAA0C;oBAAAC,QAAA,EAAC;kBAAuB;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACrFhI,OAAA;oBAAIuH,SAAS,EAAC,oCAAoC;oBAAAC,QAAA,EAC/C/G,gBAAgB,CAACgK,YAAY,CAAChD,GAAG,CAAC,CAACiD,WAAW,EAAEC,KAAK,kBACpD3K,OAAA;sBAAgBuH,SAAS,EAAC,oCAAoC;sBAAAC,QAAA,EAC3DkD;oBAAW,GADLC,KAAK;sBAAA9C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEV,CACL;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,EAELvH,gBAAgB,CAACmK,IAAI,iBACpB5K,OAAA;kBAAKuH,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBACnBxH,OAAA;oBAAIuH,SAAS,EAAC,0CAA0C;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACtEhI,OAAA;oBAAIuH,SAAS,EAAC,WAAW;oBAAAC,QAAA,EACtB/G,gBAAgB,CAACmK,IAAI,CAACnD,GAAG,CAAC,CAACoD,GAAG,EAAEF,KAAK,kBACpC3K,OAAA;sBAAgBuH,SAAS,EAAC,gCAAgC;sBAAAC,QAAA,gBACxDxH,OAAA;wBAAMuH,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,EAAC;sBAAC;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,EACjD6C,GAAG;oBAAA,GAFGF,KAAK;sBAAA9C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAGV,CACL;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CACN,EAEAvH,gBAAgB,CAACqK,UAAU,iBAC1B9K,OAAA;kBAAKuH,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBACnBxH,OAAA;oBAAIuH,SAAS,EAAC,0CAA0C;oBAAAC,QAAA,EAAC;kBAAU;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACxEhI,OAAA;oBAAIuH,SAAS,EAAC,WAAW;oBAAAC,QAAA,EACtB/G,gBAAgB,CAACqK,UAAU,CAACrD,GAAG,CAAC,CAACsD,SAAS,EAAEJ,KAAK,kBAChD3K,OAAA;sBAAgBuH,SAAS,EAAC,gCAAgC;sBAAAC,QAAA,gBACxDxH,OAAA;wBAAMuH,SAAS,EAAC,0BAA0B;wBAAAC,QAAA,EAAC;sBAAC;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,EAClD+C,SAAS;oBAAA,GAFHJ,KAAK;sBAAA9C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAGV,CACL;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAGNhI,OAAA;gBAAKuH,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5BxH,OAAA;kBAAKuH,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,gBAC7CxH,OAAA;oBAAIuH,SAAS,EAAC,0CAA0C;oBAAAC,QAAA,EAAC;kBAAY;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAE1EhI,OAAA;oBAAKuH,SAAS,EAAC,WAAW;oBAAAC,QAAA,gBACxBxH,OAAA;sBAAKuH,SAAS,EAAC,sBAAsB;sBAAAC,QAAA,gBACnCxH,OAAA;wBAAMuH,SAAS,EAAC,eAAe;wBAAAC,QAAA,EAAC;sBAAK;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAC5ChI,OAAA;wBAAMuH,SAAS,EAAC,aAAa;wBAAAC,QAAA,GAAE/G,gBAAgB,CAACsC,QAAQ,EAAC,MAAI;sBAAA;wBAAA8E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjE,CAAC,eACNhI,OAAA;sBAAKuH,SAAS,EAAC,sBAAsB;sBAAAC,QAAA,gBACnCxH,OAAA;wBAAMuH,SAAS,EAAC,eAAe;wBAAAC,QAAA,EAAC;sBAAQ;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAC/ChI,OAAA;wBAAMuH,SAAS,EAAC,aAAa;wBAAAC,QAAA,GAAE/G,gBAAgB,CAACoJ,QAAQ,EAAC,MAAI;sBAAA;wBAAAhC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjE,CAAC,eACNhI,OAAA;sBAAKuH,SAAS,EAAC,sBAAsB;sBAAAC,QAAA,gBACnCxH,OAAA;wBAAMuH,SAAS,EAAC,eAAe;wBAAAC,QAAA,EAAC;sBAAU;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACjDhI,OAAA;wBAAMuH,SAAS,EAAC,aAAa;wBAAAC,QAAA,GAAAjH,qBAAA,GAAEpB,cAAc,CAACsB,gBAAgB,CAACU,SAAS,CAAC,cAAAZ,qBAAA,uBAA1CA,qBAAA,CAA4CmG;sBAAI;wBAAAmB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENhI,OAAA;kBAAKuH,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,gBAC7CxH,OAAA;oBAAIuH,SAAS,EAAC,0CAA0C;oBAAAC,QAAA,EAAC;kBAAmB;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACjFhI,OAAA;oBAAKuH,SAAS,EAAC,sBAAsB;oBAAAC,QAAA,EAClC/G,gBAAgB,CAACvB,YAAY,CAACuI,GAAG,CAACkC,EAAE;sBAAA,IAAAqB,iBAAA,EAAAC,iBAAA;sBAAA,oBACnCjL,OAAA;wBAAeuH,SAAS,EAAC,0DAA0D;wBAAAC,QAAA,IAAAwD,iBAAA,GAChF9L,YAAY,CAACyK,EAAE,CAAC,cAAAqB,iBAAA,uBAAhBA,iBAAA,CAAkB3B,IAAI,EAAC,GAAC,GAAA4B,iBAAA,GAAC/L,YAAY,CAACyK,EAAE,CAAC,cAAAsB,iBAAA,uBAAhBA,iBAAA,CAAkBvE,IAAI;sBAAA,GADvCiD,EAAE;wBAAA9B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAEP,CAAC;oBAAA,CACR;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENhI,OAAA;kBAAKuH,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBACxBxH,OAAA;oBACE2H,OAAO,EAAEA,CAAA,KAAMnI,cAAc,CAACiB,gBAAgB,CAACuD,EAAE,CAAE;oBACnDuD,SAAS,EAAE,8FACT7F,iBAAiB,CAACmD,IAAI,CAACuE,GAAG,IAAIA,GAAG,CAACpF,EAAE,KAAKvD,gBAAgB,CAACuD,EAAE,CAAC,GACzD,0CAA0C,GAC1C,6CAA6C,EAChD;oBAAAwD,QAAA,gBAEHxH,OAAA,CAAC3B,OAAO;sBAACkJ,SAAS,EAAC;oBAAc;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EACnCtG,iBAAiB,CAACmD,IAAI,CAACuE,GAAG,IAAIA,GAAG,CAACpF,EAAE,KAAKvD,gBAAgB,CAACuD,EAAE,CAAC,GAAG,qBAAqB,GAAG,qBAAqB;kBAAA;oBAAA6D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxG,CAAC,eAEThI,OAAA;oBAAQuH,SAAS,EAAC,qIAAqI;oBAAAC,QAAA,gBACrJxH,OAAA,CAACjC,MAAM;sBAACwJ,SAAS,EAAC;oBAAc;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,wBAErC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA3E,eAAe,IAAIE,cAAc,iBAChCvD,OAAA;QAAKuH,SAAS,EAAC,4EAA4E;QAAAC,QAAA,eACzFxH,OAAA;UAAKuH,SAAS,EAAC,8CAA8C;UAAAC,QAAA,gBAC3DxH,OAAA;YAAIuH,SAAS,EAAC,0CAA0C;YAAAC,QAAA,GAAC,qBACpC,EAACjE,cAAc,CAACmD,IAAI;UAAA;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC,eAELhI,OAAA;YAAKuH,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBxH,OAAA;cAAAwH,QAAA,gBACExH,OAAA;gBAAOuH,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRhI,OAAA;gBAAKuH,SAAS,EAAC,4CAA4C;gBAAAC,QAAA,EACxDJ,WAAW,CAAC,CAAC,EAAE,IAAI,EAAGR,MAAM,IAAK;kBAChC,MAAMC,OAAO,GAAG1D,gBAAgB,CAACI,cAAc,CAACS,EAAE,CAAC,IAAI,EAAE;kBACzD2C,YAAY,CAACpD,cAAc,CAACS,EAAE,EAAE4C,MAAM,EAAEC,OAAO,CAAC;gBAClD,CAAC;cAAC;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENhI,OAAA;cAAAwH,QAAA,gBACExH,OAAA;gBAAOuH,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRhI,OAAA;gBACEkL,YAAY,EAAE/H,gBAAgB,CAACI,cAAc,CAACS,EAAE,CAAC,IAAI,EAAG;gBACxDmE,QAAQ,EAAGC,CAAC,IAAK;kBACf,MAAMrB,eAAe,GAAG;oBAAE,GAAG5D,gBAAgB;oBAAE,CAACI,cAAc,CAACS,EAAE,GAAGoE,CAAC,CAACC,MAAM,CAACpD;kBAAM,CAAC;kBACpF7B,mBAAmB,CAAC2D,eAAe,CAAC;gBACtC,CAAE;gBACFQ,SAAS,EAAC,wGAAwG;gBAClHyC,IAAI,EAAC,GAAG;gBACR9B,WAAW,EAAC;cAAgD;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENhI,OAAA;YAAKuH,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAClCxH,OAAA;cACE2H,OAAO,EAAEA,CAAA,KAAM;gBACb,MAAMf,MAAM,GAAG3D,eAAe,CAACM,cAAc,CAACS,EAAE,CAAC,IAAI,CAAC;gBACtD,MAAM6C,OAAO,GAAG1D,gBAAgB,CAACI,cAAc,CAACS,EAAE,CAAC,IAAI,EAAE;gBACzD,IAAI4C,MAAM,GAAG,CAAC,EAAE;kBACdD,YAAY,CAACpD,cAAc,CAACS,EAAE,EAAE4C,MAAM,EAAEC,OAAO,CAAC;gBAClD;cACF,CAAE;cACFU,SAAS,EAAC,wFAAwF;cAAAC,QAAA,EACnG;YAED;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACThI,OAAA;cACE2H,OAAO,EAAEA,CAAA,KAAM;gBACbrE,kBAAkB,CAAC,KAAK,CAAC;gBACzBE,iBAAiB,CAAC,IAAI,CAAC;cACzB,CAAE;cACF+D,SAAS,EAAC,2FAA2F;cAAAC,QAAA,EACtG;YAED;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC5H,EAAA,CA1kCID,SAAS;EAAA,QACIpB,OAAO;AAAA;AAAAoM,EAAA,GADpBhL,SAAS;AA4kCf,eAAeA,SAAS;AAAC,IAAAgL,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}