{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projet fitness\\\\DATA\\\\fitness-ui\\\\src\\\\pages\\\\Performance.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { FiTarget, FiTrendingUp, FiClock, FiActivity, FiZap, FiHeart, FiCalendar, FiSave, FiTrash2, FiAlertCircle } from 'react-icons/fi';\nimport { useAuth } from '../contexts/AuthContext';\nimport { calculateRacePredictions, estimateVO2Max, calculateTrainingPaces, generateTrainingRecommendations, timeToSeconds, DISTANCES } from '../utils/performancePrediction';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Performance = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const [selectedDistance, setSelectedDistance] = useState('5K');\n  const [timeInput, setTimeInput] = useState('25:00');\n  const [predictions, setPredictions] = useState(null);\n  const [trainingPaces, setTrainingPaces] = useState(null);\n  const [recommendations, setRecommendations] = useState([]);\n  const [vo2Max, setVO2Max] = useState(null);\n  const [performanceHistory, setPerformanceHistory] = useState([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [showSaveModal, setShowSaveModal] = useState(false);\n  const calculatePredictions = React.useCallback(() => {\n    const timeInSeconds = timeToSeconds(timeInput);\n    const distance = DISTANCES[selectedDistance];\n    if (timeInSeconds > 0 && distance) {\n      // Calculate race predictions\n      const racePredictions = calculateRacePredictions(timeInSeconds, distance);\n      setPredictions(racePredictions);\n\n      // Calculate VO2 Max\n      const estimatedVO2Max = estimateVO2Max(timeInSeconds, distance);\n      setVO2Max(estimatedVO2Max);\n\n      // Calculate training paces\n      const paces = calculateTrainingPaces(estimatedVO2Max, calculateAge(user === null || user === void 0 ? void 0 : user.dateOfBirth));\n      setTrainingPaces(paces);\n\n      // Generate recommendations\n      if (user) {\n        const recs = generateTrainingRecommendations(user, {\n          distance,\n          time: timeInSeconds\n        });\n        setRecommendations(recs);\n      }\n    }\n  }, [timeInput, selectedDistance, user]);\n  useEffect(() => {\n    if (timeInput && selectedDistance) {\n      calculatePredictions();\n    }\n  }, [timeInput, selectedDistance, calculatePredictions]);\n  const calculateAge = dateOfBirth => {\n    if (!dateOfBirth) return 30;\n    const today = new Date();\n    const birthDate = new Date(dateOfBirth);\n    let age = today.getFullYear() - birthDate.getFullYear();\n    const monthDiff = today.getMonth() - birthDate.getMonth();\n    if (monthDiff < 0 || monthDiff === 0 && today.getDate() < birthDate.getDate()) {\n      age--;\n    }\n    return age;\n  };\n\n  // Charger les données de performance depuis localStorage\n  useEffect(() => {\n    try {\n      const savedHistory = JSON.parse(localStorage.getItem('performanceHistory') || '[]');\n      setPerformanceHistory(savedHistory);\n    } catch (error) {\n      console.error('Erreur lors du chargement de l\\'historique:', error);\n      setError('Erreur lors du chargement des données sauvegardées.');\n    }\n  }, []);\n\n  // Sauvegarder une performance\n  const savePerformance = () => {\n    if (!predictions || !vo2Max) {\n      setError('Veuillez d\\'abord calculer vos prédictions.');\n      return;\n    }\n    const performanceEntry = {\n      id: Date.now(),\n      date: new Date().toISOString(),\n      distance: selectedDistance,\n      time: timeInput,\n      timeInSeconds: timeToSeconds(timeInput),\n      vo2Max: vo2Max,\n      predictions: predictions,\n      trainingPaces: trainingPaces,\n      user: (user === null || user === void 0 ? void 0 : user.firstName) || 'Utilisateur'\n    };\n    try {\n      const updatedHistory = [performanceEntry, ...performanceHistory];\n      setPerformanceHistory(updatedHistory);\n      localStorage.setItem('performanceHistory', JSON.stringify(updatedHistory));\n      setShowSaveModal(false);\n    } catch (error) {\n      console.error('Erreur lors de la sauvegarde:', error);\n      setError('Erreur lors de la sauvegarde des données.');\n    }\n  };\n\n  // Supprimer une performance de l'historique\n  const deletePerformance = performanceId => {\n    try {\n      const updatedHistory = performanceHistory.filter(p => p.id !== performanceId);\n      setPerformanceHistory(updatedHistory);\n      localStorage.setItem('performanceHistory', JSON.stringify(updatedHistory));\n    } catch (error) {\n      console.error('Erreur lors de la suppression:', error);\n      setError('Erreur lors de la suppression.');\n    }\n  };\n  const formatDistance = distance => {\n    const distanceNames = {\n      '5K': '5 km',\n      '10K': '10 km',\n      'HALF_MARATHON': 'Semi-marathon',\n      'MARATHON': 'Marathon'\n    };\n    return distanceNames[distance] || distance;\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50 py-8\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold text-gray-900 mb-4\",\n          children: \"Pr\\xE9diction de Performance\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg text-gray-600 max-w-3xl mx-auto\",\n          children: \"D\\xE9couvrez vos temps de course pr\\xE9dits, vos allures d'entra\\xEEnement personnalis\\xE9es et recevez des recommandations adapt\\xE9es \\xE0 vos objectifs.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 11\n        }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4 max-w-md mx-auto bg-red-50 border border-red-200 rounded-lg p-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(FiAlertCircle, {\n              className: \"h-5 w-5 text-red-600 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-red-800\",\n              children: error\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-lg p-6 mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-semibold text-gray-900 mb-6 flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(FiTarget, {\n            className: \"mr-2 text-blue-600\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 13\n          }, this), \"Entrez votre performance actuelle\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"distance\",\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Distance\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              id: \"distance\",\n              value: selectedDistance,\n              onChange: e => setSelectedDistance(e.target.value),\n              className: \"block w-full px-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n              children: Object.keys(DISTANCES).map(distance => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: distance,\n                children: formatDistance(distance)\n              }, distance, false, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"time\",\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Temps (MM:SS ou HH:MM:SS)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"time\",\n              value: timeInput,\n              onChange: e => setTimeInput(e.target.value),\n              placeholder: \"25:00\",\n              className: \"block w-full px-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 9\n      }, this), predictions && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:col-span-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg p-6 mb-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-xl font-semibold text-gray-900 mb-6 flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(FiClock, {\n                className: \"mr-2 text-green-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 217,\n                columnNumber: 19\n              }, this), \"Pr\\xE9dictions de temps de course\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n              children: Object.entries(predictions).map(([distance, data]) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-gray-50 rounded-lg p-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-between items-center mb-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"font-semibold text-gray-900\",\n                    children: formatDistance(distance)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 225,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(FiActivity, {\n                    className: \"text-blue-600\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 226,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 224,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-2xl font-bold text-blue-600 mb-1\",\n                  children: data.timeFormatted\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 228,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-gray-600\",\n                  children: [\"Allure: \", data.paceFormatted, \"/km\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 231,\n                  columnNumber: 23\n                }, this)]\n              }, distance, true, {\n                fileName: _jsxFileName,\n                lineNumber: 223,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 15\n          }, this), trainingPaces && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-xl font-semibold text-gray-900 mb-6 flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(FiZap, {\n                className: \"mr-2 text-purple-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 21\n              }, this), \"Allures d'entra\\xEEnement\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: Object.entries(trainingPaces).map(([type, data]) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"border border-gray-200 rounded-lg p-4\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-between items-start mb-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"font-semibold text-gray-900 capitalize\",\n                      children: type === 'easy' ? 'Facile' : type === 'marathon' ? 'Marathon' : type === 'threshold' ? 'Seuil' : type === 'interval' ? 'Intervalles' : type === 'repetition' ? 'Répétitions' : type\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 252,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-600\",\n                      children: data.description\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 259,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 251,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-right\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-lg font-bold text-purple-600\",\n                      children: [data.paceFormatted, \"/km\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 262,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm text-gray-500\",\n                      children: [data.hrZone.min, \"-\", data.hrZone.max, \"% FCM\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 265,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 261,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 250,\n                  columnNumber: 25\n                }, this)\n              }, type, false, {\n                fileName: _jsxFileName,\n                lineNumber: 249,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-8\",\n          children: [vo2Max && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-4 flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(FiHeart, {\n                className: \"mr-2 text-red-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 283,\n                columnNumber: 21\n              }, this), \"VO2 Max estim\\xE9\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-3xl font-bold text-red-600 mb-2\",\n                children: vo2Max.toFixed(1)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 287,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-600\",\n                children: \"ml/kg/min\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 290,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-4 text-sm text-gray-500\",\n                children: vo2Max >= 60 ? 'Excellent' : vo2Max >= 50 ? 'Très bon' : vo2Max >= 40 ? 'Bon' : vo2Max >= 35 ? 'Moyen' : 'À améliorer'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 293,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 17\n          }, this), recommendations.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-4 flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(FiTrendingUp, {\n                className: \"mr-2 text-orange-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 21\n              }, this), \"Recommandations\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: recommendations.map((rec, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"border-l-4 border-orange-500 pl-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"font-medium text-gray-900 mb-1\",\n                  children: rec.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 314,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-600 mb-2\",\n                  children: rec.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 317,\n                  columnNumber: 25\n                }, this), rec.frequency && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs text-gray-500\",\n                  children: [\"Fr\\xE9quence: \", rec.frequency]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 321,\n                  columnNumber: 27\n                }, this), rec.duration && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs text-gray-500\",\n                  children: [\"Dur\\xE9e: \", rec.duration]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 326,\n                  columnNumber: 27\n                }, this), rec.intensity && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs text-gray-500\",\n                  children: [\"Intensit\\xE9: \", rec.intensity]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 331,\n                  columnNumber: 27\n                }, this), rec.details && /*#__PURE__*/_jsxDEV(\"ul\", {\n                  className: \"text-xs text-gray-500 mt-2 ml-4\",\n                  children: rec.details.map((detail, i) => /*#__PURE__*/_jsxDEV(\"li\", {\n                    className: \"list-disc\",\n                    children: detail\n                  }, i, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 338,\n                    columnNumber: 31\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 336,\n                  columnNumber: 27\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 313,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 311,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-4 flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(FiCalendar, {\n                className: \"mr-2 text-indigo-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 351,\n                columnNumber: 19\n              }, this), \"Conseils d'entra\\xEEnement\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 350,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3 text-sm text-gray-600\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-2 h-2 bg-indigo-600 rounded-full mt-2 mr-3 flex-shrink-0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 357,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"Respectez le principe de progressivit\\xE9 : augmentez votre volume d'entra\\xEEnement de 10% maximum par semaine.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 358,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 356,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-2 h-2 bg-indigo-600 rounded-full mt-2 mr-3 flex-shrink-0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 361,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"80% de vos entra\\xEEnements doivent \\xEAtre \\xE0 allure facile pour d\\xE9velopper votre endurance de base.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 362,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 360,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-2 h-2 bg-indigo-600 rounded-full mt-2 mr-3 flex-shrink-0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 365,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"Int\\xE9grez une sortie longue par semaine pour am\\xE9liorer votre endurance.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 366,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 364,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-2 h-2 bg-indigo-600 rounded-full mt-2 mr-3 flex-shrink-0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 369,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"Planifiez des semaines de r\\xE9cup\\xE9ration toutes les 3-4 semaines.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 370,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 368,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 355,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 349,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 278,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 212,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 147,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 146,\n    columnNumber: 5\n  }, this);\n};\n_s(Performance, \"gjnUFLUAvQhlznHPR1jjfjODOTk=\", false, function () {\n  return [useAuth];\n});\n_c = Performance;\nexport default Performance;\nvar _c;\n$RefreshReg$(_c, \"Performance\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "<PERSON><PERSON><PERSON><PERSON>", "FiTrendingUp", "<PERSON><PERSON><PERSON>", "FiActivity", "FiZap", "<PERSON><PERSON><PERSON><PERSON>", "FiCalendar", "FiSave", "FiTrash2", "FiAlertCircle", "useAuth", "calculateRacePredictions", "estimateVO2Max", "calculateTrainingPaces", "generateTrainingRecommendations", "timeToSeconds", "DISTANCES", "jsxDEV", "_jsxDEV", "Performance", "_s", "user", "selectedDistance", "setSelectedDistance", "timeInput", "setTimeInput", "predictions", "setPredictions", "trainingPaces", "setTrainingPaces", "recommendations", "setRecommendations", "vo2Max", "setVO2Max", "performanceHistory", "setPerformanceHistory", "isLoading", "setIsLoading", "error", "setError", "showSaveModal", "setShowSaveModal", "calculatePredictions", "useCallback", "timeInSeconds", "distance", "racePredictions", "estimatedVO2Max", "paces", "calculateAge", "dateOfBirth", "recs", "time", "today", "Date", "birthDate", "age", "getFullYear", "monthDiff", "getMonth", "getDate", "savedHistory", "JSON", "parse", "localStorage", "getItem", "console", "savePerformance", "performanceEntry", "id", "now", "date", "toISOString", "firstName", "updatedHistory", "setItem", "stringify", "deletePerformance", "performanceId", "filter", "p", "formatDistance", "distanceNames", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "htmlFor", "value", "onChange", "e", "target", "Object", "keys", "map", "type", "placeholder", "entries", "data", "timeFormatted", "paceFormatted", "description", "hrZone", "min", "max", "toFixed", "length", "rec", "index", "title", "frequency", "duration", "intensity", "details", "detail", "i", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/projet fitness/DATA/fitness-ui/src/pages/Performance.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  FiTarget,\n  FiTrendingUp,\n  FiClock,\n  FiActivity,\n  FiZap,\n  FiHeart,\n  FiCalendar,\n  FiSave,\n  FiTrash2,\n  FiAlertCircle\n} from 'react-icons/fi';\nimport { useAuth } from '../contexts/AuthContext';\nimport {\n  calculateRacePredictions,\n  estimateVO2Max,\n  calculateTrainingPaces,\n  generateTrainingRecommendations,\n  timeToSeconds,\n  DISTANCES\n} from '../utils/performancePrediction';\n\nconst Performance = () => {\n  const { user } = useAuth();\n  const [selectedDistance, setSelectedDistance] = useState('5K');\n  const [timeInput, setTimeInput] = useState('25:00');\n  const [predictions, setPredictions] = useState(null);\n  const [trainingPaces, setTrainingPaces] = useState(null);\n  const [recommendations, setRecommendations] = useState([]);\n  const [vo2Max, setVO2Max] = useState(null);\n  const [performanceHistory, setPerformanceHistory] = useState([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [showSaveModal, setShowSaveModal] = useState(false);\n\n  const calculatePredictions = React.useCallback(() => {\n    const timeInSeconds = timeToSeconds(timeInput);\n    const distance = DISTANCES[selectedDistance];\n\n    if (timeInSeconds > 0 && distance) {\n      // Calculate race predictions\n      const racePredictions = calculateRacePredictions(timeInSeconds, distance);\n      setPredictions(racePredictions);\n\n      // Calculate VO2 Max\n      const estimatedVO2Max = estimateVO2Max(timeInSeconds, distance);\n      setVO2Max(estimatedVO2Max);\n\n      // Calculate training paces\n      const paces = calculateTrainingPaces(estimatedVO2Max, calculateAge(user?.dateOfBirth));\n      setTrainingPaces(paces);\n\n      // Generate recommendations\n      if (user) {\n        const recs = generateTrainingRecommendations(user, { distance, time: timeInSeconds });\n        setRecommendations(recs);\n      }\n    }\n  }, [timeInput, selectedDistance, user]);\n\n  useEffect(() => {\n    if (timeInput && selectedDistance) {\n      calculatePredictions();\n    }\n  }, [timeInput, selectedDistance, calculatePredictions]);\n\n\n\n  const calculateAge = (dateOfBirth) => {\n    if (!dateOfBirth) return 30;\n    const today = new Date();\n    const birthDate = new Date(dateOfBirth);\n    let age = today.getFullYear() - birthDate.getFullYear();\n    const monthDiff = today.getMonth() - birthDate.getMonth();\n    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {\n      age--;\n    }\n    return age;\n  };\n\n  // Charger les données de performance depuis localStorage\n  useEffect(() => {\n    try {\n      const savedHistory = JSON.parse(localStorage.getItem('performanceHistory') || '[]');\n      setPerformanceHistory(savedHistory);\n    } catch (error) {\n      console.error('Erreur lors du chargement de l\\'historique:', error);\n      setError('Erreur lors du chargement des données sauvegardées.');\n    }\n  }, []);\n\n  // Sauvegarder une performance\n  const savePerformance = () => {\n    if (!predictions || !vo2Max) {\n      setError('Veuillez d\\'abord calculer vos prédictions.');\n      return;\n    }\n\n    const performanceEntry = {\n      id: Date.now(),\n      date: new Date().toISOString(),\n      distance: selectedDistance,\n      time: timeInput,\n      timeInSeconds: timeToSeconds(timeInput),\n      vo2Max: vo2Max,\n      predictions: predictions,\n      trainingPaces: trainingPaces,\n      user: user?.firstName || 'Utilisateur'\n    };\n\n    try {\n      const updatedHistory = [performanceEntry, ...performanceHistory];\n      setPerformanceHistory(updatedHistory);\n      localStorage.setItem('performanceHistory', JSON.stringify(updatedHistory));\n      setShowSaveModal(false);\n    } catch (error) {\n      console.error('Erreur lors de la sauvegarde:', error);\n      setError('Erreur lors de la sauvegarde des données.');\n    }\n  };\n\n  // Supprimer une performance de l'historique\n  const deletePerformance = (performanceId) => {\n    try {\n      const updatedHistory = performanceHistory.filter(p => p.id !== performanceId);\n      setPerformanceHistory(updatedHistory);\n      localStorage.setItem('performanceHistory', JSON.stringify(updatedHistory));\n    } catch (error) {\n      console.error('Erreur lors de la suppression:', error);\n      setError('Erreur lors de la suppression.');\n    }\n  };\n\n  const formatDistance = (distance) => {\n    const distanceNames = {\n      '5K': '5 km',\n      '10K': '10 km',\n      'HALF_MARATHON': 'Semi-marathon',\n      'MARATHON': 'Marathon'\n    };\n    return distanceNames[distance] || distance;\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 py-8\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Header */}\n        <div className=\"text-center mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-4\">\n            Prédiction de Performance\n          </h1>\n          <p className=\"text-lg text-gray-600 max-w-3xl mx-auto\">\n            Découvrez vos temps de course prédits, vos allures d'entraînement personnalisées\n            et recevez des recommandations adaptées à vos objectifs.\n          </p>\n\n          {/* Messages d'erreur */}\n          {error && (\n            <div className=\"mt-4 max-w-md mx-auto bg-red-50 border border-red-200 rounded-lg p-4\">\n              <div className=\"flex items-center\">\n                <FiAlertCircle className=\"h-5 w-5 text-red-600 mr-2\" />\n                <p className=\"text-sm text-red-800\">{error}</p>\n              </div>\n            </div>\n          )}\n        </div>\n\n        {/* Input Section */}\n        <div className=\"bg-white rounded-lg shadow-lg p-6 mb-8\">\n          <h2 className=\"text-xl font-semibold text-gray-900 mb-6 flex items-center\">\n            <FiTarget className=\"mr-2 text-blue-600\" />\n            Entrez votre performance actuelle\n          </h2>\n          \n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            <div>\n              <label htmlFor=\"distance\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Distance\n              </label>\n              <select\n                id=\"distance\"\n                value={selectedDistance}\n                onChange={(e) => setSelectedDistance(e.target.value)}\n                className=\"block w-full px-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              >\n                {Object.keys(DISTANCES).map(distance => (\n                  <option key={distance} value={distance}>\n                    {formatDistance(distance)}\n                  </option>\n                ))}\n              </select>\n            </div>\n            \n            <div>\n              <label htmlFor=\"time\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Temps (MM:SS ou HH:MM:SS)\n              </label>\n              <input\n                type=\"text\"\n                id=\"time\"\n                value={timeInput}\n                onChange={(e) => setTimeInput(e.target.value)}\n                placeholder=\"25:00\"\n                className=\"block w-full px-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              />\n            </div>\n          </div>\n        </div>\n\n        {predictions && (\n          <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n            {/* Race Predictions */}\n            <div className=\"lg:col-span-2\">\n              <div className=\"bg-white rounded-lg shadow-lg p-6 mb-8\">\n                <h2 className=\"text-xl font-semibold text-gray-900 mb-6 flex items-center\">\n                  <FiClock className=\"mr-2 text-green-600\" />\n                  Prédictions de temps de course\n                </h2>\n                \n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  {Object.entries(predictions).map(([distance, data]) => (\n                    <div key={distance} className=\"bg-gray-50 rounded-lg p-4\">\n                      <div className=\"flex justify-between items-center mb-2\">\n                        <h3 className=\"font-semibold text-gray-900\">{formatDistance(distance)}</h3>\n                        <FiActivity className=\"text-blue-600\" />\n                      </div>\n                      <div className=\"text-2xl font-bold text-blue-600 mb-1\">\n                        {data.timeFormatted}\n                      </div>\n                      <div className=\"text-sm text-gray-600\">\n                        Allure: {data.paceFormatted}/km\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n\n              {/* Training Paces */}\n              {trainingPaces && (\n                <div className=\"bg-white rounded-lg shadow-lg p-6\">\n                  <h2 className=\"text-xl font-semibold text-gray-900 mb-6 flex items-center\">\n                    <FiZap className=\"mr-2 text-purple-600\" />\n                    Allures d'entraînement\n                  </h2>\n                  \n                  <div className=\"space-y-4\">\n                    {Object.entries(trainingPaces).map(([type, data]) => (\n                      <div key={type} className=\"border border-gray-200 rounded-lg p-4\">\n                        <div className=\"flex justify-between items-start mb-2\">\n                          <div>\n                            <h3 className=\"font-semibold text-gray-900 capitalize\">\n                              {type === 'easy' ? 'Facile' : \n                               type === 'marathon' ? 'Marathon' :\n                               type === 'threshold' ? 'Seuil' :\n                               type === 'interval' ? 'Intervalles' :\n                               type === 'repetition' ? 'Répétitions' : type}\n                            </h3>\n                            <p className=\"text-sm text-gray-600\">{data.description}</p>\n                          </div>\n                          <div className=\"text-right\">\n                            <div className=\"text-lg font-bold text-purple-600\">\n                              {data.paceFormatted}/km\n                            </div>\n                            <div className=\"text-sm text-gray-500\">\n                              {data.hrZone.min}-{data.hrZone.max}% FCM\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              )}\n            </div>\n\n            {/* Sidebar */}\n            <div className=\"space-y-8\">\n              {/* VO2 Max */}\n              {vo2Max && (\n                <div className=\"bg-white rounded-lg shadow-lg p-6\">\n                  <h3 className=\"text-lg font-semibold text-gray-900 mb-4 flex items-center\">\n                    <FiHeart className=\"mr-2 text-red-600\" />\n                    VO2 Max estimé\n                  </h3>\n                  <div className=\"text-center\">\n                    <div className=\"text-3xl font-bold text-red-600 mb-2\">\n                      {vo2Max.toFixed(1)}\n                    </div>\n                    <div className=\"text-sm text-gray-600\">\n                      ml/kg/min\n                    </div>\n                    <div className=\"mt-4 text-sm text-gray-500\">\n                      {vo2Max >= 60 ? 'Excellent' :\n                       vo2Max >= 50 ? 'Très bon' :\n                       vo2Max >= 40 ? 'Bon' :\n                       vo2Max >= 35 ? 'Moyen' : 'À améliorer'}\n                    </div>\n                  </div>\n                </div>\n              )}\n\n              {/* Training Recommendations */}\n              {recommendations.length > 0 && (\n                <div className=\"bg-white rounded-lg shadow-lg p-6\">\n                  <h3 className=\"text-lg font-semibold text-gray-900 mb-4 flex items-center\">\n                    <FiTrendingUp className=\"mr-2 text-orange-600\" />\n                    Recommandations\n                  </h3>\n                  \n                  <div className=\"space-y-4\">\n                    {recommendations.map((rec, index) => (\n                      <div key={index} className=\"border-l-4 border-orange-500 pl-4\">\n                        <h4 className=\"font-medium text-gray-900 mb-1\">\n                          {rec.title}\n                        </h4>\n                        <p className=\"text-sm text-gray-600 mb-2\">\n                          {rec.description}\n                        </p>\n                        {rec.frequency && (\n                          <div className=\"text-xs text-gray-500\">\n                            Fréquence: {rec.frequency}\n                          </div>\n                        )}\n                        {rec.duration && (\n                          <div className=\"text-xs text-gray-500\">\n                            Durée: {rec.duration}\n                          </div>\n                        )}\n                        {rec.intensity && (\n                          <div className=\"text-xs text-gray-500\">\n                            Intensité: {rec.intensity}\n                          </div>\n                        )}\n                        {rec.details && (\n                          <ul className=\"text-xs text-gray-500 mt-2 ml-4\">\n                            {rec.details.map((detail, i) => (\n                              <li key={i} className=\"list-disc\">{detail}</li>\n                            ))}\n                          </ul>\n                        )}\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              )}\n\n              {/* Performance Tips */}\n              <div className=\"bg-white rounded-lg shadow-lg p-6\">\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-4 flex items-center\">\n                  <FiCalendar className=\"mr-2 text-indigo-600\" />\n                  Conseils d'entraînement\n                </h3>\n                \n                <div className=\"space-y-3 text-sm text-gray-600\">\n                  <div className=\"flex items-start\">\n                    <div className=\"w-2 h-2 bg-indigo-600 rounded-full mt-2 mr-3 flex-shrink-0\"></div>\n                    <p>Respectez le principe de progressivité : augmentez votre volume d'entraînement de 10% maximum par semaine.</p>\n                  </div>\n                  <div className=\"flex items-start\">\n                    <div className=\"w-2 h-2 bg-indigo-600 rounded-full mt-2 mr-3 flex-shrink-0\"></div>\n                    <p>80% de vos entraînements doivent être à allure facile pour développer votre endurance de base.</p>\n                  </div>\n                  <div className=\"flex items-start\">\n                    <div className=\"w-2 h-2 bg-indigo-600 rounded-full mt-2 mr-3 flex-shrink-0\"></div>\n                    <p>Intégrez une sortie longue par semaine pour améliorer votre endurance.</p>\n                  </div>\n                  <div className=\"flex items-start\">\n                    <div className=\"w-2 h-2 bg-indigo-600 rounded-full mt-2 mr-3 flex-shrink-0\"></div>\n                    <p>Planifiez des semaines de récupération toutes les 3-4 semaines.</p>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default Performance;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,QAAQ,EACRC,YAAY,EACZC,OAAO,EACPC,UAAU,EACVC,KAAK,EACLC,OAAO,EACPC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,aAAa,QACR,gBAAgB;AACvB,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SACEC,wBAAwB,EACxBC,cAAc,EACdC,sBAAsB,EACtBC,+BAA+B,EAC/BC,aAAa,EACbC,SAAS,QACJ,gCAAgC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAM;IAAEC;EAAK,CAAC,GAAGX,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACY,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAAC0B,SAAS,EAAEC,YAAY,CAAC,GAAG3B,QAAQ,CAAC,OAAO,CAAC;EACnD,MAAM,CAAC4B,WAAW,EAAEC,cAAc,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAAC8B,aAAa,EAAEC,gBAAgB,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACgC,eAAe,EAAEC,kBAAkB,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACkC,MAAM,EAAEC,SAAS,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAACoC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM,CAACsC,SAAS,EAAEC,YAAY,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACwC,KAAK,EAAEC,QAAQ,CAAC,GAAGzC,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC0C,aAAa,EAAEC,gBAAgB,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EAEzD,MAAM4C,oBAAoB,GAAG7C,KAAK,CAAC8C,WAAW,CAAC,MAAM;IACnD,MAAMC,aAAa,GAAG7B,aAAa,CAACS,SAAS,CAAC;IAC9C,MAAMqB,QAAQ,GAAG7B,SAAS,CAACM,gBAAgB,CAAC;IAE5C,IAAIsB,aAAa,GAAG,CAAC,IAAIC,QAAQ,EAAE;MACjC;MACA,MAAMC,eAAe,GAAGnC,wBAAwB,CAACiC,aAAa,EAAEC,QAAQ,CAAC;MACzElB,cAAc,CAACmB,eAAe,CAAC;;MAE/B;MACA,MAAMC,eAAe,GAAGnC,cAAc,CAACgC,aAAa,EAAEC,QAAQ,CAAC;MAC/DZ,SAAS,CAACc,eAAe,CAAC;;MAE1B;MACA,MAAMC,KAAK,GAAGnC,sBAAsB,CAACkC,eAAe,EAAEE,YAAY,CAAC5B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6B,WAAW,CAAC,CAAC;MACtFrB,gBAAgB,CAACmB,KAAK,CAAC;;MAEvB;MACA,IAAI3B,IAAI,EAAE;QACR,MAAM8B,IAAI,GAAGrC,+BAA+B,CAACO,IAAI,EAAE;UAAEwB,QAAQ;UAAEO,IAAI,EAAER;QAAc,CAAC,CAAC;QACrFb,kBAAkB,CAACoB,IAAI,CAAC;MAC1B;IACF;EACF,CAAC,EAAE,CAAC3B,SAAS,EAAEF,gBAAgB,EAAED,IAAI,CAAC,CAAC;EAEvCtB,SAAS,CAAC,MAAM;IACd,IAAIyB,SAAS,IAAIF,gBAAgB,EAAE;MACjCoB,oBAAoB,CAAC,CAAC;IACxB;EACF,CAAC,EAAE,CAAClB,SAAS,EAAEF,gBAAgB,EAAEoB,oBAAoB,CAAC,CAAC;EAIvD,MAAMO,YAAY,GAAIC,WAAW,IAAK;IACpC,IAAI,CAACA,WAAW,EAAE,OAAO,EAAE;IAC3B,MAAMG,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC;IACxB,MAAMC,SAAS,GAAG,IAAID,IAAI,CAACJ,WAAW,CAAC;IACvC,IAAIM,GAAG,GAAGH,KAAK,CAACI,WAAW,CAAC,CAAC,GAAGF,SAAS,CAACE,WAAW,CAAC,CAAC;IACvD,MAAMC,SAAS,GAAGL,KAAK,CAACM,QAAQ,CAAC,CAAC,GAAGJ,SAAS,CAACI,QAAQ,CAAC,CAAC;IACzD,IAAID,SAAS,GAAG,CAAC,IAAKA,SAAS,KAAK,CAAC,IAAIL,KAAK,CAACO,OAAO,CAAC,CAAC,GAAGL,SAAS,CAACK,OAAO,CAAC,CAAE,EAAE;MAC/EJ,GAAG,EAAE;IACP;IACA,OAAOA,GAAG;EACZ,CAAC;;EAED;EACAzD,SAAS,CAAC,MAAM;IACd,IAAI;MACF,MAAM8D,YAAY,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC,IAAI,IAAI,CAAC;MACnF9B,qBAAqB,CAAC0B,YAAY,CAAC;IACrC,CAAC,CAAC,OAAOvB,KAAK,EAAE;MACd4B,OAAO,CAAC5B,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;MACnEC,QAAQ,CAAC,qDAAqD,CAAC;IACjE;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM4B,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAI,CAACzC,WAAW,IAAI,CAACM,MAAM,EAAE;MAC3BO,QAAQ,CAAC,6CAA6C,CAAC;MACvD;IACF;IAEA,MAAM6B,gBAAgB,GAAG;MACvBC,EAAE,EAAEf,IAAI,CAACgB,GAAG,CAAC,CAAC;MACdC,IAAI,EAAE,IAAIjB,IAAI,CAAC,CAAC,CAACkB,WAAW,CAAC,CAAC;MAC9B3B,QAAQ,EAAEvB,gBAAgB;MAC1B8B,IAAI,EAAE5B,SAAS;MACfoB,aAAa,EAAE7B,aAAa,CAACS,SAAS,CAAC;MACvCQ,MAAM,EAAEA,MAAM;MACdN,WAAW,EAAEA,WAAW;MACxBE,aAAa,EAAEA,aAAa;MAC5BP,IAAI,EAAE,CAAAA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoD,SAAS,KAAI;IAC3B,CAAC;IAED,IAAI;MACF,MAAMC,cAAc,GAAG,CAACN,gBAAgB,EAAE,GAAGlC,kBAAkB,CAAC;MAChEC,qBAAqB,CAACuC,cAAc,CAAC;MACrCV,YAAY,CAACW,OAAO,CAAC,oBAAoB,EAAEb,IAAI,CAACc,SAAS,CAACF,cAAc,CAAC,CAAC;MAC1EjC,gBAAgB,CAAC,KAAK,CAAC;IACzB,CAAC,CAAC,OAAOH,KAAK,EAAE;MACd4B,OAAO,CAAC5B,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrDC,QAAQ,CAAC,2CAA2C,CAAC;IACvD;EACF,CAAC;;EAED;EACA,MAAMsC,iBAAiB,GAAIC,aAAa,IAAK;IAC3C,IAAI;MACF,MAAMJ,cAAc,GAAGxC,kBAAkB,CAAC6C,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACX,EAAE,KAAKS,aAAa,CAAC;MAC7E3C,qBAAqB,CAACuC,cAAc,CAAC;MACrCV,YAAY,CAACW,OAAO,CAAC,oBAAoB,EAAEb,IAAI,CAACc,SAAS,CAACF,cAAc,CAAC,CAAC;IAC5E,CAAC,CAAC,OAAOpC,KAAK,EAAE;MACd4B,OAAO,CAAC5B,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtDC,QAAQ,CAAC,gCAAgC,CAAC;IAC5C;EACF,CAAC;EAED,MAAM0C,cAAc,GAAIpC,QAAQ,IAAK;IACnC,MAAMqC,aAAa,GAAG;MACpB,IAAI,EAAE,MAAM;MACZ,KAAK,EAAE,OAAO;MACd,eAAe,EAAE,eAAe;MAChC,UAAU,EAAE;IACd,CAAC;IACD,OAAOA,aAAa,CAACrC,QAAQ,CAAC,IAAIA,QAAQ;EAC5C,CAAC;EAED,oBACE3B,OAAA;IAAKiE,SAAS,EAAC,8BAA8B;IAAAC,QAAA,eAC3ClE,OAAA;MAAKiE,SAAS,EAAC,wCAAwC;MAAAC,QAAA,gBAErDlE,OAAA;QAAKiE,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BlE,OAAA;UAAIiE,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAEtD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLtE,OAAA;UAAGiE,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EAAC;QAGvD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,EAGHlD,KAAK,iBACJpB,OAAA;UAAKiE,SAAS,EAAC,sEAAsE;UAAAC,QAAA,eACnFlE,OAAA;YAAKiE,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChClE,OAAA,CAACT,aAAa;cAAC0E,SAAS,EAAC;YAA2B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvDtE,OAAA;cAAGiE,SAAS,EAAC,sBAAsB;cAAAC,QAAA,EAAE9C;YAAK;cAAA+C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNtE,OAAA;QAAKiE,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDlE,OAAA;UAAIiE,SAAS,EAAC,4DAA4D;UAAAC,QAAA,gBACxElE,OAAA,CAAClB,QAAQ;YAACmF,SAAS,EAAC;UAAoB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,qCAE7C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAELtE,OAAA;UAAKiE,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpDlE,OAAA;YAAAkE,QAAA,gBACElE,OAAA;cAAOuE,OAAO,EAAC,UAAU;cAACN,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEnF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRtE,OAAA;cACEmD,EAAE,EAAC,UAAU;cACbqB,KAAK,EAAEpE,gBAAiB;cACxBqE,QAAQ,EAAGC,CAAC,IAAKrE,mBAAmB,CAACqE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cACrDP,SAAS,EAAC,uIAAuI;cAAAC,QAAA,EAEhJU,MAAM,CAACC,IAAI,CAAC/E,SAAS,CAAC,CAACgF,GAAG,CAACnD,QAAQ,iBAClC3B,OAAA;gBAAuBwE,KAAK,EAAE7C,QAAS;gBAAAuC,QAAA,EACpCH,cAAc,CAACpC,QAAQ;cAAC,GADdA,QAAQ;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEb,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENtE,OAAA;YAAAkE,QAAA,gBACElE,OAAA;cAAOuE,OAAO,EAAC,MAAM;cAACN,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAE/E;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRtE,OAAA;cACE+E,IAAI,EAAC,MAAM;cACX5B,EAAE,EAAC,MAAM;cACTqB,KAAK,EAAElE,SAAU;cACjBmE,QAAQ,EAAGC,CAAC,IAAKnE,YAAY,CAACmE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cAC9CQ,WAAW,EAAC,OAAO;cACnBf,SAAS,EAAC;YAAuI;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAEL9D,WAAW,iBACVR,OAAA;QAAKiE,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBAEpDlE,OAAA;UAAKiE,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BlE,OAAA;YAAKiE,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACrDlE,OAAA;cAAIiE,SAAS,EAAC,4DAA4D;cAAAC,QAAA,gBACxElE,OAAA,CAAChB,OAAO;gBAACiF,SAAS,EAAC;cAAqB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,qCAE7C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAELtE,OAAA;cAAKiE,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EACnDU,MAAM,CAACK,OAAO,CAACzE,WAAW,CAAC,CAACsE,GAAG,CAAC,CAAC,CAACnD,QAAQ,EAAEuD,IAAI,CAAC,kBAChDlF,OAAA;gBAAoBiE,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,gBACvDlE,OAAA;kBAAKiE,SAAS,EAAC,wCAAwC;kBAAAC,QAAA,gBACrDlE,OAAA;oBAAIiE,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,EAAEH,cAAc,CAACpC,QAAQ;kBAAC;oBAAAwC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC3EtE,OAAA,CAACf,UAAU;oBAACgF,SAAS,EAAC;kBAAe;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrC,CAAC,eACNtE,OAAA;kBAAKiE,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,EACnDgB,IAAI,CAACC;gBAAa;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CAAC,eACNtE,OAAA;kBAAKiE,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,GAAC,UAC7B,EAACgB,IAAI,CAACE,aAAa,EAAC,KAC9B;gBAAA;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA,GAVE3C,QAAQ;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAWb,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGL5D,aAAa,iBACZV,OAAA;YAAKiE,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDlE,OAAA;cAAIiE,SAAS,EAAC,4DAA4D;cAAAC,QAAA,gBACxElE,OAAA,CAACd,KAAK;gBAAC+E,SAAS,EAAC;cAAsB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,6BAE5C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAELtE,OAAA;cAAKiE,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvBU,MAAM,CAACK,OAAO,CAACvE,aAAa,CAAC,CAACoE,GAAG,CAAC,CAAC,CAACC,IAAI,EAAEG,IAAI,CAAC,kBAC9ClF,OAAA;gBAAgBiE,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,eAC/DlE,OAAA;kBAAKiE,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,gBACpDlE,OAAA;oBAAAkE,QAAA,gBACElE,OAAA;sBAAIiE,SAAS,EAAC,wCAAwC;sBAAAC,QAAA,EACnDa,IAAI,KAAK,MAAM,GAAG,QAAQ,GAC1BA,IAAI,KAAK,UAAU,GAAG,UAAU,GAChCA,IAAI,KAAK,WAAW,GAAG,OAAO,GAC9BA,IAAI,KAAK,UAAU,GAAG,aAAa,GACnCA,IAAI,KAAK,YAAY,GAAG,aAAa,GAAGA;oBAAI;sBAAAZ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3C,CAAC,eACLtE,OAAA;sBAAGiE,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAEgB,IAAI,CAACG;oBAAW;sBAAAlB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxD,CAAC,eACNtE,OAAA;oBAAKiE,SAAS,EAAC,YAAY;oBAAAC,QAAA,gBACzBlE,OAAA;sBAAKiE,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,GAC/CgB,IAAI,CAACE,aAAa,EAAC,KACtB;oBAAA;sBAAAjB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNtE,OAAA;sBAAKiE,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,GACnCgB,IAAI,CAACI,MAAM,CAACC,GAAG,EAAC,GAAC,EAACL,IAAI,CAACI,MAAM,CAACE,GAAG,EAAC,OACrC;oBAAA;sBAAArB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC,GApBES,IAAI;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAqBT,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGNtE,OAAA;UAAKiE,SAAS,EAAC,WAAW;UAAAC,QAAA,GAEvBpD,MAAM,iBACLd,OAAA;YAAKiE,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDlE,OAAA;cAAIiE,SAAS,EAAC,4DAA4D;cAAAC,QAAA,gBACxElE,OAAA,CAACb,OAAO;gBAAC8E,SAAS,EAAC;cAAmB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,qBAE3C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLtE,OAAA;cAAKiE,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BlE,OAAA;gBAAKiE,SAAS,EAAC,sCAAsC;gBAAAC,QAAA,EAClDpD,MAAM,CAAC2E,OAAO,CAAC,CAAC;cAAC;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC,eACNtE,OAAA;gBAAKiE,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAEvC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNtE,OAAA;gBAAKiE,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EACxCpD,MAAM,IAAI,EAAE,GAAG,WAAW,GAC1BA,MAAM,IAAI,EAAE,GAAG,UAAU,GACzBA,MAAM,IAAI,EAAE,GAAG,KAAK,GACpBA,MAAM,IAAI,EAAE,GAAG,OAAO,GAAG;cAAa;gBAAAqD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAGA1D,eAAe,CAAC8E,MAAM,GAAG,CAAC,iBACzB1F,OAAA;YAAKiE,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDlE,OAAA;cAAIiE,SAAS,EAAC,4DAA4D;cAAAC,QAAA,gBACxElE,OAAA,CAACjB,YAAY;gBAACkF,SAAS,EAAC;cAAsB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,mBAEnD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAELtE,OAAA;cAAKiE,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvBtD,eAAe,CAACkE,GAAG,CAAC,CAACa,GAAG,EAAEC,KAAK,kBAC9B5F,OAAA;gBAAiBiE,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,gBAC5DlE,OAAA;kBAAIiE,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,EAC3CyB,GAAG,CAACE;gBAAK;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC,eACLtE,OAAA;kBAAGiE,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EACtCyB,GAAG,CAACN;gBAAW;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC,EACHqB,GAAG,CAACG,SAAS,iBACZ9F,OAAA;kBAAKiE,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,GAAC,gBAC1B,EAACyB,GAAG,CAACG,SAAS;gBAAA;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CACN,EACAqB,GAAG,CAACI,QAAQ,iBACX/F,OAAA;kBAAKiE,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,GAAC,YAC9B,EAACyB,GAAG,CAACI,QAAQ;gBAAA;kBAAA5B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB,CACN,EACAqB,GAAG,CAACK,SAAS,iBACZhG,OAAA;kBAAKiE,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,GAAC,gBAC1B,EAACyB,GAAG,CAACK,SAAS;gBAAA;kBAAA7B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CACN,EACAqB,GAAG,CAACM,OAAO,iBACVjG,OAAA;kBAAIiE,SAAS,EAAC,iCAAiC;kBAAAC,QAAA,EAC5CyB,GAAG,CAACM,OAAO,CAACnB,GAAG,CAAC,CAACoB,MAAM,EAAEC,CAAC,kBACzBnG,OAAA;oBAAYiE,SAAS,EAAC,WAAW;oBAAAC,QAAA,EAAEgC;kBAAM,GAAhCC,CAAC;oBAAAhC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAoC,CAC/C;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CACL;cAAA,GA5BOsB,KAAK;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA6BV,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eAGDtE,OAAA;YAAKiE,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDlE,OAAA;cAAIiE,SAAS,EAAC,4DAA4D;cAAAC,QAAA,gBACxElE,OAAA,CAACZ,UAAU;gBAAC6E,SAAS,EAAC;cAAsB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,8BAEjD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAELtE,OAAA;cAAKiE,SAAS,EAAC,iCAAiC;cAAAC,QAAA,gBAC9ClE,OAAA;gBAAKiE,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC/BlE,OAAA;kBAAKiE,SAAS,EAAC;gBAA4D;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAClFtE,OAAA;kBAAAkE,QAAA,EAAG;gBAA0G;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9G,CAAC,eACNtE,OAAA;gBAAKiE,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC/BlE,OAAA;kBAAKiE,SAAS,EAAC;gBAA4D;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAClFtE,OAAA;kBAAAkE,QAAA,EAAG;gBAA8F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClG,CAAC,eACNtE,OAAA;gBAAKiE,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC/BlE,OAAA;kBAAKiE,SAAS,EAAC;gBAA4D;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAClFtE,OAAA;kBAAAkE,QAAA,EAAG;gBAAsE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1E,CAAC,eACNtE,OAAA;gBAAKiE,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC/BlE,OAAA;kBAAKiE,SAAS,EAAC;gBAA4D;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAClFtE,OAAA;kBAAAkE,QAAA,EAAG;gBAA+D;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACpE,EAAA,CApWID,WAAW;EAAA,QACET,OAAO;AAAA;AAAA4G,EAAA,GADpBnG,WAAW;AAsWjB,eAAeA,WAAW;AAAC,IAAAmG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}