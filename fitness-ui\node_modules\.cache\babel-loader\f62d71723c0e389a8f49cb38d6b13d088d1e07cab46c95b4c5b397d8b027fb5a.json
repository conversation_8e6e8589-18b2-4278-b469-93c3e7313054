{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projet fitness\\\\DATA\\\\fitness-ui\\\\src\\\\pages\\\\Exercises.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { FiPlay, FiClock, FiZap, FiFilter, FiSearch, FiTarget, FiHeart, FiStar, FiInfo, FiPlus, FiTrendingUp, FiBookmark, FiVideo, FiBarChart2, FiCalendar, FiX } from 'react-icons/fi';\nimport { useAuth } from '../contexts/AuthContext';\nimport { exercises, exerciseCategories, muscleGroups, equipmentTypes, getDifficultyColor, getRecommendedExercises, searchExercises, createWorkoutPlan, toggleFavorite, loadFavorites, addToHistory, getExerciseHistory, calculateProgressStats, createCustomProgram, getExerciseVideo } from '../utils/exerciseUtils';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Exercises = () => {\n  _s();\n  var _exerciseCategories$s, _exerciseCategories$s2, _equipmentTypes$selec;\n  const {\n    user\n  } = useAuth();\n  const [selectedExercise, setSelectedExercise] = useState(null);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [showFilters, setShowFilters] = useState(false);\n  const [filters, setFilters] = useState({\n    category: 'all',\n    muscleGroup: 'all',\n    equipment: 'all',\n    difficulty: 'all',\n    maxDuration: null\n  });\n  const [activeTab, setActiveTab] = useState('all');\n  const [recommendedExercises, setRecommendedExercises] = useState([]);\n  const [favoriteExercises, setFavoriteExercises] = useState([]);\n  const [filteredExercises, setFilteredExercises] = useState(exercises);\n\n  // Nouveaux états pour les fonctionnalités avancées\n  const [exerciseHistory, setExerciseHistory] = useState([]);\n  const [showCreateProgram, setShowCreateProgram] = useState(false);\n  const [selectedExercisesForProgram, setSelectedExercisesForProgram] = useState([]);\n  const [showProgressModal, setShowProgressModal] = useState(false);\n  const [progressData, setProgressData] = useState(null);\n  const [showPerformanceModal, setShowPerformanceModal] = useState(false);\n  const [performanceData, setPerformanceData] = useState({\n    sets: 3,\n    reps: 12,\n    weight: '',\n    duration: '',\n    notes: ''\n  });\n  const [exerciseRatings, setExerciseRatings] = useState({});\n  const [exerciseComments, setExerciseComments] = useState({});\n  const [showRatingModal, setShowRatingModal] = useState(false);\n  const [ratingExercise, setRatingExercise] = useState(null);\n  useEffect(() => {\n    // Générer des recommandations basées sur le profil utilisateur\n    if (user) {\n      const recommendations = getRecommendedExercises(user, user.primaryGoal || 'general_fitness');\n      setRecommendedExercises(recommendations);\n    }\n\n    // Charger les favoris depuis localStorage\n    const savedFavorites = loadFavorites();\n    const favoriteExerciseObjects = exercises.filter(ex => savedFavorites.includes(ex.id));\n    setFavoriteExercises(favoriteExerciseObjects);\n\n    // Charger l'historique des exercices\n    const history = getExerciseHistory();\n    setExerciseHistory(history);\n  }, [user]);\n  useEffect(() => {\n    // Appliquer les filtres et la recherche\n    let results = exercises;\n    if (activeTab === 'recommended') {\n      results = recommendedExercises;\n    } else if (activeTab === 'favorites') {\n      results = favoriteExercises;\n    }\n    if (searchQuery || Object.values(filters).some(f => f !== 'all' && f !== null)) {\n      results = searchExercises(searchQuery, filters);\n    }\n    setFilteredExercises(results);\n  }, [searchQuery, filters, activeTab, recommendedExercises, favoriteExercises]);\n  const handleFilterChange = (filterType, value) => {\n    setFilters(prev => ({\n      ...prev,\n      [filterType]: value\n    }));\n  };\n  const clearFilters = () => {\n    setFilters({\n      category: 'all',\n      muscleGroup: 'all',\n      equipment: 'all',\n      difficulty: 'all',\n      maxDuration: null\n    });\n    setSearchQuery('');\n  };\n  const generateQuickWorkout = () => {\n    const selectedExercises = filteredExercises.slice(0, 4);\n    const workout = createWorkoutPlan(selectedExercises, 30);\n\n    // Ici on pourrait ouvrir une modal avec le programme généré\n    alert(`Programme généré !\\nDurée: ${workout.totalDuration} min\\nCalories estimées: ${workout.estimatedCalories}\\nExercices: ${workout.exercises.length}`);\n  };\n  const handleToggleFavorite = exerciseId => {\n    const savedFavorites = loadFavorites();\n    const isNowFavorite = toggleFavorite(exerciseId, savedFavorites, () => {});\n\n    // Mettre à jour l'état local\n    if (isNowFavorite) {\n      const exercise = exercises.find(ex => ex.id === exerciseId);\n      setFavoriteExercises(prev => [...prev, exercise]);\n    } else {\n      setFavoriteExercises(prev => prev.filter(ex => ex.id !== exerciseId));\n    }\n  };\n\n  // Ajouter un exercice à l'historique avec performance\n  const handleAddToHistory = exercise => {\n    const entry = addToHistory(exercise, performanceData);\n    if (entry) {\n      setExerciseHistory(prev => [entry, ...prev]);\n      setShowPerformanceModal(false);\n      setPerformanceData({\n        sets: 3,\n        reps: 12,\n        weight: '',\n        duration: '',\n        notes: ''\n      });\n      alert('Exercice ajouté à votre historique !');\n    }\n  };\n\n  // Afficher les statistiques de progression\n  const handleShowProgress = exerciseId => {\n    const stats = calculateProgressStats(exerciseId);\n    setProgressData(stats);\n    setShowProgressModal(true);\n  };\n\n  // Ajouter/retirer un exercice de la sélection pour programme\n  const handleToggleExerciseSelection = exercise => {\n    setSelectedExercisesForProgram(prev => {\n      const isSelected = prev.some(ex => ex.id === exercise.id);\n      if (isSelected) {\n        return prev.filter(ex => ex.id !== exercise.id);\n      } else {\n        return [...prev, exercise];\n      }\n    });\n  };\n\n  // Créer un programme personnalisé\n  const handleCreateProgram = programName => {\n    if (selectedExercisesForProgram.length === 0) {\n      alert('Veuillez sélectionner au moins un exercice');\n      return;\n    }\n    const program = createCustomProgram(programName, selectedExercisesForProgram);\n    setSelectedExercisesForProgram([]);\n    setShowCreateProgram(false);\n    alert(`Programme \"${program.name}\" créé avec succès !`);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold text-gray-900 mb-4\",\n          children: \"Biblioth\\xE8que d'Exercices\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg text-gray-600 max-w-3xl mx-auto\",\n          children: \"D\\xE9couvrez notre collection compl\\xE8te d'exercices avec instructions d\\xE9taill\\xE9es, recommandations personnalis\\xE9es et programmes adapt\\xE9s \\xE0 vos objectifs.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 203,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-lg p-6 mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col lg:flex-row gap-4 items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 relative\",\n            children: [/*#__PURE__*/_jsxDEV(FiSearch, {\n              className: \"absolute left-3 top-3 h-5 w-5 text-gray-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"Rechercher un exercice, groupe musculaire...\",\n              value: searchQuery,\n              onChange: e => setSearchQuery(e.target.value),\n              className: \"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-wrap gap-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowFilters(!showFilters),\n              className: `px-4 py-3 rounded-lg font-medium transition-colors flex items-center ${showFilters ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`,\n              children: [/*#__PURE__*/_jsxDEV(FiFilter, {\n                className: \"h-4 w-4 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 17\n              }, this), \"Filtres\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: generateQuickWorkout,\n              className: \"px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(FiTarget, {\n                className: \"h-4 w-4 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 17\n              }, this), \"Programme rapide\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowCreateProgram(true),\n              className: \"px-4 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(FiPlus, {\n                className: \"h-4 w-4 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 17\n              }, this), \"Cr\\xE9er programme\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setActiveTab('history'),\n              className: \"px-4 py-3 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(FiCalendar, {\n                className: \"h-4 w-4 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 254,\n                columnNumber: 17\n              }, this), \"Historique\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 250,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 11\n        }, this), showFilters && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-6 pt-6 border-t border-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Cat\\xE9gorie\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: filters.category,\n                onChange: e => handleFilterChange('category', e.target.value),\n                className: \"w-full p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"all\",\n                  children: \"Toutes les cat\\xE9gories\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 271,\n                  columnNumber: 21\n                }, this), Object.entries(exerciseCategories).map(([key, category]) => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: key,\n                  children: category.name\n                }, key, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 273,\n                  columnNumber: 23\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 266,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Groupe musculaire\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 279,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: filters.muscleGroup,\n                onChange: e => handleFilterChange('muscleGroup', e.target.value),\n                className: \"w-full p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"all\",\n                  children: \"Tous les groupes\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 285,\n                  columnNumber: 21\n                }, this), Object.entries(muscleGroups).map(([key, group]) => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: key,\n                  children: group.name\n                }, key, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 287,\n                  columnNumber: 23\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 278,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"\\xC9quipement\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 293,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: filters.equipment,\n                onChange: e => handleFilterChange('equipment', e.target.value),\n                className: \"w-full p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"all\",\n                  children: \"Tout \\xE9quipement\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 299,\n                  columnNumber: 21\n                }, this), Object.entries(equipmentTypes).map(([key, equipment]) => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: key,\n                  children: equipment.name\n                }, key, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 301,\n                  columnNumber: 23\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 294,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Difficult\\xE9\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: filters.difficulty,\n                onChange: e => handleFilterChange('difficulty', e.target.value),\n                className: \"w-full p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"all\",\n                  children: \"Toutes difficult\\xE9s\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 313,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Facile\",\n                  children: \"Facile\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 314,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Mod\\xE9r\\xE9\",\n                  children: \"Mod\\xE9r\\xE9\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 315,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Difficile\",\n                  children: \"Difficile\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 316,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 308,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-4 flex justify-between items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700\",\n                children: \"Dur\\xE9e max (min):\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 323,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                value: filters.maxDuration || '',\n                onChange: e => handleFilterChange('maxDuration', e.target.value ? parseInt(e.target.value) : null),\n                placeholder: \"60\",\n                className: \"w-20 p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 324,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 322,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: clearFilters,\n              className: \"px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors\",\n              children: \"Effacer les filtres\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 214,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex space-x-1 bg-gray-100 p-1 rounded-lg mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setActiveTab('all'),\n          className: `flex-1 py-3 px-4 rounded-md text-sm font-medium transition-colors ${activeTab === 'all' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'}`,\n          children: [\"Tous (\", exercises.length, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 345,\n          columnNumber: 11\n        }, this), user && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setActiveTab('recommended'),\n            className: `flex-1 py-3 px-4 rounded-md text-sm font-medium transition-colors ${activeTab === 'recommended' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'}`,\n            children: [/*#__PURE__*/_jsxDEV(FiStar, {\n              className: \"inline mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 365,\n              columnNumber: 17\n            }, this), \"Recommand\\xE9s (\", recommendedExercises.length, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 357,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setActiveTab('favorites'),\n            className: `flex-1 py-3 px-4 rounded-md text-sm font-medium transition-colors ${activeTab === 'favorites' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'}`,\n            children: [/*#__PURE__*/_jsxDEV(FiHeart, {\n              className: \"inline mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 376,\n              columnNumber: 17\n            }, this), \"Favoris (\", favoriteExercises.length, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 368,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setActiveTab('history'),\n            className: `flex-1 py-3 px-4 rounded-md text-sm font-medium transition-colors ${activeTab === 'history' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'}`,\n            children: [/*#__PURE__*/_jsxDEV(FiCalendar, {\n              className: \"inline mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 387,\n              columnNumber: 17\n            }, this), \"Historique (\", exerciseHistory.length, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 379,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 344,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-6\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: [filteredExercises.length, \" exercice\", filteredExercises.length > 1 ? 's' : '', \" trouv\\xE9\", filteredExercises.length > 1 ? 's' : '', searchQuery && ` pour \"${searchQuery}\"`]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 396,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 395,\n        columnNumber: 9\n      }, this), activeTab === 'history' ?\n      /*#__PURE__*/\n      /* Exercise History */\n      _jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-lg p-6 mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-2xl font-bold text-gray-900 mb-6\",\n          children: \"Historique des exercices\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 406,\n          columnNumber: 13\n        }, this), exerciseHistory.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center py-12\",\n          children: [/*#__PURE__*/_jsxDEV(FiCalendar, {\n            className: \"h-16 w-16 mx-auto text-gray-400 mb-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 409,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-medium text-gray-900 mb-2\",\n            children: \"Aucun exercice dans l'historique\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 410,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600\",\n            children: \"Commencez un exercice pour voir votre progression ici.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 411,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 408,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: exerciseHistory.slice(0, 10).map(entry => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"border border-gray-200 rounded-lg p-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"font-semibold text-gray-900\",\n                children: entry.exerciseName\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 418,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-500\",\n                children: new Date(entry.date).toLocaleDateString('fr-FR')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 419,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 417,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-600\",\n                  children: \"S\\xE9ries:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 425,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ml-2 font-medium\",\n                  children: entry.performance.sets\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 426,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 424,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-600\",\n                  children: \"R\\xE9p\\xE9titions:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 429,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ml-2 font-medium\",\n                  children: entry.performance.reps\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 430,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 428,\n                columnNumber: 23\n              }, this), entry.performance.weight && /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-600\",\n                  children: \"Poids:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 434,\n                  columnNumber: 27\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ml-2 font-medium\",\n                  children: [entry.performance.weight, \" kg\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 435,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 433,\n                columnNumber: 25\n              }, this), entry.performance.duration && /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-600\",\n                  children: \"Dur\\xE9e:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 440,\n                  columnNumber: 27\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ml-2 font-medium\",\n                  children: [entry.performance.duration, \" min\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 441,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 439,\n                columnNumber: 25\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 423,\n              columnNumber: 21\n            }, this), entry.performance.notes && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-2 text-sm text-gray-600\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium\",\n                children: \"Notes:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 447,\n                columnNumber: 25\n              }, this), \" \", entry.performance.notes]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 446,\n              columnNumber: 23\n            }, this)]\n          }, entry.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 416,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 414,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 405,\n        columnNumber: 11\n      }, this) :\n      /*#__PURE__*/\n      /* Exercise Grid */\n      _jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8\",\n        children: (activeTab === 'all' ? filteredExercises : activeTab === 'recommended' ? recommendedExercises : favoriteExercises).map(exercise => {\n          var _exerciseCategories$e, _exerciseCategories$e2, _equipmentTypes$exerc, _equipmentTypes$exerc2;\n          const isFavorite = favoriteExercises.some(fav => fav.id === exercise.id);\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start justify-between mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-xl font-semibold text-gray-900 mb-2\",\n                    children: exercise.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 471,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-2 mb-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `px-3 py-1 rounded-full text-sm font-medium ${getDifficultyColor(exercise.difficulty)}`,\n                      children: exercise.difficulty\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 475,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-xs text-gray-500\",\n                      children: [(_exerciseCategories$e = exerciseCategories[exercise.category]) === null || _exerciseCategories$e === void 0 ? void 0 : _exerciseCategories$e.icon, \" \", (_exerciseCategories$e2 = exerciseCategories[exercise.category]) === null || _exerciseCategories$e2 === void 0 ? void 0 : _exerciseCategories$e2.name]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 478,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 474,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 470,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: e => {\n                      e.stopPropagation();\n                      handleShowProgress(exercise.id);\n                    },\n                    className: \"p-2 rounded-full text-gray-400 hover:text-blue-500 transition-colors\",\n                    title: \"Voir les statistiques\",\n                    children: /*#__PURE__*/_jsxDEV(FiBarChart2, {\n                      className: \"h-5 w-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 492,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 484,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: e => {\n                      e.stopPropagation();\n                      handleToggleFavorite(exercise.id);\n                    },\n                    className: `p-2 rounded-full transition-colors ${isFavorite ? 'text-red-500 hover:text-red-600' : 'text-gray-400 hover:text-red-500'}`,\n                    title: isFavorite ? 'Retirer des favoris' : 'Ajouter aux favoris',\n                    children: /*#__PURE__*/_jsxDEV(FiHeart, {\n                      className: `h-5 w-5 ${isFavorite ? 'fill-current' : ''}`\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 506,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 494,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 483,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 469,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 mb-4 line-clamp-2\",\n                children: exercise.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 511,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-wrap gap-1 mb-4\",\n                children: exercise.muscleGroups.map(mg => {\n                  var _muscleGroups$mg;\n                  return /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full\",\n                    children: (_muscleGroups$mg = muscleGroups[mg]) === null || _muscleGroups$mg === void 0 ? void 0 : _muscleGroups$mg.name\n                  }, mg, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 518,\n                    columnNumber: 23\n                  }, this);\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 516,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center mb-4 text-sm text-gray-600\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"mr-2\",\n                  children: (_equipmentTypes$exerc = equipmentTypes[exercise.equipment]) === null || _equipmentTypes$exerc === void 0 ? void 0 : _equipmentTypes$exerc.icon\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 526,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: (_equipmentTypes$exerc2 = equipmentTypes[exercise.equipment]) === null || _equipmentTypes$exerc2 === void 0 ? void 0 : _equipmentTypes$exerc2.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 527,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 525,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between text-sm text-gray-500 mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(FiClock, {\n                    className: \"h-4 w-4 mr-1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 532,\n                    columnNumber: 23\n                  }, this), exercise.duration, \" min\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 531,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(FiZap, {\n                    className: \"h-4 w-4 mr-1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 536,\n                    columnNumber: 23\n                  }, this), exercise.calories, \" cal\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 535,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 530,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex gap-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => setSelectedExercise(exercise),\n                    className: \"flex-1 bg-gray-100 text-gray-700 py-2 px-3 rounded-lg hover:bg-gray-200 transition-colors flex items-center justify-center text-sm\",\n                    children: [/*#__PURE__*/_jsxDEV(FiInfo, {\n                      className: \"h-4 w-4 mr-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 547,\n                      columnNumber: 25\n                    }, this), \"D\\xE9tails\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 543,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => {\n                      setSelectedExercise(exercise);\n                      setShowPerformanceModal(true);\n                    },\n                    className: \"flex-1 bg-blue-600 text-white py-2 px-3 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center text-sm\",\n                    children: [/*#__PURE__*/_jsxDEV(FiPlay, {\n                      className: \"h-4 w-4 mr-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 557,\n                      columnNumber: 25\n                    }, this), \"Commencer\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 550,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 542,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex gap-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: e => {\n                      e.stopPropagation();\n                      handleToggleExerciseSelection(exercise);\n                    },\n                    className: `flex-1 py-2 px-3 rounded-lg transition-colors flex items-center justify-center text-sm ${selectedExercisesForProgram.some(ex => ex.id === exercise.id) ? 'bg-purple-100 text-purple-700 border border-purple-300' : 'bg-gray-50 text-gray-600 hover:bg-gray-100'}`,\n                    children: [/*#__PURE__*/_jsxDEV(FiBookmark, {\n                      className: \"h-4 w-4 mr-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 573,\n                      columnNumber: 25\n                    }, this), selectedExercisesForProgram.some(ex => ex.id === exercise.id) ? 'Sélectionné' : 'Sélectionner']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 562,\n                    columnNumber: 23\n                  }, this), getExerciseVideo(exercise.id) && /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => window.open(getExerciseVideo(exercise.id), '_blank'),\n                    className: \"flex-1 bg-green-100 text-green-700 py-2 px-3 rounded-lg hover:bg-green-200 transition-colors flex items-center justify-center text-sm\",\n                    children: [/*#__PURE__*/_jsxDEV(FiVideo, {\n                      className: \"h-4 w-4 mr-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 581,\n                      columnNumber: 27\n                    }, this), \"Vid\\xE9o\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 577,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 561,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 541,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 468,\n              columnNumber: 17\n            }, this)\n          }, exercise.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 464,\n            columnNumber: 15\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 457,\n        columnNumber: 11\n      }, this), filteredExercises.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-gray-400 mb-4\",\n          children: /*#__PURE__*/_jsxDEV(FiSearch, {\n            className: \"h-16 w-16 mx-auto\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 598,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 597,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium text-gray-900 mb-2\",\n          children: \"Aucun exercice trouv\\xE9\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 600,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mb-4\",\n          children: \"Essayez de modifier vos crit\\xE8res de recherche ou vos filtres.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 603,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: clearFilters,\n          className: \"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors\",\n          children: \"Effacer les filtres\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 606,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 596,\n        columnNumber: 11\n      }, this), showPerformanceModal && selectedExercise && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg max-w-md w-full\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-xl font-bold text-gray-900\",\n                children: \"Enregistrer la performance\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 621,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setShowPerformanceModal(false),\n                className: \"text-gray-400 hover:text-gray-600\",\n                children: /*#__PURE__*/_jsxDEV(FiX, {\n                  className: \"h-6 w-6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 628,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 624,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 620,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Nombre de s\\xE9ries\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 634,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  value: performanceData.sets,\n                  onChange: e => setPerformanceData(prev => ({\n                    ...prev,\n                    sets: parseInt(e.target.value) || 0\n                  })),\n                  className: \"w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 637,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 633,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"R\\xE9p\\xE9titions par s\\xE9rie\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 646,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  value: performanceData.reps,\n                  onChange: e => setPerformanceData(prev => ({\n                    ...prev,\n                    reps: parseInt(e.target.value) || 0\n                  })),\n                  className: \"w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 649,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 645,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Poids (kg) - optionnel\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 658,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  value: performanceData.weight,\n                  onChange: e => setPerformanceData(prev => ({\n                    ...prev,\n                    weight: e.target.value\n                  })),\n                  className: \"w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                  placeholder: \"Ex: 20\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 661,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 657,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Dur\\xE9e (min) - optionnel\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 671,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  value: performanceData.duration,\n                  onChange: e => setPerformanceData(prev => ({\n                    ...prev,\n                    duration: e.target.value\n                  })),\n                  className: \"w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                  placeholder: \"Ex: 15\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 674,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 670,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Notes - optionnel\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 684,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  value: performanceData.notes,\n                  onChange: e => setPerformanceData(prev => ({\n                    ...prev,\n                    notes: e.target.value\n                  })),\n                  className: \"w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                  rows: \"3\",\n                  placeholder: \"Comment vous \\xEAtes-vous senti ?\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 687,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 683,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 632,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex gap-3 mt-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setShowPerformanceModal(false),\n                className: \"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\",\n                children: \"Annuler\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 698,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleAddToHistory(selectedExercise),\n                className: \"flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                children: \"Enregistrer\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 704,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 697,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 619,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 618,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 617,\n        columnNumber: 11\n      }, this), showProgressModal && progressData && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg max-w-lg w-full\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-xl font-bold text-gray-900\",\n                children: \"Statistiques de progression\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 722,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setShowProgressModal(false),\n                className: \"text-gray-400 hover:text-gray-600\",\n                children: /*#__PURE__*/_jsxDEV(FiX, {\n                  className: \"h-6 w-6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 729,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 725,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 721,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-2 gap-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-blue-50 p-4 rounded-lg\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-2xl font-bold text-blue-600\",\n                    children: progressData.totalSessions\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 736,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm text-gray-600\",\n                    children: \"Sessions totales\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 737,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 735,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-green-50 p-4 rounded-lg\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-2xl font-bold text-green-600\",\n                    children: progressData.lastPerformed ? new Date(progressData.lastPerformed).toLocaleDateString('fr-FR') : 'Jamais'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 740,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm text-gray-600\",\n                    children: \"Derni\\xE8re fois\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 746,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 739,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 734,\n                columnNumber: 19\n              }, this), progressData.bestPerformance && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-yellow-50 p-4 rounded-lg\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"font-semibold text-gray-900 mb-2\",\n                  children: \"Meilleure performance\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 752,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-gray-600\",\n                  children: [progressData.bestPerformance.performance.sets, \" s\\xE9ries \\xD7 \", progressData.bestPerformance.performance.reps, \" reps\", progressData.bestPerformance.performance.weight && ` à ${progressData.bestPerformance.performance.weight} kg`]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 753,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 751,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-gray-50 p-4 rounded-lg\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"font-semibold text-gray-900 mb-2\",\n                  children: \"Tendance\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 763,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `flex items-center ${progressData.progressTrend === 'improving' ? 'text-green-600' : progressData.progressTrend === 'declining' ? 'text-red-600' : 'text-gray-600'}`,\n                  children: [/*#__PURE__*/_jsxDEV(FiTrendingUp, {\n                    className: \"h-4 w-4 mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 769,\n                    columnNumber: 23\n                  }, this), progressData.progressTrend === 'improving' ? 'En progression' : progressData.progressTrend === 'declining' ? 'En baisse' : 'Stable']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 764,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 762,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 733,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 720,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 719,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 718,\n        columnNumber: 11\n      }, this), showCreateProgram && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg max-w-md w-full\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-xl font-bold text-gray-900\",\n                children: \"Cr\\xE9er un programme\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 787,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setShowCreateProgram(false),\n                className: \"text-gray-400 hover:text-gray-600\",\n                children: /*#__PURE__*/_jsxDEV(FiX, {\n                  className: \"h-6 w-6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 794,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 790,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 786,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 mb-4\",\n                children: [selectedExercisesForProgram.length, \" exercice(s) s\\xE9lectionn\\xE9(s)\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 799,\n                columnNumber: 19\n              }, this), selectedExercisesForProgram.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-2 mb-4 max-h-40 overflow-y-auto\",\n                children: selectedExercisesForProgram.map(exercise => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between bg-gray-50 p-2 rounded\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm\",\n                    children: exercise.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 807,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleToggleExerciseSelection(exercise),\n                    className: \"text-red-500 hover:text-red-700\",\n                    children: /*#__PURE__*/_jsxDEV(FiX, {\n                      className: \"h-4 w-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 812,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 808,\n                    columnNumber: 27\n                  }, this)]\n                }, exercise.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 806,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 804,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"Nom du programme\",\n                className: \"w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                onKeyPress: e => {\n                  if (e.key === 'Enter') {\n                    handleCreateProgram(e.target.value);\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 819,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 798,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex gap-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setShowCreateProgram(false),\n                className: \"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\",\n                children: \"Annuler\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 832,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: e => {\n                  const input = e.target.parentElement.parentElement.querySelector('input');\n                  handleCreateProgram(input.value);\n                },\n                className: \"flex-1 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors\",\n                children: \"Cr\\xE9er\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 838,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 831,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 785,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 784,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 783,\n        columnNumber: 11\n      }, this), selectedExercise && !showPerformanceModal && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"text-3xl font-bold text-gray-900 mb-2\",\n                  children: selectedExercise.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 860,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `px-3 py-1 rounded-full text-sm font-medium ${getDifficultyColor(selectedExercise.difficulty)}`,\n                    children: selectedExercise.difficulty\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 864,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-600\",\n                    children: [(_exerciseCategories$s = exerciseCategories[selectedExercise.category]) === null || _exerciseCategories$s === void 0 ? void 0 : _exerciseCategories$s.icon, \" \", (_exerciseCategories$s2 = exerciseCategories[selectedExercise.category]) === null || _exerciseCategories$s2 === void 0 ? void 0 : _exerciseCategories$s2.name]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 867,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 863,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 859,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setSelectedExercise(null),\n                className: \"text-gray-400 hover:text-gray-600 text-2xl\",\n                children: \"\\u2715\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 872,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 858,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"lg:col-span-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-xl font-semibold text-gray-900 mb-3\",\n                    children: \"Description\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 884,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-gray-600 leading-relaxed\",\n                    children: selectedExercise.description\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 885,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 883,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-xl font-semibold text-gray-900 mb-4\",\n                    children: \"Instructions d\\xE9taill\\xE9es\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 889,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"ol\", {\n                    className: \"list-decimal list-inside space-y-3\",\n                    children: selectedExercise.instructions.map((instruction, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n                      className: \"text-gray-600 leading-relaxed pl-2\",\n                      children: instruction\n                    }, index, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 892,\n                      columnNumber: 27\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 890,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 888,\n                  columnNumber: 21\n                }, this), selectedExercise.tips && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-xl font-semibold text-gray-900 mb-4\",\n                    children: \"Conseils\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 901,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                    className: \"space-y-2\",\n                    children: selectedExercise.tips.map((tip, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n                      className: \"flex items-start text-gray-600\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-blue-500 mr-2 mt-1\",\n                        children: \"\\u2022\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 905,\n                        columnNumber: 31\n                      }, this), tip]\n                    }, index, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 904,\n                      columnNumber: 29\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 902,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 900,\n                  columnNumber: 23\n                }, this), selectedExercise.variations && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-xl font-semibold text-gray-900 mb-4\",\n                    children: \"Variations\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 915,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                    className: \"space-y-2\",\n                    children: selectedExercise.variations.map((variation, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n                      className: \"flex items-start text-gray-600\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-green-500 mr-2 mt-1\",\n                        children: \"\\u2022\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 919,\n                        columnNumber: 31\n                      }, this), variation]\n                    }, index, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 918,\n                      columnNumber: 29\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 916,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 914,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 882,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"lg:col-span-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-gray-50 rounded-lg p-6 mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-lg font-semibold text-gray-900 mb-4\",\n                    children: \"Informations\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 931,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"space-y-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex justify-between\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-gray-600\",\n                        children: \"Dur\\xE9e\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 935,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"font-medium\",\n                        children: [selectedExercise.duration, \" min\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 936,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 934,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex justify-between\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-gray-600\",\n                        children: \"Calories\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 939,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"font-medium\",\n                        children: [selectedExercise.calories, \" cal\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 940,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 938,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex justify-between\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-gray-600\",\n                        children: \"\\xC9quipement\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 943,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"font-medium\",\n                        children: (_equipmentTypes$selec = equipmentTypes[selectedExercise.equipment]) === null || _equipmentTypes$selec === void 0 ? void 0 : _equipmentTypes$selec.name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 944,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 942,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 933,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 930,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-gray-50 rounded-lg p-6 mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-lg font-semibold text-gray-900 mb-4\",\n                    children: \"Groupes musculaires\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 950,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex flex-wrap gap-2\",\n                    children: selectedExercise.muscleGroups.map(mg => {\n                      var _muscleGroups$mg2, _muscleGroups$mg3;\n                      return /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"px-3 py-1 bg-blue-100 text-blue-700 text-sm rounded-full\",\n                        children: [(_muscleGroups$mg2 = muscleGroups[mg]) === null || _muscleGroups$mg2 === void 0 ? void 0 : _muscleGroups$mg2.icon, \" \", (_muscleGroups$mg3 = muscleGroups[mg]) === null || _muscleGroups$mg3 === void 0 ? void 0 : _muscleGroups$mg3.name]\n                      }, mg, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 953,\n                        columnNumber: 27\n                      }, this);\n                    })\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 951,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 949,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => toggleFavorite(selectedExercise.id),\n                    className: `w-full py-3 px-6 rounded-lg font-medium transition-colors flex items-center justify-center ${favoriteExercises.some(fav => fav.id === selectedExercise.id) ? 'bg-red-100 text-red-700 hover:bg-red-200' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`,\n                    children: [/*#__PURE__*/_jsxDEV(FiHeart, {\n                      className: \"h-5 w-5 mr-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 969,\n                      columnNumber: 25\n                    }, this), favoriteExercises.some(fav => fav.id === selectedExercise.id) ? 'Retirer des favoris' : 'Ajouter aux favoris']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 961,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"w-full bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center font-medium\",\n                    children: [/*#__PURE__*/_jsxDEV(FiPlay, {\n                      className: \"h-5 w-5 mr-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 974,\n                      columnNumber: 25\n                    }, this), \"Commencer l'exercice\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 973,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 960,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 929,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 880,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 857,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 856,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 855,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 201,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 200,\n    columnNumber: 5\n  }, this);\n};\n_s(Exercises, \"KVLMrNt+y/4o1tiNhxODrQlETQc=\", false, function () {\n  return [useAuth];\n});\n_c = Exercises;\nexport default Exercises;\nvar _c;\n$RefreshReg$(_c, \"Exercises\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "FiPlay", "<PERSON><PERSON><PERSON>", "FiZap", "<PERSON><PERSON><PERSON><PERSON>", "FiSearch", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "FiStar", "FiInfo", "FiPlus", "FiTrendingUp", "FiBookmark", "FiVideo", "FiBarChart2", "FiCalendar", "FiX", "useAuth", "exercises", "exerciseCategories", "muscleGroups", "equipmentTypes", "getDifficultyColor", "getRecommendedExercises", "searchExercises", "createWorkoutPlan", "toggleFavorite", "loadFavorites", "addToHistory", "getExerciseHistory", "calculateProgressStats", "createCustomProgram", "getExerciseVideo", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Exercises", "_s", "_exerciseCategories$s", "_exerciseCategories$s2", "_equipmentTypes$selec", "user", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "setSelectedExercise", "searchQuery", "setSearch<PERSON>uery", "showFilters", "setShowFilters", "filters", "setFilters", "category", "muscleGroup", "equipment", "difficulty", "maxDuration", "activeTab", "setActiveTab", "recommendedExercises", "setRecommendedExercises", "favoriteExercises", "setFavoriteExercises", "filteredExercises", "setFilteredExercises", "exerciseHistory", "setExerciseHistory", "showCreateProgram", "setShowCreateProgram", "selectedExercisesForProgram", "setSelectedExercisesForProgram", "showProgressModal", "setShowProgressModal", "progressData", "setProgressData", "showPerformanceModal", "setShowPerformanceModal", "performanceData", "setPerformanceData", "sets", "reps", "weight", "duration", "notes", "exerciseRatings", "setExerciseRatings", "exerciseComments", "setExerciseComments", "showRatingModal", "setShowRatingModal", "ratingExercise", "setRatingExercise", "recommendations", "primaryGoal", "savedFavorites", "favoriteExerciseObjects", "filter", "ex", "includes", "id", "history", "results", "Object", "values", "some", "f", "handleFilterChange", "filterType", "value", "prev", "clearFilters", "generateQuickWorkout", "selectedExercises", "slice", "workout", "alert", "totalDuration", "estimatedCalories", "length", "handleToggleFavorite", "exerciseId", "isNowFavorite", "exercise", "find", "handleAddToHistory", "entry", "handleShowProgress", "stats", "handleToggleExerciseSelection", "isSelected", "handleCreateProgram", "programName", "program", "name", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "placeholder", "onChange", "e", "target", "onClick", "entries", "map", "key", "group", "parseInt", "exercise<PERSON>ame", "Date", "date", "toLocaleDateString", "performance", "_exerciseCategories$e", "_exerciseCategories$e2", "_equipmentTypes$exerc", "_equipmentTypes$exerc2", "isFavorite", "fav", "icon", "stopPropagation", "title", "description", "mg", "_muscleGroups$mg", "calories", "window", "open", "rows", "totalSessions", "lastPerformed", "bestPerformance", "progressTrend", "onKeyPress", "input", "parentElement", "querySelector", "instructions", "instruction", "index", "tips", "tip", "variations", "variation", "_muscleGroups$mg2", "_muscleGroups$mg3", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/projet fitness/DATA/fitness-ui/src/pages/Exercises.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  <PERSON><PERSON>lay,\n  FiClock,\n  FiZap,\n  FiFilter,\n  FiSearch,\n  FiTarget,\n  FiHeart,\n  FiStar,\n  FiInfo,\n  FiPlus,\n  FiTrendingUp,\n  FiBookmark,\n  FiVideo,\n  FiBarChart2,\n  FiCalendar,\n  FiX,\n\n} from 'react-icons/fi';\nimport { useAuth } from '../contexts/AuthContext';\nimport {\n  exercises,\n  exerciseCategories,\n  muscleGroups,\n  equipmentTypes,\n  getDifficultyColor,\n  getRecommendedExercises,\n  searchExercises,\n  createWorkoutPlan,\n  toggleFavorite,\n  loadFavorites,\n  addToHistory,\n  getExerciseHistory,\n  calculateProgressStats,\n  createCustomProgram,\n  getExerciseVideo\n} from '../utils/exerciseUtils';\n\nconst Exercises = () => {\n  const { user } = useAuth();\n\n  const [selectedExercise, setSelectedExercise] = useState(null);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [showFilters, setShowFilters] = useState(false);\n  const [filters, setFilters] = useState({\n    category: 'all',\n    muscleGroup: 'all',\n    equipment: 'all',\n    difficulty: 'all',\n    maxDuration: null\n  });\n  const [activeTab, setActiveTab] = useState('all');\n  const [recommendedExercises, setRecommendedExercises] = useState([]);\n  const [favoriteExercises, setFavoriteExercises] = useState([]);\n  const [filteredExercises, setFilteredExercises] = useState(exercises);\n\n  // Nouveaux états pour les fonctionnalités avancées\n  const [exerciseHistory, setExerciseHistory] = useState([]);\n  const [showCreateProgram, setShowCreateProgram] = useState(false);\n  const [selectedExercisesForProgram, setSelectedExercisesForProgram] = useState([]);\n  const [showProgressModal, setShowProgressModal] = useState(false);\n  const [progressData, setProgressData] = useState(null);\n  const [showPerformanceModal, setShowPerformanceModal] = useState(false);\n  const [performanceData, setPerformanceData] = useState({\n    sets: 3,\n    reps: 12,\n    weight: '',\n    duration: '',\n    notes: ''\n  });\n  const [exerciseRatings, setExerciseRatings] = useState({});\n  const [exerciseComments, setExerciseComments] = useState({});\n  const [showRatingModal, setShowRatingModal] = useState(false);\n  const [ratingExercise, setRatingExercise] = useState(null);\n\n  useEffect(() => {\n    // Générer des recommandations basées sur le profil utilisateur\n    if (user) {\n      const recommendations = getRecommendedExercises(user, user.primaryGoal || 'general_fitness');\n      setRecommendedExercises(recommendations);\n    }\n\n    // Charger les favoris depuis localStorage\n    const savedFavorites = loadFavorites();\n    const favoriteExerciseObjects = exercises.filter(ex => savedFavorites.includes(ex.id));\n    setFavoriteExercises(favoriteExerciseObjects);\n\n    // Charger l'historique des exercices\n    const history = getExerciseHistory();\n    setExerciseHistory(history);\n  }, [user]);\n\n  useEffect(() => {\n    // Appliquer les filtres et la recherche\n    let results = exercises;\n    \n    if (activeTab === 'recommended') {\n      results = recommendedExercises;\n    } else if (activeTab === 'favorites') {\n      results = favoriteExercises;\n    }\n    \n    if (searchQuery || Object.values(filters).some(f => f !== 'all' && f !== null)) {\n      results = searchExercises(searchQuery, filters);\n    }\n    \n    setFilteredExercises(results);\n  }, [searchQuery, filters, activeTab, recommendedExercises, favoriteExercises]);\n\n  const handleFilterChange = (filterType, value) => {\n    setFilters(prev => ({\n      ...prev,\n      [filterType]: value\n    }));\n  };\n\n  const clearFilters = () => {\n    setFilters({\n      category: 'all',\n      muscleGroup: 'all',\n      equipment: 'all',\n      difficulty: 'all',\n      maxDuration: null\n    });\n    setSearchQuery('');\n  };\n\n  const generateQuickWorkout = () => {\n    const selectedExercises = filteredExercises.slice(0, 4);\n    const workout = createWorkoutPlan(selectedExercises, 30);\n    \n    // Ici on pourrait ouvrir une modal avec le programme généré\n    alert(`Programme généré !\\nDurée: ${workout.totalDuration} min\\nCalories estimées: ${workout.estimatedCalories}\\nExercices: ${workout.exercises.length}`);\n  };\n\n  const handleToggleFavorite = (exerciseId) => {\n    const savedFavorites = loadFavorites();\n    const isNowFavorite = toggleFavorite(exerciseId, savedFavorites, () => {});\n\n    // Mettre à jour l'état local\n    if (isNowFavorite) {\n      const exercise = exercises.find(ex => ex.id === exerciseId);\n      setFavoriteExercises(prev => [...prev, exercise]);\n    } else {\n      setFavoriteExercises(prev => prev.filter(ex => ex.id !== exerciseId));\n    }\n  };\n\n  // Ajouter un exercice à l'historique avec performance\n  const handleAddToHistory = (exercise) => {\n    const entry = addToHistory(exercise, performanceData);\n    if (entry) {\n      setExerciseHistory(prev => [entry, ...prev]);\n      setShowPerformanceModal(false);\n      setPerformanceData({\n        sets: 3,\n        reps: 12,\n        weight: '',\n        duration: '',\n        notes: ''\n      });\n      alert('Exercice ajouté à votre historique !');\n    }\n  };\n\n  // Afficher les statistiques de progression\n  const handleShowProgress = (exerciseId) => {\n    const stats = calculateProgressStats(exerciseId);\n    setProgressData(stats);\n    setShowProgressModal(true);\n  };\n\n  // Ajouter/retirer un exercice de la sélection pour programme\n  const handleToggleExerciseSelection = (exercise) => {\n    setSelectedExercisesForProgram(prev => {\n      const isSelected = prev.some(ex => ex.id === exercise.id);\n      if (isSelected) {\n        return prev.filter(ex => ex.id !== exercise.id);\n      } else {\n        return [...prev, exercise];\n      }\n    });\n  };\n\n  // Créer un programme personnalisé\n  const handleCreateProgram = (programName) => {\n    if (selectedExercisesForProgram.length === 0) {\n      alert('Veuillez sélectionner au moins un exercice');\n      return;\n    }\n\n    const program = createCustomProgram(programName, selectedExercisesForProgram);\n    setSelectedExercisesForProgram([]);\n    setShowCreateProgram(false);\n    alert(`Programme \"${program.name}\" créé avec succès !`);\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Header */}\n        <div className=\"text-center mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-4\">\n            Bibliothèque d'Exercices\n          </h1>\n          <p className=\"text-lg text-gray-600 max-w-3xl mx-auto\">\n            Découvrez notre collection complète d'exercices avec instructions détaillées, \n            recommandations personnalisées et programmes adaptés à vos objectifs.\n          </p>\n        </div>\n\n        {/* Search and Quick Actions */}\n        <div className=\"bg-white rounded-lg shadow-lg p-6 mb-8\">\n          <div className=\"flex flex-col lg:flex-row gap-4 items-center\">\n            <div className=\"flex-1 relative\">\n              <FiSearch className=\"absolute left-3 top-3 h-5 w-5 text-gray-400\" />\n              <input\n                type=\"text\"\n                placeholder=\"Rechercher un exercice, groupe musculaire...\"\n                value={searchQuery}\n                onChange={(e) => setSearchQuery(e.target.value)}\n                className=\"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              />\n            </div>\n            <div className=\"flex flex-wrap gap-3\">\n              <button\n                onClick={() => setShowFilters(!showFilters)}\n                className={`px-4 py-3 rounded-lg font-medium transition-colors flex items-center ${\n                  showFilters ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'\n                }`}\n              >\n                <FiFilter className=\"h-4 w-4 mr-2\" />\n                Filtres\n              </button>\n              <button\n                onClick={generateQuickWorkout}\n                className=\"px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center\"\n              >\n                <FiTarget className=\"h-4 w-4 mr-2\" />\n                Programme rapide\n              </button>\n              <button\n                onClick={() => setShowCreateProgram(true)}\n                className=\"px-4 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors flex items-center\"\n              >\n                <FiPlus className=\"h-4 w-4 mr-2\" />\n                Créer programme\n              </button>\n              <button\n                onClick={() => setActiveTab('history')}\n                className=\"px-4 py-3 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors flex items-center\"\n              >\n                <FiCalendar className=\"h-4 w-4 mr-2\" />\n                Historique\n              </button>\n            </div>\n          </div>\n\n          {/* Advanced Filters */}\n          {showFilters && (\n            <div className=\"mt-6 pt-6 border-t border-gray-200\">\n              <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Catégorie</label>\n                  <select\n                    value={filters.category}\n                    onChange={(e) => handleFilterChange('category', e.target.value)}\n                    className=\"w-full p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  >\n                    <option value=\"all\">Toutes les catégories</option>\n                    {Object.entries(exerciseCategories).map(([key, category]) => (\n                      <option key={key} value={key}>{category.name}</option>\n                    ))}\n                  </select>\n                </div>\n                \n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Groupe musculaire</label>\n                  <select\n                    value={filters.muscleGroup}\n                    onChange={(e) => handleFilterChange('muscleGroup', e.target.value)}\n                    className=\"w-full p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  >\n                    <option value=\"all\">Tous les groupes</option>\n                    {Object.entries(muscleGroups).map(([key, group]) => (\n                      <option key={key} value={key}>{group.name}</option>\n                    ))}\n                  </select>\n                </div>\n                \n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Équipement</label>\n                  <select\n                    value={filters.equipment}\n                    onChange={(e) => handleFilterChange('equipment', e.target.value)}\n                    className=\"w-full p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  >\n                    <option value=\"all\">Tout équipement</option>\n                    {Object.entries(equipmentTypes).map(([key, equipment]) => (\n                      <option key={key} value={key}>{equipment.name}</option>\n                    ))}\n                  </select>\n                </div>\n                \n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Difficulté</label>\n                  <select\n                    value={filters.difficulty}\n                    onChange={(e) => handleFilterChange('difficulty', e.target.value)}\n                    className=\"w-full p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  >\n                    <option value=\"all\">Toutes difficultés</option>\n                    <option value=\"Facile\">Facile</option>\n                    <option value=\"Modéré\">Modéré</option>\n                    <option value=\"Difficile\">Difficile</option>\n                  </select>\n                </div>\n              </div>\n              \n              <div className=\"mt-4 flex justify-between items-center\">\n                <div className=\"flex items-center space-x-4\">\n                  <label className=\"block text-sm font-medium text-gray-700\">Durée max (min):</label>\n                  <input\n                    type=\"number\"\n                    value={filters.maxDuration || ''}\n                    onChange={(e) => handleFilterChange('maxDuration', e.target.value ? parseInt(e.target.value) : null)}\n                    placeholder=\"60\"\n                    className=\"w-20 p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  />\n                </div>\n                <button\n                  onClick={clearFilters}\n                  className=\"px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors\"\n                >\n                  Effacer les filtres\n                </button>\n              </div>\n            </div>\n          )}\n        </div>\n\n        {/* Tabs */}\n        <div className=\"flex space-x-1 bg-gray-100 p-1 rounded-lg mb-8\">\n          <button\n            onClick={() => setActiveTab('all')}\n            className={`flex-1 py-3 px-4 rounded-md text-sm font-medium transition-colors ${\n              activeTab === 'all'\n                ? 'bg-white text-blue-600 shadow-sm'\n                : 'text-gray-600 hover:text-gray-900'\n            }`}\n          >\n            Tous ({exercises.length})\n          </button>\n          {user && (\n            <>\n              <button\n                onClick={() => setActiveTab('recommended')}\n                className={`flex-1 py-3 px-4 rounded-md text-sm font-medium transition-colors ${\n                  activeTab === 'recommended'\n                    ? 'bg-white text-blue-600 shadow-sm'\n                    : 'text-gray-600 hover:text-gray-900'\n                }`}\n              >\n                <FiStar className=\"inline mr-2\" />\n                Recommandés ({recommendedExercises.length})\n              </button>\n              <button\n                onClick={() => setActiveTab('favorites')}\n                className={`flex-1 py-3 px-4 rounded-md text-sm font-medium transition-colors ${\n                  activeTab === 'favorites'\n                    ? 'bg-white text-blue-600 shadow-sm'\n                    : 'text-gray-600 hover:text-gray-900'\n                }`}\n              >\n                <FiHeart className=\"inline mr-2\" />\n                Favoris ({favoriteExercises.length})\n              </button>\n              <button\n                onClick={() => setActiveTab('history')}\n                className={`flex-1 py-3 px-4 rounded-md text-sm font-medium transition-colors ${\n                  activeTab === 'history'\n                    ? 'bg-white text-blue-600 shadow-sm'\n                    : 'text-gray-600 hover:text-gray-900'\n                }`}\n              >\n                <FiCalendar className=\"inline mr-2\" />\n                Historique ({exerciseHistory.length})\n              </button>\n            </>\n          )}\n        </div>\n\n        {/* Results Summary */}\n        <div className=\"mb-6\">\n          <p className=\"text-gray-600\">\n            {filteredExercises.length} exercice{filteredExercises.length > 1 ? 's' : ''} trouvé{filteredExercises.length > 1 ? 's' : ''}\n            {searchQuery && ` pour \"${searchQuery}\"`}\n          </p>\n        </div>\n\n        {/* Content based on active tab */}\n        {activeTab === 'history' ? (\n          /* Exercise History */\n          <div className=\"bg-white rounded-lg shadow-lg p-6 mb-8\">\n            <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">Historique des exercices</h2>\n            {exerciseHistory.length === 0 ? (\n              <div className=\"text-center py-12\">\n                <FiCalendar className=\"h-16 w-16 mx-auto text-gray-400 mb-4\" />\n                <h3 className=\"text-lg font-medium text-gray-900 mb-2\">Aucun exercice dans l'historique</h3>\n                <p className=\"text-gray-600\">Commencez un exercice pour voir votre progression ici.</p>\n              </div>\n            ) : (\n              <div className=\"space-y-4\">\n                {exerciseHistory.slice(0, 10).map(entry => (\n                  <div key={entry.id} className=\"border border-gray-200 rounded-lg p-4\">\n                    <div className=\"flex items-center justify-between mb-2\">\n                      <h3 className=\"font-semibold text-gray-900\">{entry.exerciseName}</h3>\n                      <span className=\"text-sm text-gray-500\">\n                        {new Date(entry.date).toLocaleDateString('fr-FR')}\n                      </span>\n                    </div>\n                    <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm\">\n                      <div>\n                        <span className=\"text-gray-600\">Séries:</span>\n                        <span className=\"ml-2 font-medium\">{entry.performance.sets}</span>\n                      </div>\n                      <div>\n                        <span className=\"text-gray-600\">Répétitions:</span>\n                        <span className=\"ml-2 font-medium\">{entry.performance.reps}</span>\n                      </div>\n                      {entry.performance.weight && (\n                        <div>\n                          <span className=\"text-gray-600\">Poids:</span>\n                          <span className=\"ml-2 font-medium\">{entry.performance.weight} kg</span>\n                        </div>\n                      )}\n                      {entry.performance.duration && (\n                        <div>\n                          <span className=\"text-gray-600\">Durée:</span>\n                          <span className=\"ml-2 font-medium\">{entry.performance.duration} min</span>\n                        </div>\n                      )}\n                    </div>\n                    {entry.performance.notes && (\n                      <div className=\"mt-2 text-sm text-gray-600\">\n                        <span className=\"font-medium\">Notes:</span> {entry.performance.notes}\n                      </div>\n                    )}\n                  </div>\n                ))}\n              </div>\n            )}\n          </div>\n        ) : (\n          /* Exercise Grid */\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8\">\n            {(activeTab === 'all' ? filteredExercises :\n              activeTab === 'recommended' ? recommendedExercises :\n              favoriteExercises).map(exercise => {\n            const isFavorite = favoriteExercises.some(fav => fav.id === exercise.id);\n\n            return (\n              <div\n                key={exercise.id}\n                className=\"bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow\"\n              >\n                <div className=\"p-6\">\n                  <div className=\"flex items-start justify-between mb-4\">\n                    <div className=\"flex-1\">\n                      <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">\n                        {exercise.name}\n                      </h3>\n                      <div className=\"flex items-center space-x-2 mb-3\">\n                        <span className={`px-3 py-1 rounded-full text-sm font-medium ${getDifficultyColor(exercise.difficulty)}`}>\n                          {exercise.difficulty}\n                        </span>\n                        <span className=\"text-xs text-gray-500\">\n                          {exerciseCategories[exercise.category]?.icon} {exerciseCategories[exercise.category]?.name}\n                        </span>\n                      </div>\n                    </div>\n                    <div className=\"flex space-x-2\">\n                      <button\n                        onClick={(e) => {\n                          e.stopPropagation();\n                          handleShowProgress(exercise.id);\n                        }}\n                        className=\"p-2 rounded-full text-gray-400 hover:text-blue-500 transition-colors\"\n                        title=\"Voir les statistiques\"\n                      >\n                        <FiBarChart2 className=\"h-5 w-5\" />\n                      </button>\n                      <button\n                        onClick={(e) => {\n                          e.stopPropagation();\n                          handleToggleFavorite(exercise.id);\n                        }}\n                        className={`p-2 rounded-full transition-colors ${\n                          isFavorite\n                            ? 'text-red-500 hover:text-red-600'\n                            : 'text-gray-400 hover:text-red-500'\n                        }`}\n                        title={isFavorite ? 'Retirer des favoris' : 'Ajouter aux favoris'}\n                      >\n                        <FiHeart className={`h-5 w-5 ${isFavorite ? 'fill-current' : ''}`} />\n                      </button>\n                    </div>\n                  </div>\n\n                  <p className=\"text-gray-600 mb-4 line-clamp-2\">\n                    {exercise.description}\n                  </p>\n\n                  {/* Muscle Groups */}\n                  <div className=\"flex flex-wrap gap-1 mb-4\">\n                    {exercise.muscleGroups.map(mg => (\n                      <span key={mg} className=\"px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full\">\n                        {muscleGroups[mg]?.name}\n                      </span>\n                    ))}\n                  </div>\n\n                  {/* Equipment */}\n                  <div className=\"flex items-center mb-4 text-sm text-gray-600\">\n                    <span className=\"mr-2\">{equipmentTypes[exercise.equipment]?.icon}</span>\n                    <span>{equipmentTypes[exercise.equipment]?.name}</span>\n                  </div>\n\n                  <div className=\"flex items-center justify-between text-sm text-gray-500 mb-4\">\n                    <div className=\"flex items-center\">\n                      <FiClock className=\"h-4 w-4 mr-1\" />\n                      {exercise.duration} min\n                    </div>\n                    <div className=\"flex items-center\">\n                      <FiZap className=\"h-4 w-4 mr-1\" />\n                      {exercise.calories} cal\n                    </div>\n                  </div>\n\n                  <div className=\"space-y-2\">\n                    <div className=\"flex gap-2\">\n                      <button\n                        onClick={() => setSelectedExercise(exercise)}\n                        className=\"flex-1 bg-gray-100 text-gray-700 py-2 px-3 rounded-lg hover:bg-gray-200 transition-colors flex items-center justify-center text-sm\"\n                      >\n                        <FiInfo className=\"h-4 w-4 mr-1\" />\n                        Détails\n                      </button>\n                      <button\n                        onClick={() => {\n                          setSelectedExercise(exercise);\n                          setShowPerformanceModal(true);\n                        }}\n                        className=\"flex-1 bg-blue-600 text-white py-2 px-3 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center text-sm\"\n                      >\n                        <FiPlay className=\"h-4 w-4 mr-1\" />\n                        Commencer\n                      </button>\n                    </div>\n                    <div className=\"flex gap-2\">\n                      <button\n                        onClick={(e) => {\n                          e.stopPropagation();\n                          handleToggleExerciseSelection(exercise);\n                        }}\n                        className={`flex-1 py-2 px-3 rounded-lg transition-colors flex items-center justify-center text-sm ${\n                          selectedExercisesForProgram.some(ex => ex.id === exercise.id)\n                            ? 'bg-purple-100 text-purple-700 border border-purple-300'\n                            : 'bg-gray-50 text-gray-600 hover:bg-gray-100'\n                        }`}\n                      >\n                        <FiBookmark className=\"h-4 w-4 mr-1\" />\n                        {selectedExercisesForProgram.some(ex => ex.id === exercise.id) ? 'Sélectionné' : 'Sélectionner'}\n                      </button>\n                      {getExerciseVideo(exercise.id) && (\n                        <button\n                          onClick={() => window.open(getExerciseVideo(exercise.id), '_blank')}\n                          className=\"flex-1 bg-green-100 text-green-700 py-2 px-3 rounded-lg hover:bg-green-200 transition-colors flex items-center justify-center text-sm\"\n                        >\n                          <FiVideo className=\"h-4 w-4 mr-1\" />\n                          Vidéo\n                        </button>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              </div>\n            );\n          })}\n          </div>\n        )}\n\n        {/* Empty State */}\n        {filteredExercises.length === 0 && (\n          <div className=\"text-center py-12\">\n            <div className=\"text-gray-400 mb-4\">\n              <FiSearch className=\"h-16 w-16 mx-auto\" />\n            </div>\n            <h3 className=\"text-lg font-medium text-gray-900 mb-2\">\n              Aucun exercice trouvé\n            </h3>\n            <p className=\"text-gray-600 mb-4\">\n              Essayez de modifier vos critères de recherche ou vos filtres.\n            </p>\n            <button\n              onClick={clearFilters}\n              className=\"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors\"\n            >\n              Effacer les filtres\n            </button>\n          </div>\n        )}\n\n        {/* Performance Modal */}\n        {showPerformanceModal && selectedExercise && (\n          <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\">\n            <div className=\"bg-white rounded-lg max-w-md w-full\">\n              <div className=\"p-6\">\n                <div className=\"flex items-center justify-between mb-6\">\n                  <h2 className=\"text-xl font-bold text-gray-900\">\n                    Enregistrer la performance\n                  </h2>\n                  <button\n                    onClick={() => setShowPerformanceModal(false)}\n                    className=\"text-gray-400 hover:text-gray-600\"\n                  >\n                    <FiX className=\"h-6 w-6\" />\n                  </button>\n                </div>\n\n                <div className=\"space-y-4\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Nombre de séries\n                    </label>\n                    <input\n                      type=\"number\"\n                      value={performanceData.sets}\n                      onChange={(e) => setPerformanceData(prev => ({...prev, sets: parseInt(e.target.value) || 0}))}\n                      className=\"w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    />\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Répétitions par série\n                    </label>\n                    <input\n                      type=\"number\"\n                      value={performanceData.reps}\n                      onChange={(e) => setPerformanceData(prev => ({...prev, reps: parseInt(e.target.value) || 0}))}\n                      className=\"w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    />\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Poids (kg) - optionnel\n                    </label>\n                    <input\n                      type=\"number\"\n                      value={performanceData.weight}\n                      onChange={(e) => setPerformanceData(prev => ({...prev, weight: e.target.value}))}\n                      className=\"w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                      placeholder=\"Ex: 20\"\n                    />\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Durée (min) - optionnel\n                    </label>\n                    <input\n                      type=\"number\"\n                      value={performanceData.duration}\n                      onChange={(e) => setPerformanceData(prev => ({...prev, duration: e.target.value}))}\n                      className=\"w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                      placeholder=\"Ex: 15\"\n                    />\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Notes - optionnel\n                    </label>\n                    <textarea\n                      value={performanceData.notes}\n                      onChange={(e) => setPerformanceData(prev => ({...prev, notes: e.target.value}))}\n                      className=\"w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                      rows=\"3\"\n                      placeholder=\"Comment vous êtes-vous senti ?\"\n                    />\n                  </div>\n                </div>\n\n                <div className=\"flex gap-3 mt-6\">\n                  <button\n                    onClick={() => setShowPerformanceModal(false)}\n                    className=\"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\"\n                  >\n                    Annuler\n                  </button>\n                  <button\n                    onClick={() => handleAddToHistory(selectedExercise)}\n                    className=\"flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n                  >\n                    Enregistrer\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Progress Modal */}\n        {showProgressModal && progressData && (\n          <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\">\n            <div className=\"bg-white rounded-lg max-w-lg w-full\">\n              <div className=\"p-6\">\n                <div className=\"flex items-center justify-between mb-6\">\n                  <h2 className=\"text-xl font-bold text-gray-900\">\n                    Statistiques de progression\n                  </h2>\n                  <button\n                    onClick={() => setShowProgressModal(false)}\n                    className=\"text-gray-400 hover:text-gray-600\"\n                  >\n                    <FiX className=\"h-6 w-6\" />\n                  </button>\n                </div>\n\n                <div className=\"space-y-4\">\n                  <div className=\"grid grid-cols-2 gap-4\">\n                    <div className=\"bg-blue-50 p-4 rounded-lg\">\n                      <div className=\"text-2xl font-bold text-blue-600\">{progressData.totalSessions}</div>\n                      <div className=\"text-sm text-gray-600\">Sessions totales</div>\n                    </div>\n                    <div className=\"bg-green-50 p-4 rounded-lg\">\n                      <div className=\"text-2xl font-bold text-green-600\">\n                        {progressData.lastPerformed ?\n                          new Date(progressData.lastPerformed).toLocaleDateString('fr-FR') :\n                          'Jamais'\n                        }\n                      </div>\n                      <div className=\"text-sm text-gray-600\">Dernière fois</div>\n                    </div>\n                  </div>\n\n                  {progressData.bestPerformance && (\n                    <div className=\"bg-yellow-50 p-4 rounded-lg\">\n                      <h3 className=\"font-semibold text-gray-900 mb-2\">Meilleure performance</h3>\n                      <div className=\"text-sm text-gray-600\">\n                        {progressData.bestPerformance.performance.sets} séries × {progressData.bestPerformance.performance.reps} reps\n                        {progressData.bestPerformance.performance.weight &&\n                          ` à ${progressData.bestPerformance.performance.weight} kg`\n                        }\n                      </div>\n                    </div>\n                  )}\n\n                  <div className=\"bg-gray-50 p-4 rounded-lg\">\n                    <h3 className=\"font-semibold text-gray-900 mb-2\">Tendance</h3>\n                    <div className={`flex items-center ${\n                      progressData.progressTrend === 'improving' ? 'text-green-600' :\n                      progressData.progressTrend === 'declining' ? 'text-red-600' :\n                      'text-gray-600'\n                    }`}>\n                      <FiTrendingUp className=\"h-4 w-4 mr-2\" />\n                      {progressData.progressTrend === 'improving' ? 'En progression' :\n                       progressData.progressTrend === 'declining' ? 'En baisse' :\n                       'Stable'}\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Create Program Modal */}\n        {showCreateProgram && (\n          <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\">\n            <div className=\"bg-white rounded-lg max-w-md w-full\">\n              <div className=\"p-6\">\n                <div className=\"flex items-center justify-between mb-6\">\n                  <h2 className=\"text-xl font-bold text-gray-900\">\n                    Créer un programme\n                  </h2>\n                  <button\n                    onClick={() => setShowCreateProgram(false)}\n                    className=\"text-gray-400 hover:text-gray-600\"\n                  >\n                    <FiX className=\"h-6 w-6\" />\n                  </button>\n                </div>\n\n                <div className=\"mb-4\">\n                  <p className=\"text-gray-600 mb-4\">\n                    {selectedExercisesForProgram.length} exercice(s) sélectionné(s)\n                  </p>\n\n                  {selectedExercisesForProgram.length > 0 && (\n                    <div className=\"space-y-2 mb-4 max-h-40 overflow-y-auto\">\n                      {selectedExercisesForProgram.map(exercise => (\n                        <div key={exercise.id} className=\"flex items-center justify-between bg-gray-50 p-2 rounded\">\n                          <span className=\"text-sm\">{exercise.name}</span>\n                          <button\n                            onClick={() => handleToggleExerciseSelection(exercise)}\n                            className=\"text-red-500 hover:text-red-700\"\n                          >\n                            <FiX className=\"h-4 w-4\" />\n                          </button>\n                        </div>\n                      ))}\n                    </div>\n                  )}\n\n                  <input\n                    type=\"text\"\n                    placeholder=\"Nom du programme\"\n                    className=\"w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    onKeyPress={(e) => {\n                      if (e.key === 'Enter') {\n                        handleCreateProgram(e.target.value);\n                      }\n                    }}\n                  />\n                </div>\n\n                <div className=\"flex gap-3\">\n                  <button\n                    onClick={() => setShowCreateProgram(false)}\n                    className=\"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\"\n                  >\n                    Annuler\n                  </button>\n                  <button\n                    onClick={(e) => {\n                      const input = e.target.parentElement.parentElement.querySelector('input');\n                      handleCreateProgram(input.value);\n                    }}\n                    className=\"flex-1 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors\"\n                  >\n                    Créer\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Exercise Detail Modal */}\n        {selectedExercise && !showPerformanceModal && (\n          <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\">\n            <div className=\"bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto\">\n              <div className=\"p-6\">\n                <div className=\"flex items-center justify-between mb-6\">\n                  <div>\n                    <h2 className=\"text-3xl font-bold text-gray-900 mb-2\">\n                      {selectedExercise.name}\n                    </h2>\n                    <div className=\"flex items-center space-x-3\">\n                      <span className={`px-3 py-1 rounded-full text-sm font-medium ${getDifficultyColor(selectedExercise.difficulty)}`}>\n                        {selectedExercise.difficulty}\n                      </span>\n                      <span className=\"text-gray-600\">\n                        {exerciseCategories[selectedExercise.category]?.icon} {exerciseCategories[selectedExercise.category]?.name}\n                      </span>\n                    </div>\n                  </div>\n                  <button\n                    onClick={() => setSelectedExercise(null)}\n                    className=\"text-gray-400 hover:text-gray-600 text-2xl\"\n                  >\n                    ✕\n                  </button>\n                </div>\n\n                <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n                  {/* Main Content */}\n                  <div className=\"lg:col-span-2\">\n                    <div className=\"mb-6\">\n                      <h3 className=\"text-xl font-semibold text-gray-900 mb-3\">Description</h3>\n                      <p className=\"text-gray-600 leading-relaxed\">{selectedExercise.description}</p>\n                    </div>\n\n                    <div className=\"mb-6\">\n                      <h3 className=\"text-xl font-semibold text-gray-900 mb-4\">Instructions détaillées</h3>\n                      <ol className=\"list-decimal list-inside space-y-3\">\n                        {selectedExercise.instructions.map((instruction, index) => (\n                          <li key={index} className=\"text-gray-600 leading-relaxed pl-2\">\n                            {instruction}\n                          </li>\n                        ))}\n                      </ol>\n                    </div>\n\n                    {selectedExercise.tips && (\n                      <div className=\"mb-6\">\n                        <h3 className=\"text-xl font-semibold text-gray-900 mb-4\">Conseils</h3>\n                        <ul className=\"space-y-2\">\n                          {selectedExercise.tips.map((tip, index) => (\n                            <li key={index} className=\"flex items-start text-gray-600\">\n                              <span className=\"text-blue-500 mr-2 mt-1\">•</span>\n                              {tip}\n                            </li>\n                          ))}\n                        </ul>\n                      </div>\n                    )}\n\n                    {selectedExercise.variations && (\n                      <div className=\"mb-6\">\n                        <h3 className=\"text-xl font-semibold text-gray-900 mb-4\">Variations</h3>\n                        <ul className=\"space-y-2\">\n                          {selectedExercise.variations.map((variation, index) => (\n                            <li key={index} className=\"flex items-start text-gray-600\">\n                              <span className=\"text-green-500 mr-2 mt-1\">•</span>\n                              {variation}\n                            </li>\n                          ))}\n                        </ul>\n                      </div>\n                    )}\n                  </div>\n\n                  {/* Sidebar */}\n                  <div className=\"lg:col-span-1\">\n                    <div className=\"bg-gray-50 rounded-lg p-6 mb-6\">\n                      <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Informations</h3>\n\n                      <div className=\"space-y-4\">\n                        <div className=\"flex justify-between\">\n                          <span className=\"text-gray-600\">Durée</span>\n                          <span className=\"font-medium\">{selectedExercise.duration} min</span>\n                        </div>\n                        <div className=\"flex justify-between\">\n                          <span className=\"text-gray-600\">Calories</span>\n                          <span className=\"font-medium\">{selectedExercise.calories} cal</span>\n                        </div>\n                        <div className=\"flex justify-between\">\n                          <span className=\"text-gray-600\">Équipement</span>\n                          <span className=\"font-medium\">{equipmentTypes[selectedExercise.equipment]?.name}</span>\n                        </div>\n                      </div>\n                    </div>\n\n                    <div className=\"bg-gray-50 rounded-lg p-6 mb-6\">\n                      <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Groupes musculaires</h3>\n                      <div className=\"flex flex-wrap gap-2\">\n                        {selectedExercise.muscleGroups.map(mg => (\n                          <span key={mg} className=\"px-3 py-1 bg-blue-100 text-blue-700 text-sm rounded-full\">\n                            {muscleGroups[mg]?.icon} {muscleGroups[mg]?.name}\n                          </span>\n                        ))}\n                      </div>\n                    </div>\n\n                    <div className=\"space-y-3\">\n                      <button\n                        onClick={() => toggleFavorite(selectedExercise.id)}\n                        className={`w-full py-3 px-6 rounded-lg font-medium transition-colors flex items-center justify-center ${\n                          favoriteExercises.some(fav => fav.id === selectedExercise.id)\n                            ? 'bg-red-100 text-red-700 hover:bg-red-200'\n                            : 'bg-gray-100 text-gray-700 hover:bg-gray-200'\n                        }`}\n                      >\n                        <FiHeart className=\"h-5 w-5 mr-2\" />\n                        {favoriteExercises.some(fav => fav.id === selectedExercise.id) ? 'Retirer des favoris' : 'Ajouter aux favoris'}\n                      </button>\n\n                      <button className=\"w-full bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center font-medium\">\n                        <FiPlay className=\"h-5 w-5 mr-2\" />\n                        Commencer l'exercice\n                      </button>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default Exercises;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,MAAM,EACNC,OAAO,EACPC,KAAK,EACLC,QAAQ,EACRC,QAAQ,EACRC,QAAQ,EACRC,OAAO,EACPC,MAAM,EACNC,MAAM,EACNC,MAAM,EACNC,YAAY,EACZC,UAAU,EACVC,OAAO,EACPC,WAAW,EACXC,UAAU,EACVC,GAAG,QAEE,gBAAgB;AACvB,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SACEC,SAAS,EACTC,kBAAkB,EAClBC,YAAY,EACZC,cAAc,EACdC,kBAAkB,EAClBC,uBAAuB,EACvBC,eAAe,EACfC,iBAAiB,EACjBC,cAAc,EACdC,aAAa,EACbC,YAAY,EACZC,kBAAkB,EAClBC,sBAAsB,EACtBC,mBAAmB,EACnBC,gBAAgB,QACX,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEhC,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA;EACtB,MAAM;IAAEC;EAAK,CAAC,GAAGzB,OAAO,CAAC,CAAC;EAE1B,MAAM,CAAC0B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG7C,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAAC8C,WAAW,EAAEC,cAAc,CAAC,GAAG/C,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACgD,WAAW,EAAEC,cAAc,CAAC,GAAGjD,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACkD,OAAO,EAAEC,UAAU,CAAC,GAAGnD,QAAQ,CAAC;IACrCoD,QAAQ,EAAE,KAAK;IACfC,WAAW,EAAE,KAAK;IAClBC,SAAS,EAAE,KAAK;IAChBC,UAAU,EAAE,KAAK;IACjBC,WAAW,EAAE;EACf,CAAC,CAAC;EACF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG1D,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC2D,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG5D,QAAQ,CAAC,EAAE,CAAC;EACpE,MAAM,CAAC6D,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG9D,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAAC+D,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGhE,QAAQ,CAACmB,SAAS,CAAC;;EAErE;EACA,MAAM,CAAC8C,eAAe,EAAEC,kBAAkB,CAAC,GAAGlE,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACmE,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGpE,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACqE,2BAA2B,EAAEC,8BAA8B,CAAC,GAAGtE,QAAQ,CAAC,EAAE,CAAC;EAClF,MAAM,CAACuE,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGxE,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACyE,YAAY,EAAEC,eAAe,CAAC,GAAG1E,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC2E,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG5E,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAAC6E,eAAe,EAAEC,kBAAkB,CAAC,GAAG9E,QAAQ,CAAC;IACrD+E,IAAI,EAAE,CAAC;IACPC,IAAI,EAAE,EAAE;IACRC,MAAM,EAAE,EAAE;IACVC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGrF,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1D,MAAM,CAACsF,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvF,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5D,MAAM,CAACwF,eAAe,EAAEC,kBAAkB,CAAC,GAAGzF,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC0F,cAAc,EAAEC,iBAAiB,CAAC,GAAG3F,QAAQ,CAAC,IAAI,CAAC;EAE1DC,SAAS,CAAC,MAAM;IACd;IACA,IAAI0C,IAAI,EAAE;MACR,MAAMiD,eAAe,GAAGpE,uBAAuB,CAACmB,IAAI,EAAEA,IAAI,CAACkD,WAAW,IAAI,iBAAiB,CAAC;MAC5FjC,uBAAuB,CAACgC,eAAe,CAAC;IAC1C;;IAEA;IACA,MAAME,cAAc,GAAGlE,aAAa,CAAC,CAAC;IACtC,MAAMmE,uBAAuB,GAAG5E,SAAS,CAAC6E,MAAM,CAACC,EAAE,IAAIH,cAAc,CAACI,QAAQ,CAACD,EAAE,CAACE,EAAE,CAAC,CAAC;IACtFrC,oBAAoB,CAACiC,uBAAuB,CAAC;;IAE7C;IACA,MAAMK,OAAO,GAAGtE,kBAAkB,CAAC,CAAC;IACpCoC,kBAAkB,CAACkC,OAAO,CAAC;EAC7B,CAAC,EAAE,CAACzD,IAAI,CAAC,CAAC;EAEV1C,SAAS,CAAC,MAAM;IACd;IACA,IAAIoG,OAAO,GAAGlF,SAAS;IAEvB,IAAIsC,SAAS,KAAK,aAAa,EAAE;MAC/B4C,OAAO,GAAG1C,oBAAoB;IAChC,CAAC,MAAM,IAAIF,SAAS,KAAK,WAAW,EAAE;MACpC4C,OAAO,GAAGxC,iBAAiB;IAC7B;IAEA,IAAIf,WAAW,IAAIwD,MAAM,CAACC,MAAM,CAACrD,OAAO,CAAC,CAACsD,IAAI,CAACC,CAAC,IAAIA,CAAC,KAAK,KAAK,IAAIA,CAAC,KAAK,IAAI,CAAC,EAAE;MAC9EJ,OAAO,GAAG5E,eAAe,CAACqB,WAAW,EAAEI,OAAO,CAAC;IACjD;IAEAc,oBAAoB,CAACqC,OAAO,CAAC;EAC/B,CAAC,EAAE,CAACvD,WAAW,EAAEI,OAAO,EAAEO,SAAS,EAAEE,oBAAoB,EAAEE,iBAAiB,CAAC,CAAC;EAE9E,MAAM6C,kBAAkB,GAAGA,CAACC,UAAU,EAAEC,KAAK,KAAK;IAChDzD,UAAU,CAAC0D,IAAI,KAAK;MAClB,GAAGA,IAAI;MACP,CAACF,UAAU,GAAGC;IAChB,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAME,YAAY,GAAGA,CAAA,KAAM;IACzB3D,UAAU,CAAC;MACTC,QAAQ,EAAE,KAAK;MACfC,WAAW,EAAE,KAAK;MAClBC,SAAS,EAAE,KAAK;MAChBC,UAAU,EAAE,KAAK;MACjBC,WAAW,EAAE;IACf,CAAC,CAAC;IACFT,cAAc,CAAC,EAAE,CAAC;EACpB,CAAC;EAED,MAAMgE,oBAAoB,GAAGA,CAAA,KAAM;IACjC,MAAMC,iBAAiB,GAAGjD,iBAAiB,CAACkD,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;IACvD,MAAMC,OAAO,GAAGxF,iBAAiB,CAACsF,iBAAiB,EAAE,EAAE,CAAC;;IAExD;IACAG,KAAK,CAAC,8BAA8BD,OAAO,CAACE,aAAa,4BAA4BF,OAAO,CAACG,iBAAiB,gBAAgBH,OAAO,CAAC/F,SAAS,CAACmG,MAAM,EAAE,CAAC;EAC3J,CAAC;EAED,MAAMC,oBAAoB,GAAIC,UAAU,IAAK;IAC3C,MAAM1B,cAAc,GAAGlE,aAAa,CAAC,CAAC;IACtC,MAAM6F,aAAa,GAAG9F,cAAc,CAAC6F,UAAU,EAAE1B,cAAc,EAAE,MAAM,CAAC,CAAC,CAAC;;IAE1E;IACA,IAAI2B,aAAa,EAAE;MACjB,MAAMC,QAAQ,GAAGvG,SAAS,CAACwG,IAAI,CAAC1B,EAAE,IAAIA,EAAE,CAACE,EAAE,KAAKqB,UAAU,CAAC;MAC3D1D,oBAAoB,CAAC+C,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEa,QAAQ,CAAC,CAAC;IACnD,CAAC,MAAM;MACL5D,oBAAoB,CAAC+C,IAAI,IAAIA,IAAI,CAACb,MAAM,CAACC,EAAE,IAAIA,EAAE,CAACE,EAAE,KAAKqB,UAAU,CAAC,CAAC;IACvE;EACF,CAAC;;EAED;EACA,MAAMI,kBAAkB,GAAIF,QAAQ,IAAK;IACvC,MAAMG,KAAK,GAAGhG,YAAY,CAAC6F,QAAQ,EAAE7C,eAAe,CAAC;IACrD,IAAIgD,KAAK,EAAE;MACT3D,kBAAkB,CAAC2C,IAAI,IAAI,CAACgB,KAAK,EAAE,GAAGhB,IAAI,CAAC,CAAC;MAC5CjC,uBAAuB,CAAC,KAAK,CAAC;MAC9BE,kBAAkB,CAAC;QACjBC,IAAI,EAAE,CAAC;QACPC,IAAI,EAAE,EAAE;QACRC,MAAM,EAAE,EAAE;QACVC,QAAQ,EAAE,EAAE;QACZC,KAAK,EAAE;MACT,CAAC,CAAC;MACFgC,KAAK,CAAC,sCAAsC,CAAC;IAC/C;EACF,CAAC;;EAED;EACA,MAAMW,kBAAkB,GAAIN,UAAU,IAAK;IACzC,MAAMO,KAAK,GAAGhG,sBAAsB,CAACyF,UAAU,CAAC;IAChD9C,eAAe,CAACqD,KAAK,CAAC;IACtBvD,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMwD,6BAA6B,GAAIN,QAAQ,IAAK;IAClDpD,8BAA8B,CAACuC,IAAI,IAAI;MACrC,MAAMoB,UAAU,GAAGpB,IAAI,CAACL,IAAI,CAACP,EAAE,IAAIA,EAAE,CAACE,EAAE,KAAKuB,QAAQ,CAACvB,EAAE,CAAC;MACzD,IAAI8B,UAAU,EAAE;QACd,OAAOpB,IAAI,CAACb,MAAM,CAACC,EAAE,IAAIA,EAAE,CAACE,EAAE,KAAKuB,QAAQ,CAACvB,EAAE,CAAC;MACjD,CAAC,MAAM;QACL,OAAO,CAAC,GAAGU,IAAI,EAAEa,QAAQ,CAAC;MAC5B;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMQ,mBAAmB,GAAIC,WAAW,IAAK;IAC3C,IAAI9D,2BAA2B,CAACiD,MAAM,KAAK,CAAC,EAAE;MAC5CH,KAAK,CAAC,4CAA4C,CAAC;MACnD;IACF;IAEA,MAAMiB,OAAO,GAAGpG,mBAAmB,CAACmG,WAAW,EAAE9D,2BAA2B,CAAC;IAC7EC,8BAA8B,CAAC,EAAE,CAAC;IAClCF,oBAAoB,CAAC,KAAK,CAAC;IAC3B+C,KAAK,CAAC,cAAciB,OAAO,CAACC,IAAI,sBAAsB,CAAC;EACzD,CAAC;EAED,oBACElG,OAAA;IAAKmG,SAAS,EAAC,yBAAyB;IAAAC,QAAA,eACtCpG,OAAA;MAAKmG,SAAS,EAAC,6CAA6C;MAAAC,QAAA,gBAE1DpG,OAAA;QAAKmG,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BpG,OAAA;UAAImG,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAEtD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLxG,OAAA;UAAGmG,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EAAC;QAGvD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGNxG,OAAA;QAAKmG,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDpG,OAAA;UAAKmG,SAAS,EAAC,8CAA8C;UAAAC,QAAA,gBAC3DpG,OAAA;YAAKmG,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9BpG,OAAA,CAAC7B,QAAQ;cAACgI,SAAS,EAAC;YAA6C;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpExG,OAAA;cACEyG,IAAI,EAAC,MAAM;cACXC,WAAW,EAAC,8CAA8C;cAC1DjC,KAAK,EAAE9D,WAAY;cACnBgG,QAAQ,EAAGC,CAAC,IAAKhG,cAAc,CAACgG,CAAC,CAACC,MAAM,CAACpC,KAAK,CAAE;cAChD0B,SAAS,EAAC;YAA8G;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNxG,OAAA;YAAKmG,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnCpG,OAAA;cACE8G,OAAO,EAAEA,CAAA,KAAMhG,cAAc,CAAC,CAACD,WAAW,CAAE;cAC5CsF,SAAS,EAAE,wEACTtF,WAAW,GAAG,wBAAwB,GAAG,6CAA6C,EACrF;cAAAuF,QAAA,gBAEHpG,OAAA,CAAC9B,QAAQ;gBAACiI,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,WAEvC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTxG,OAAA;cACE8G,OAAO,EAAElC,oBAAqB;cAC9BuB,SAAS,EAAC,qGAAqG;cAAAC,QAAA,gBAE/GpG,OAAA,CAAC5B,QAAQ;gBAAC+H,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,oBAEvC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTxG,OAAA;cACE8G,OAAO,EAAEA,CAAA,KAAM7E,oBAAoB,CAAC,IAAI,CAAE;cAC1CkE,SAAS,EAAC,uGAAuG;cAAAC,QAAA,gBAEjHpG,OAAA,CAACxB,MAAM;gBAAC2H,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,sBAErC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTxG,OAAA;cACE8G,OAAO,EAAEA,CAAA,KAAMvF,YAAY,CAAC,SAAS,CAAE;cACvC4E,SAAS,EAAC,uGAAuG;cAAAC,QAAA,gBAEjHpG,OAAA,CAACnB,UAAU;gBAACsH,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,cAEzC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGL3F,WAAW,iBACVb,OAAA;UAAKmG,SAAS,EAAC,oCAAoC;UAAAC,QAAA,gBACjDpG,OAAA;YAAKmG,SAAS,EAAC,sDAAsD;YAAAC,QAAA,gBACnEpG,OAAA;cAAAoG,QAAA,gBACEpG,OAAA;gBAAOmG,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACjFxG,OAAA;gBACEyE,KAAK,EAAE1D,OAAO,CAACE,QAAS;gBACxB0F,QAAQ,EAAGC,CAAC,IAAKrC,kBAAkB,CAAC,UAAU,EAAEqC,CAAC,CAACC,MAAM,CAACpC,KAAK,CAAE;gBAChE0B,SAAS,EAAC,kGAAkG;gBAAAC,QAAA,gBAE5GpG,OAAA;kBAAQyE,KAAK,EAAC,KAAK;kBAAA2B,QAAA,EAAC;gBAAqB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EACjDrC,MAAM,CAAC4C,OAAO,CAAC9H,kBAAkB,CAAC,CAAC+H,GAAG,CAAC,CAAC,CAACC,GAAG,EAAEhG,QAAQ,CAAC,kBACtDjB,OAAA;kBAAkByE,KAAK,EAAEwC,GAAI;kBAAAb,QAAA,EAAEnF,QAAQ,CAACiF;gBAAI,GAA/Be,GAAG;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAqC,CACtD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENxG,OAAA;cAAAoG,QAAA,gBACEpG,OAAA;gBAAOmG,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACzFxG,OAAA;gBACEyE,KAAK,EAAE1D,OAAO,CAACG,WAAY;gBAC3ByF,QAAQ,EAAGC,CAAC,IAAKrC,kBAAkB,CAAC,aAAa,EAAEqC,CAAC,CAACC,MAAM,CAACpC,KAAK,CAAE;gBACnE0B,SAAS,EAAC,kGAAkG;gBAAAC,QAAA,gBAE5GpG,OAAA;kBAAQyE,KAAK,EAAC,KAAK;kBAAA2B,QAAA,EAAC;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EAC5CrC,MAAM,CAAC4C,OAAO,CAAC7H,YAAY,CAAC,CAAC8H,GAAG,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,kBAC7ClH,OAAA;kBAAkByE,KAAK,EAAEwC,GAAI;kBAAAb,QAAA,EAAEc,KAAK,CAAChB;gBAAI,GAA5Be,GAAG;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAkC,CACnD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENxG,OAAA;cAAAoG,QAAA,gBACEpG,OAAA;gBAAOmG,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAClFxG,OAAA;gBACEyE,KAAK,EAAE1D,OAAO,CAACI,SAAU;gBACzBwF,QAAQ,EAAGC,CAAC,IAAKrC,kBAAkB,CAAC,WAAW,EAAEqC,CAAC,CAACC,MAAM,CAACpC,KAAK,CAAE;gBACjE0B,SAAS,EAAC,kGAAkG;gBAAAC,QAAA,gBAE5GpG,OAAA;kBAAQyE,KAAK,EAAC,KAAK;kBAAA2B,QAAA,EAAC;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EAC3CrC,MAAM,CAAC4C,OAAO,CAAC5H,cAAc,CAAC,CAAC6H,GAAG,CAAC,CAAC,CAACC,GAAG,EAAE9F,SAAS,CAAC,kBACnDnB,OAAA;kBAAkByE,KAAK,EAAEwC,GAAI;kBAAAb,QAAA,EAAEjF,SAAS,CAAC+E;gBAAI,GAAhCe,GAAG;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAsC,CACvD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENxG,OAAA;cAAAoG,QAAA,gBACEpG,OAAA;gBAAOmG,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAClFxG,OAAA;gBACEyE,KAAK,EAAE1D,OAAO,CAACK,UAAW;gBAC1BuF,QAAQ,EAAGC,CAAC,IAAKrC,kBAAkB,CAAC,YAAY,EAAEqC,CAAC,CAACC,MAAM,CAACpC,KAAK,CAAE;gBAClE0B,SAAS,EAAC,kGAAkG;gBAAAC,QAAA,gBAE5GpG,OAAA;kBAAQyE,KAAK,EAAC,KAAK;kBAAA2B,QAAA,EAAC;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC/CxG,OAAA;kBAAQyE,KAAK,EAAC,QAAQ;kBAAA2B,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtCxG,OAAA;kBAAQyE,KAAK,EAAC,cAAQ;kBAAA2B,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtCxG,OAAA;kBAAQyE,KAAK,EAAC,WAAW;kBAAA2B,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENxG,OAAA;YAAKmG,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACrDpG,OAAA;cAAKmG,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1CpG,OAAA;gBAAOmG,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACnFxG,OAAA;gBACEyG,IAAI,EAAC,QAAQ;gBACbhC,KAAK,EAAE1D,OAAO,CAACM,WAAW,IAAI,EAAG;gBACjCsF,QAAQ,EAAGC,CAAC,IAAKrC,kBAAkB,CAAC,aAAa,EAAEqC,CAAC,CAACC,MAAM,CAACpC,KAAK,GAAG0C,QAAQ,CAACP,CAAC,CAACC,MAAM,CAACpC,KAAK,CAAC,GAAG,IAAI,CAAE;gBACrGiC,WAAW,EAAC,IAAI;gBAChBP,SAAS,EAAC;cAAgG;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3G,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNxG,OAAA;cACE8G,OAAO,EAAEnC,YAAa;cACtBwB,SAAS,EAAC,+DAA+D;cAAAC,QAAA,EAC1E;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNxG,OAAA;QAAKmG,SAAS,EAAC,gDAAgD;QAAAC,QAAA,gBAC7DpG,OAAA;UACE8G,OAAO,EAAEA,CAAA,KAAMvF,YAAY,CAAC,KAAK,CAAE;UACnC4E,SAAS,EAAE,qEACT7E,SAAS,KAAK,KAAK,GACf,kCAAkC,GAClC,mCAAmC,EACtC;UAAA8E,QAAA,GACJ,QACO,EAACpH,SAAS,CAACmG,MAAM,EAAC,GAC1B;QAAA;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EACRhG,IAAI,iBACHR,OAAA,CAAAE,SAAA;UAAAkG,QAAA,gBACEpG,OAAA;YACE8G,OAAO,EAAEA,CAAA,KAAMvF,YAAY,CAAC,aAAa,CAAE;YAC3C4E,SAAS,EAAE,qEACT7E,SAAS,KAAK,aAAa,GACvB,kCAAkC,GAClC,mCAAmC,EACtC;YAAA8E,QAAA,gBAEHpG,OAAA,CAAC1B,MAAM;cAAC6H,SAAS,EAAC;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,oBACrB,EAAChF,oBAAoB,CAAC2D,MAAM,EAAC,GAC5C;UAAA;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTxG,OAAA;YACE8G,OAAO,EAAEA,CAAA,KAAMvF,YAAY,CAAC,WAAW,CAAE;YACzC4E,SAAS,EAAE,qEACT7E,SAAS,KAAK,WAAW,GACrB,kCAAkC,GAClC,mCAAmC,EACtC;YAAA8E,QAAA,gBAEHpG,OAAA,CAAC3B,OAAO;cAAC8H,SAAS,EAAC;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,aAC1B,EAAC9E,iBAAiB,CAACyD,MAAM,EAAC,GACrC;UAAA;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTxG,OAAA;YACE8G,OAAO,EAAEA,CAAA,KAAMvF,YAAY,CAAC,SAAS,CAAE;YACvC4E,SAAS,EAAE,qEACT7E,SAAS,KAAK,SAAS,GACnB,kCAAkC,GAClC,mCAAmC,EACtC;YAAA8E,QAAA,gBAEHpG,OAAA,CAACnB,UAAU;cAACsH,SAAS,EAAC;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAC1B,EAAC1E,eAAe,CAACqD,MAAM,EAAC,GACtC;UAAA;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA,eACT,CACH;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNxG,OAAA;QAAKmG,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnBpG,OAAA;UAAGmG,SAAS,EAAC,eAAe;UAAAC,QAAA,GACzBxE,iBAAiB,CAACuD,MAAM,EAAC,WAAS,EAACvD,iBAAiB,CAACuD,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAC,YAAO,EAACvD,iBAAiB,CAACuD,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAC1HxE,WAAW,IAAI,UAAUA,WAAW,GAAG;QAAA;UAAA0F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,EAGLlF,SAAS,KAAK,SAAS;MAAA;MACtB;MACAtB,OAAA;QAAKmG,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDpG,OAAA;UAAImG,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAwB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAClF1E,eAAe,CAACqD,MAAM,KAAK,CAAC,gBAC3BnF,OAAA;UAAKmG,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCpG,OAAA,CAACnB,UAAU;YAACsH,SAAS,EAAC;UAAsC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/DxG,OAAA;YAAImG,SAAS,EAAC,wCAAwC;YAAAC,QAAA,EAAC;UAAgC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5FxG,OAAA;YAAGmG,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAsD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpF,CAAC,gBAENxG,OAAA;UAAKmG,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvBtE,eAAe,CAACgD,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAACkC,GAAG,CAACtB,KAAK,iBACrC1F,OAAA;YAAoBmG,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACnEpG,OAAA;cAAKmG,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrDpG,OAAA;gBAAImG,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,EAAEV,KAAK,CAAC0B;cAAY;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACrExG,OAAA;gBAAMmG,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EACpC,IAAIiB,IAAI,CAAC3B,KAAK,CAAC4B,IAAI,CAAC,CAACC,kBAAkB,CAAC,OAAO;cAAC;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNxG,OAAA;cAAKmG,SAAS,EAAC,+CAA+C;cAAAC,QAAA,gBAC5DpG,OAAA;gBAAAoG,QAAA,gBACEpG,OAAA;kBAAMmG,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC9CxG,OAAA;kBAAMmG,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,EAAEV,KAAK,CAAC8B,WAAW,CAAC5E;gBAAI;kBAAAyD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D,CAAC,eACNxG,OAAA;gBAAAoG,QAAA,gBACEpG,OAAA;kBAAMmG,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACnDxG,OAAA;kBAAMmG,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,EAAEV,KAAK,CAAC8B,WAAW,CAAC3E;gBAAI;kBAAAwD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D,CAAC,EACLd,KAAK,CAAC8B,WAAW,CAAC1E,MAAM,iBACvB9C,OAAA;gBAAAoG,QAAA,gBACEpG,OAAA;kBAAMmG,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC7CxG,OAAA;kBAAMmG,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,GAAEV,KAAK,CAAC8B,WAAW,CAAC1E,MAAM,EAAC,KAAG;gBAAA;kBAAAuD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpE,CACN,EACAd,KAAK,CAAC8B,WAAW,CAACzE,QAAQ,iBACzB/C,OAAA;gBAAAoG,QAAA,gBACEpG,OAAA;kBAAMmG,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC7CxG,OAAA;kBAAMmG,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,GAAEV,KAAK,CAAC8B,WAAW,CAACzE,QAAQ,EAAC,MAAI;gBAAA;kBAAAsD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvE,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,EACLd,KAAK,CAAC8B,WAAW,CAACxE,KAAK,iBACtBhD,OAAA;cAAKmG,SAAS,EAAC,4BAA4B;cAAAC,QAAA,gBACzCpG,OAAA;gBAAMmG,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,KAAC,EAACd,KAAK,CAAC8B,WAAW,CAACxE,KAAK;YAAA;cAAAqD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjE,CACN;UAAA,GAjCOd,KAAK,CAAC1B,EAAE;YAAAqC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAkCb,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;MAAA;MAEN;MACAxG,OAAA;QAAKmG,SAAS,EAAC,2DAA2D;QAAAC,QAAA,EACvE,CAAC9E,SAAS,KAAK,KAAK,GAAGM,iBAAiB,GACvCN,SAAS,KAAK,aAAa,GAAGE,oBAAoB,GAClDE,iBAAiB,EAAEsF,GAAG,CAACzB,QAAQ,IAAI;UAAA,IAAAkC,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA;UACrC,MAAMC,UAAU,GAAGnG,iBAAiB,CAAC2C,IAAI,CAACyD,GAAG,IAAIA,GAAG,CAAC9D,EAAE,KAAKuB,QAAQ,CAACvB,EAAE,CAAC;UAExE,oBACEhE,OAAA;YAEEmG,SAAS,EAAC,iFAAiF;YAAAC,QAAA,eAE3FpG,OAAA;cAAKmG,SAAS,EAAC,KAAK;cAAAC,QAAA,gBAClBpG,OAAA;gBAAKmG,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,gBACpDpG,OAAA;kBAAKmG,SAAS,EAAC,QAAQ;kBAAAC,QAAA,gBACrBpG,OAAA;oBAAImG,SAAS,EAAC,0CAA0C;oBAAAC,QAAA,EACrDb,QAAQ,CAACW;kBAAI;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACZ,CAAC,eACLxG,OAAA;oBAAKmG,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,gBAC/CpG,OAAA;sBAAMmG,SAAS,EAAE,8CAA8C/G,kBAAkB,CAACmG,QAAQ,CAACnE,UAAU,CAAC,EAAG;sBAAAgF,QAAA,EACtGb,QAAQ,CAACnE;oBAAU;sBAAAiF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChB,CAAC,eACPxG,OAAA;sBAAMmG,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,IAAAqB,qBAAA,GACpCxI,kBAAkB,CAACsG,QAAQ,CAACtE,QAAQ,CAAC,cAAAwG,qBAAA,uBAArCA,qBAAA,CAAuCM,IAAI,EAAC,GAAC,GAAAL,sBAAA,GAACzI,kBAAkB,CAACsG,QAAQ,CAACtE,QAAQ,CAAC,cAAAyG,sBAAA,uBAArCA,sBAAA,CAAuCxB,IAAI;oBAAA;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNxG,OAAA;kBAAKmG,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC7BpG,OAAA;oBACE8G,OAAO,EAAGF,CAAC,IAAK;sBACdA,CAAC,CAACoB,eAAe,CAAC,CAAC;sBACnBrC,kBAAkB,CAACJ,QAAQ,CAACvB,EAAE,CAAC;oBACjC,CAAE;oBACFmC,SAAS,EAAC,sEAAsE;oBAChF8B,KAAK,EAAC,uBAAuB;oBAAA7B,QAAA,eAE7BpG,OAAA,CAACpB,WAAW;sBAACuH,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7B,CAAC,eACTxG,OAAA;oBACE8G,OAAO,EAAGF,CAAC,IAAK;sBACdA,CAAC,CAACoB,eAAe,CAAC,CAAC;sBACnB5C,oBAAoB,CAACG,QAAQ,CAACvB,EAAE,CAAC;oBACnC,CAAE;oBACFmC,SAAS,EAAE,sCACT0B,UAAU,GACN,iCAAiC,GACjC,kCAAkC,EACrC;oBACHI,KAAK,EAAEJ,UAAU,GAAG,qBAAqB,GAAG,qBAAsB;oBAAAzB,QAAA,eAElEpG,OAAA,CAAC3B,OAAO;sBAAC8H,SAAS,EAAE,WAAW0B,UAAU,GAAG,cAAc,GAAG,EAAE;oBAAG;sBAAAxB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/D,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENxG,OAAA;gBAAGmG,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,EAC3Cb,QAAQ,CAAC2C;cAAW;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC,eAGJxG,OAAA;gBAAKmG,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EACvCb,QAAQ,CAACrG,YAAY,CAAC8H,GAAG,CAACmB,EAAE;kBAAA,IAAAC,gBAAA;kBAAA,oBAC3BpI,OAAA;oBAAemG,SAAS,EAAC,0DAA0D;oBAAAC,QAAA,GAAAgC,gBAAA,GAChFlJ,YAAY,CAACiJ,EAAE,CAAC,cAAAC,gBAAA,uBAAhBA,gBAAA,CAAkBlC;kBAAI,GADdiC,EAAE;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEP,CAAC;gBAAA,CACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAGNxG,OAAA;gBAAKmG,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,gBAC3DpG,OAAA;kBAAMmG,SAAS,EAAC,MAAM;kBAAAC,QAAA,GAAAuB,qBAAA,GAAExI,cAAc,CAACoG,QAAQ,CAACpE,SAAS,CAAC,cAAAwG,qBAAA,uBAAlCA,qBAAA,CAAoCI;gBAAI;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACxExG,OAAA;kBAAAoG,QAAA,GAAAwB,sBAAA,GAAOzI,cAAc,CAACoG,QAAQ,CAACpE,SAAS,CAAC,cAAAyG,sBAAA,uBAAlCA,sBAAA,CAAoC1B;gBAAI;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC,eAENxG,OAAA;gBAAKmG,SAAS,EAAC,8DAA8D;gBAAAC,QAAA,gBAC3EpG,OAAA;kBAAKmG,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChCpG,OAAA,CAAChC,OAAO;oBAACmI,SAAS,EAAC;kBAAc;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EACnCjB,QAAQ,CAACxC,QAAQ,EAAC,MACrB;gBAAA;kBAAAsD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNxG,OAAA;kBAAKmG,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChCpG,OAAA,CAAC/B,KAAK;oBAACkI,SAAS,EAAC;kBAAc;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EACjCjB,QAAQ,CAAC8C,QAAQ,EAAC,MACrB;gBAAA;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENxG,OAAA;gBAAKmG,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxBpG,OAAA;kBAAKmG,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBpG,OAAA;oBACE8G,OAAO,EAAEA,CAAA,KAAMpG,mBAAmB,CAAC6E,QAAQ,CAAE;oBAC7CY,SAAS,EAAC,oIAAoI;oBAAAC,QAAA,gBAE9IpG,OAAA,CAACzB,MAAM;sBAAC4H,SAAS,EAAC;oBAAc;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,cAErC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACTxG,OAAA;oBACE8G,OAAO,EAAEA,CAAA,KAAM;sBACbpG,mBAAmB,CAAC6E,QAAQ,CAAC;sBAC7B9C,uBAAuB,CAAC,IAAI,CAAC;oBAC/B,CAAE;oBACF0D,SAAS,EAAC,iIAAiI;oBAAAC,QAAA,gBAE3IpG,OAAA,CAACjC,MAAM;sBAACoI,SAAS,EAAC;oBAAc;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,aAErC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACNxG,OAAA;kBAAKmG,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBpG,OAAA;oBACE8G,OAAO,EAAGF,CAAC,IAAK;sBACdA,CAAC,CAACoB,eAAe,CAAC,CAAC;sBACnBnC,6BAA6B,CAACN,QAAQ,CAAC;oBACzC,CAAE;oBACFY,SAAS,EAAE,0FACTjE,2BAA2B,CAACmC,IAAI,CAACP,EAAE,IAAIA,EAAE,CAACE,EAAE,KAAKuB,QAAQ,CAACvB,EAAE,CAAC,GACzD,wDAAwD,GACxD,4CAA4C,EAC/C;oBAAAoC,QAAA,gBAEHpG,OAAA,CAACtB,UAAU;sBAACyH,SAAS,EAAC;oBAAc;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EACtCtE,2BAA2B,CAACmC,IAAI,CAACP,EAAE,IAAIA,EAAE,CAACE,EAAE,KAAKuB,QAAQ,CAACvB,EAAE,CAAC,GAAG,aAAa,GAAG,cAAc;kBAAA;oBAAAqC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzF,CAAC,EACR1G,gBAAgB,CAACyF,QAAQ,CAACvB,EAAE,CAAC,iBAC5BhE,OAAA;oBACE8G,OAAO,EAAEA,CAAA,KAAMwB,MAAM,CAACC,IAAI,CAACzI,gBAAgB,CAACyF,QAAQ,CAACvB,EAAE,CAAC,EAAE,QAAQ,CAAE;oBACpEmC,SAAS,EAAC,uIAAuI;oBAAAC,QAAA,gBAEjJpG,OAAA,CAACrB,OAAO;sBAACwH,SAAS,EAAC;oBAAc;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,YAEtC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CACT;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC,GA1HDjB,QAAQ,CAACvB,EAAE;YAAAqC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA2Hb,CAAC;QAEV,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CACN,EAGA5E,iBAAiB,CAACuD,MAAM,KAAK,CAAC,iBAC7BnF,OAAA;QAAKmG,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCpG,OAAA;UAAKmG,SAAS,EAAC,oBAAoB;UAAAC,QAAA,eACjCpG,OAAA,CAAC7B,QAAQ;YAACgI,SAAS,EAAC;UAAmB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC,eACNxG,OAAA;UAAImG,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAEvD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLxG,OAAA;UAAGmG,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAElC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJxG,OAAA;UACE8G,OAAO,EAAEnC,YAAa;UACtBwB,SAAS,EAAC,iFAAiF;UAAAC,QAAA,EAC5F;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN,EAGAhE,oBAAoB,IAAI/B,gBAAgB,iBACvCT,OAAA;QAAKmG,SAAS,EAAC,gFAAgF;QAAAC,QAAA,eAC7FpG,OAAA;UAAKmG,SAAS,EAAC,qCAAqC;UAAAC,QAAA,eAClDpG,OAAA;YAAKmG,SAAS,EAAC,KAAK;YAAAC,QAAA,gBAClBpG,OAAA;cAAKmG,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrDpG,OAAA;gBAAImG,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,EAAC;cAEhD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLxG,OAAA;gBACE8G,OAAO,EAAEA,CAAA,KAAMrE,uBAAuB,CAAC,KAAK,CAAE;gBAC9C0D,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,eAE7CpG,OAAA,CAAClB,GAAG;kBAACqH,SAAS,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENxG,OAAA;cAAKmG,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBpG,OAAA;gBAAAoG,QAAA,gBACEpG,OAAA;kBAAOmG,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRxG,OAAA;kBACEyG,IAAI,EAAC,QAAQ;kBACbhC,KAAK,EAAE/B,eAAe,CAACE,IAAK;kBAC5B+D,QAAQ,EAAGC,CAAC,IAAKjE,kBAAkB,CAAC+B,IAAI,KAAK;oBAAC,GAAGA,IAAI;oBAAE9B,IAAI,EAAEuE,QAAQ,CAACP,CAAC,CAACC,MAAM,CAACpC,KAAK,CAAC,IAAI;kBAAC,CAAC,CAAC,CAAE;kBAC9F0B,SAAS,EAAC;gBAAkG;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7G,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENxG,OAAA;gBAAAoG,QAAA,gBACEpG,OAAA;kBAAOmG,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRxG,OAAA;kBACEyG,IAAI,EAAC,QAAQ;kBACbhC,KAAK,EAAE/B,eAAe,CAACG,IAAK;kBAC5B8D,QAAQ,EAAGC,CAAC,IAAKjE,kBAAkB,CAAC+B,IAAI,KAAK;oBAAC,GAAGA,IAAI;oBAAE7B,IAAI,EAAEsE,QAAQ,CAACP,CAAC,CAACC,MAAM,CAACpC,KAAK,CAAC,IAAI;kBAAC,CAAC,CAAC,CAAE;kBAC9F0B,SAAS,EAAC;gBAAkG;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7G,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENxG,OAAA;gBAAAoG,QAAA,gBACEpG,OAAA;kBAAOmG,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRxG,OAAA;kBACEyG,IAAI,EAAC,QAAQ;kBACbhC,KAAK,EAAE/B,eAAe,CAACI,MAAO;kBAC9B6D,QAAQ,EAAGC,CAAC,IAAKjE,kBAAkB,CAAC+B,IAAI,KAAK;oBAAC,GAAGA,IAAI;oBAAE5B,MAAM,EAAE8D,CAAC,CAACC,MAAM,CAACpC;kBAAK,CAAC,CAAC,CAAE;kBACjF0B,SAAS,EAAC,kGAAkG;kBAC5GO,WAAW,EAAC;gBAAQ;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENxG,OAAA;gBAAAoG,QAAA,gBACEpG,OAAA;kBAAOmG,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRxG,OAAA;kBACEyG,IAAI,EAAC,QAAQ;kBACbhC,KAAK,EAAE/B,eAAe,CAACK,QAAS;kBAChC4D,QAAQ,EAAGC,CAAC,IAAKjE,kBAAkB,CAAC+B,IAAI,KAAK;oBAAC,GAAGA,IAAI;oBAAE3B,QAAQ,EAAE6D,CAAC,CAACC,MAAM,CAACpC;kBAAK,CAAC,CAAC,CAAE;kBACnF0B,SAAS,EAAC,kGAAkG;kBAC5GO,WAAW,EAAC;gBAAQ;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENxG,OAAA;gBAAAoG,QAAA,gBACEpG,OAAA;kBAAOmG,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRxG,OAAA;kBACEyE,KAAK,EAAE/B,eAAe,CAACM,KAAM;kBAC7B2D,QAAQ,EAAGC,CAAC,IAAKjE,kBAAkB,CAAC+B,IAAI,KAAK;oBAAC,GAAGA,IAAI;oBAAE1B,KAAK,EAAE4D,CAAC,CAACC,MAAM,CAACpC;kBAAK,CAAC,CAAC,CAAE;kBAChF0B,SAAS,EAAC,kGAAkG;kBAC5GqC,IAAI,EAAC,GAAG;kBACR9B,WAAW,EAAC;gBAAgC;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENxG,OAAA;cAAKmG,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9BpG,OAAA;gBACE8G,OAAO,EAAEA,CAAA,KAAMrE,uBAAuB,CAAC,KAAK,CAAE;gBAC9C0D,SAAS,EAAC,qGAAqG;gBAAAC,QAAA,EAChH;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTxG,OAAA;gBACE8G,OAAO,EAAEA,CAAA,KAAMrB,kBAAkB,CAAChF,gBAAgB,CAAE;gBACpD0F,SAAS,EAAC,wFAAwF;gBAAAC,QAAA,EACnG;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGApE,iBAAiB,IAAIE,YAAY,iBAChCtC,OAAA;QAAKmG,SAAS,EAAC,gFAAgF;QAAAC,QAAA,eAC7FpG,OAAA;UAAKmG,SAAS,EAAC,qCAAqC;UAAAC,QAAA,eAClDpG,OAAA;YAAKmG,SAAS,EAAC,KAAK;YAAAC,QAAA,gBAClBpG,OAAA;cAAKmG,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrDpG,OAAA;gBAAImG,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,EAAC;cAEhD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLxG,OAAA;gBACE8G,OAAO,EAAEA,CAAA,KAAMzE,oBAAoB,CAAC,KAAK,CAAE;gBAC3C8D,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,eAE7CpG,OAAA,CAAClB,GAAG;kBAACqH,SAAS,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENxG,OAAA;cAAKmG,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBpG,OAAA;gBAAKmG,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACrCpG,OAAA;kBAAKmG,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,gBACxCpG,OAAA;oBAAKmG,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAAE9D,YAAY,CAACmG;kBAAa;oBAAApC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACpFxG,OAAA;oBAAKmG,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAAgB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1D,CAAC,eACNxG,OAAA;kBAAKmG,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,gBACzCpG,OAAA;oBAAKmG,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAC/C9D,YAAY,CAACoG,aAAa,GACzB,IAAIrB,IAAI,CAAC/E,YAAY,CAACoG,aAAa,CAAC,CAACnB,kBAAkB,CAAC,OAAO,CAAC,GAChE;kBAAQ;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEP,CAAC,eACNxG,OAAA;oBAAKmG,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAELlE,YAAY,CAACqG,eAAe,iBAC3B3I,OAAA;gBAAKmG,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1CpG,OAAA;kBAAImG,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAC;gBAAqB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC3ExG,OAAA;kBAAKmG,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,GACnC9D,YAAY,CAACqG,eAAe,CAACnB,WAAW,CAAC5E,IAAI,EAAC,kBAAU,EAACN,YAAY,CAACqG,eAAe,CAACnB,WAAW,CAAC3E,IAAI,EAAC,OACxG,EAACP,YAAY,CAACqG,eAAe,CAACnB,WAAW,CAAC1E,MAAM,IAC9C,MAAMR,YAAY,CAACqG,eAAe,CAACnB,WAAW,CAAC1E,MAAM,KAAK;gBAAA;kBAAAuD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEzD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN,eAEDxG,OAAA;gBAAKmG,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,gBACxCpG,OAAA;kBAAImG,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC9DxG,OAAA;kBAAKmG,SAAS,EAAE,qBACd7D,YAAY,CAACsG,aAAa,KAAK,WAAW,GAAG,gBAAgB,GAC7DtG,YAAY,CAACsG,aAAa,KAAK,WAAW,GAAG,cAAc,GAC3D,eAAe,EACd;kBAAAxC,QAAA,gBACDpG,OAAA,CAACvB,YAAY;oBAAC0H,SAAS,EAAC;kBAAc;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EACxClE,YAAY,CAACsG,aAAa,KAAK,WAAW,GAAG,gBAAgB,GAC7DtG,YAAY,CAACsG,aAAa,KAAK,WAAW,GAAG,WAAW,GACxD,QAAQ;gBAAA;kBAAAvC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGAxE,iBAAiB,iBAChBhC,OAAA;QAAKmG,SAAS,EAAC,gFAAgF;QAAAC,QAAA,eAC7FpG,OAAA;UAAKmG,SAAS,EAAC,qCAAqC;UAAAC,QAAA,eAClDpG,OAAA;YAAKmG,SAAS,EAAC,KAAK;YAAAC,QAAA,gBAClBpG,OAAA;cAAKmG,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrDpG,OAAA;gBAAImG,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,EAAC;cAEhD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLxG,OAAA;gBACE8G,OAAO,EAAEA,CAAA,KAAM7E,oBAAoB,CAAC,KAAK,CAAE;gBAC3CkE,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,eAE7CpG,OAAA,CAAClB,GAAG;kBAACqH,SAAS,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENxG,OAAA;cAAKmG,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBpG,OAAA;gBAAGmG,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,GAC9BlE,2BAA2B,CAACiD,MAAM,EAAC,mCACtC;cAAA;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,EAEHtE,2BAA2B,CAACiD,MAAM,GAAG,CAAC,iBACrCnF,OAAA;gBAAKmG,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,EACrDlE,2BAA2B,CAAC8E,GAAG,CAACzB,QAAQ,iBACvCvF,OAAA;kBAAuBmG,SAAS,EAAC,0DAA0D;kBAAAC,QAAA,gBACzFpG,OAAA;oBAAMmG,SAAS,EAAC,SAAS;oBAAAC,QAAA,EAAEb,QAAQ,CAACW;kBAAI;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAChDxG,OAAA;oBACE8G,OAAO,EAAEA,CAAA,KAAMjB,6BAA6B,CAACN,QAAQ,CAAE;oBACvDY,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,eAE3CpG,OAAA,CAAClB,GAAG;sBAACqH,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB,CAAC;gBAAA,GAPDjB,QAAQ,CAACvB,EAAE;kBAAAqC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAQhB,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CACN,eAEDxG,OAAA;gBACEyG,IAAI,EAAC,MAAM;gBACXC,WAAW,EAAC,kBAAkB;gBAC9BP,SAAS,EAAC,kGAAkG;gBAC5G0C,UAAU,EAAGjC,CAAC,IAAK;kBACjB,IAAIA,CAAC,CAACK,GAAG,KAAK,OAAO,EAAE;oBACrBlB,mBAAmB,CAACa,CAAC,CAACC,MAAM,CAACpC,KAAK,CAAC;kBACrC;gBACF;cAAE;gBAAA4B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENxG,OAAA;cAAKmG,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBpG,OAAA;gBACE8G,OAAO,EAAEA,CAAA,KAAM7E,oBAAoB,CAAC,KAAK,CAAE;gBAC3CkE,SAAS,EAAC,qGAAqG;gBAAAC,QAAA,EAChH;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTxG,OAAA;gBACE8G,OAAO,EAAGF,CAAC,IAAK;kBACd,MAAMkC,KAAK,GAAGlC,CAAC,CAACC,MAAM,CAACkC,aAAa,CAACA,aAAa,CAACC,aAAa,CAAC,OAAO,CAAC;kBACzEjD,mBAAmB,CAAC+C,KAAK,CAACrE,KAAK,CAAC;gBAClC,CAAE;gBACF0B,SAAS,EAAC,4FAA4F;gBAAAC,QAAA,EACvG;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA/F,gBAAgB,IAAI,CAAC+B,oBAAoB,iBACxCxC,OAAA;QAAKmG,SAAS,EAAC,gFAAgF;QAAAC,QAAA,eAC7FpG,OAAA;UAAKmG,SAAS,EAAC,mEAAmE;UAAAC,QAAA,eAChFpG,OAAA;YAAKmG,SAAS,EAAC,KAAK;YAAAC,QAAA,gBAClBpG,OAAA;cAAKmG,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrDpG,OAAA;gBAAAoG,QAAA,gBACEpG,OAAA;kBAAImG,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,EAClD3F,gBAAgB,CAACyF;gBAAI;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB,CAAC,eACLxG,OAAA;kBAAKmG,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1CpG,OAAA;oBAAMmG,SAAS,EAAE,8CAA8C/G,kBAAkB,CAACqB,gBAAgB,CAACW,UAAU,CAAC,EAAG;oBAAAgF,QAAA,EAC9G3F,gBAAgB,CAACW;kBAAU;oBAAAiF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB,CAAC,eACPxG,OAAA;oBAAMmG,SAAS,EAAC,eAAe;oBAAAC,QAAA,IAAA/F,qBAAA,GAC5BpB,kBAAkB,CAACwB,gBAAgB,CAACQ,QAAQ,CAAC,cAAAZ,qBAAA,uBAA7CA,qBAAA,CAA+C0H,IAAI,EAAC,GAAC,GAAAzH,sBAAA,GAACrB,kBAAkB,CAACwB,gBAAgB,CAACQ,QAAQ,CAAC,cAAAX,sBAAA,uBAA7CA,sBAAA,CAA+C4F,IAAI;kBAAA;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNxG,OAAA;gBACE8G,OAAO,EAAEA,CAAA,KAAMpG,mBAAmB,CAAC,IAAI,CAAE;gBACzCyF,SAAS,EAAC,4CAA4C;gBAAAC,QAAA,EACvD;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENxG,OAAA;cAAKmG,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBAEpDpG,OAAA;gBAAKmG,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5BpG,OAAA;kBAAKmG,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBACnBpG,OAAA;oBAAImG,SAAS,EAAC,0CAA0C;oBAAAC,QAAA,EAAC;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACzExG,OAAA;oBAAGmG,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,EAAE3F,gBAAgB,CAACyH;kBAAW;oBAAA7B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5E,CAAC,eAENxG,OAAA;kBAAKmG,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBACnBpG,OAAA;oBAAImG,SAAS,EAAC,0CAA0C;oBAAAC,QAAA,EAAC;kBAAuB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACrFxG,OAAA;oBAAImG,SAAS,EAAC,oCAAoC;oBAAAC,QAAA,EAC/C3F,gBAAgB,CAACwI,YAAY,CAACjC,GAAG,CAAC,CAACkC,WAAW,EAAEC,KAAK,kBACpDnJ,OAAA;sBAAgBmG,SAAS,EAAC,oCAAoC;sBAAAC,QAAA,EAC3D8C;oBAAW,GADLC,KAAK;sBAAA9C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEV,CACL;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,EAEL/F,gBAAgB,CAAC2I,IAAI,iBACpBpJ,OAAA;kBAAKmG,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBACnBpG,OAAA;oBAAImG,SAAS,EAAC,0CAA0C;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACtExG,OAAA;oBAAImG,SAAS,EAAC,WAAW;oBAAAC,QAAA,EACtB3F,gBAAgB,CAAC2I,IAAI,CAACpC,GAAG,CAAC,CAACqC,GAAG,EAAEF,KAAK,kBACpCnJ,OAAA;sBAAgBmG,SAAS,EAAC,gCAAgC;sBAAAC,QAAA,gBACxDpG,OAAA;wBAAMmG,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,EAAC;sBAAC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,EACjD6C,GAAG;oBAAA,GAFGF,KAAK;sBAAA9C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAGV,CACL;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CACN,EAEA/F,gBAAgB,CAAC6I,UAAU,iBAC1BtJ,OAAA;kBAAKmG,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBACnBpG,OAAA;oBAAImG,SAAS,EAAC,0CAA0C;oBAAAC,QAAA,EAAC;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACxExG,OAAA;oBAAImG,SAAS,EAAC,WAAW;oBAAAC,QAAA,EACtB3F,gBAAgB,CAAC6I,UAAU,CAACtC,GAAG,CAAC,CAACuC,SAAS,EAAEJ,KAAK,kBAChDnJ,OAAA;sBAAgBmG,SAAS,EAAC,gCAAgC;sBAAAC,QAAA,gBACxDpG,OAAA;wBAAMmG,SAAS,EAAC,0BAA0B;wBAAAC,QAAA,EAAC;sBAAC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,EAClD+C,SAAS;oBAAA,GAFHJ,KAAK;sBAAA9C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAGV,CACL;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAGNxG,OAAA;gBAAKmG,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5BpG,OAAA;kBAAKmG,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,gBAC7CpG,OAAA;oBAAImG,SAAS,EAAC,0CAA0C;oBAAAC,QAAA,EAAC;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAE1ExG,OAAA;oBAAKmG,SAAS,EAAC,WAAW;oBAAAC,QAAA,gBACxBpG,OAAA;sBAAKmG,SAAS,EAAC,sBAAsB;sBAAAC,QAAA,gBACnCpG,OAAA;wBAAMmG,SAAS,EAAC,eAAe;wBAAAC,QAAA,EAAC;sBAAK;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAC5CxG,OAAA;wBAAMmG,SAAS,EAAC,aAAa;wBAAAC,QAAA,GAAE3F,gBAAgB,CAACsC,QAAQ,EAAC,MAAI;sBAAA;wBAAAsD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjE,CAAC,eACNxG,OAAA;sBAAKmG,SAAS,EAAC,sBAAsB;sBAAAC,QAAA,gBACnCpG,OAAA;wBAAMmG,SAAS,EAAC,eAAe;wBAAAC,QAAA,EAAC;sBAAQ;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAC/CxG,OAAA;wBAAMmG,SAAS,EAAC,aAAa;wBAAAC,QAAA,GAAE3F,gBAAgB,CAAC4H,QAAQ,EAAC,MAAI;sBAAA;wBAAAhC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjE,CAAC,eACNxG,OAAA;sBAAKmG,SAAS,EAAC,sBAAsB;sBAAAC,QAAA,gBACnCpG,OAAA;wBAAMmG,SAAS,EAAC,eAAe;wBAAAC,QAAA,EAAC;sBAAU;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACjDxG,OAAA;wBAAMmG,SAAS,EAAC,aAAa;wBAAAC,QAAA,GAAA7F,qBAAA,GAAEpB,cAAc,CAACsB,gBAAgB,CAACU,SAAS,CAAC,cAAAZ,qBAAA,uBAA1CA,qBAAA,CAA4C2F;sBAAI;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENxG,OAAA;kBAAKmG,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,gBAC7CpG,OAAA;oBAAImG,SAAS,EAAC,0CAA0C;oBAAAC,QAAA,EAAC;kBAAmB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACjFxG,OAAA;oBAAKmG,SAAS,EAAC,sBAAsB;oBAAAC,QAAA,EAClC3F,gBAAgB,CAACvB,YAAY,CAAC8H,GAAG,CAACmB,EAAE;sBAAA,IAAAqB,iBAAA,EAAAC,iBAAA;sBAAA,oBACnCzJ,OAAA;wBAAemG,SAAS,EAAC,0DAA0D;wBAAAC,QAAA,IAAAoD,iBAAA,GAChFtK,YAAY,CAACiJ,EAAE,CAAC,cAAAqB,iBAAA,uBAAhBA,iBAAA,CAAkBzB,IAAI,EAAC,GAAC,GAAA0B,iBAAA,GAACvK,YAAY,CAACiJ,EAAE,CAAC,cAAAsB,iBAAA,uBAAhBA,iBAAA,CAAkBvD,IAAI;sBAAA,GADvCiC,EAAE;wBAAA9B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAEP,CAAC;oBAAA,CACR;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENxG,OAAA;kBAAKmG,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBACxBpG,OAAA;oBACE8G,OAAO,EAAEA,CAAA,KAAMtH,cAAc,CAACiB,gBAAgB,CAACuD,EAAE,CAAE;oBACnDmC,SAAS,EAAE,8FACTzE,iBAAiB,CAAC2C,IAAI,CAACyD,GAAG,IAAIA,GAAG,CAAC9D,EAAE,KAAKvD,gBAAgB,CAACuD,EAAE,CAAC,GACzD,0CAA0C,GAC1C,6CAA6C,EAChD;oBAAAoC,QAAA,gBAEHpG,OAAA,CAAC3B,OAAO;sBAAC8H,SAAS,EAAC;oBAAc;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EACnC9E,iBAAiB,CAAC2C,IAAI,CAACyD,GAAG,IAAIA,GAAG,CAAC9D,EAAE,KAAKvD,gBAAgB,CAACuD,EAAE,CAAC,GAAG,qBAAqB,GAAG,qBAAqB;kBAAA;oBAAAqC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxG,CAAC,eAETxG,OAAA;oBAAQmG,SAAS,EAAC,qIAAqI;oBAAAC,QAAA,gBACrJpG,OAAA,CAACjC,MAAM;sBAACoI,SAAS,EAAC;oBAAc;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,wBAErC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACpG,EAAA,CAn7BID,SAAS;EAAA,QACIpB,OAAO;AAAA;AAAA2K,EAAA,GADpBvJ,SAAS;AAq7Bf,eAAeA,SAAS;AAAC,IAAAuJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}