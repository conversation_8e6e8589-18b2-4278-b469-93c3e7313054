import React, { useState, useEffect } from 'react';
import {
  FiUsers,
  FiTrendingUp,
  FiAward,
  FiUserPlus,
  FiMessageCircle,
  FiTarget,
  FiClock,
  FiMapPin,
  FiCheck,
  FiX,
  FiSearch,
  FiPlus,
  FiEdit3,
  FiTrash2,
  FiShare2,
  FiHeart,
  FiThumbsUp
} from 'react-icons/fi';
import { useAuth } from '../contexts/AuthContext';
import {
  generateMockUsers,
  generateChallenges,
  generateFriendships,
  generateFriendSuggestions,
  formatTime,
  formatPace
} from '../utils/socialUtils';
import { generateSegments } from '../utils/mapUtils';

const Social = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('friends');

  const [challenges, setChallenges] = useState([]);
  const [segments, setSegments] = useState([]);
  const [friendships, setFriendships] = useState([]);
  const [friendSuggestions, setFriendSuggestions] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [userActivities, setUserActivities] = useState([]);
  const [showCreateChallenge, setShowCreateChallenge] = useState(false);
  const [newChallenge, setNewChallenge] = useState({
    name: '',
    description: '',
    type: 'distance',
    target: '',
    duration: 7,
    difficulty: 'modéré'
  });


  useEffect(() => {
    // Charger les données sauvegardées
    try {
      const savedChallenges = JSON.parse(localStorage.getItem('socialChallenges') || '[]');
      const savedActivities = JSON.parse(localStorage.getItem('userActivities') || '[]');
      const savedFriendships = JSON.parse(localStorage.getItem('friendships') || '[]');

      if (savedChallenges.length > 0) {
        setChallenges(savedChallenges);
      }
      setUserActivities(savedActivities);
      if (savedFriendships.length > 0) {
        setFriendships(savedFriendships);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des données sociales:', error);
    }

    // Générer des données fictives si aucune donnée sauvegardée
    const mockUsers = generateMockUsers(50);
    const mockChallenges = generateChallenges(mockUsers);
    const mockSegments = generateSegments([
      // Quelques routes fictives pour générer des segments
      {
        name: 'Parcours du Parc',
        points: Array.from({ length: 20 }, (_, i) => ({
          lat: 48.8566 + (i * 0.001),
          lng: 2.3522 + (i * 0.001),
          elevation: 100 + Math.sin(i) * 50
        })),
        type: 'running'
      },
      {
        name: 'Circuit de la Forêt',
        points: Array.from({ length: 15 }, (_, i) => ({
          lat: 48.8566 + (i * 0.0015),
          lng: 2.3522 - (i * 0.0008),
          elevation: 120 + Math.cos(i) * 40
        })),
        type: 'cycling'
      }
    ]);

    // Utiliser les données fictives si pas de données sauvegardées
    if (challenges.length === 0) {
      setChallenges(mockChallenges);
    }
    setSegments(mockSegments);

    if (user && friendships.length === 0) {
      const userFriendships = generateFriendships(mockUsers, user.id);
      const suggestions = generateFriendSuggestions(mockUsers, user.id, userFriendships);
      setFriendships(userFriendships);
      setFriendSuggestions(suggestions);
    }
  }, [user]);

  const acceptedFriends = friendships.filter(f => f.status === 'accepted');
  const pendingRequests = friendships.filter(f => f.status === 'pending');
  const activeChallenges = challenges.filter(c => c.isActive);
  const completedChallenges = challenges.filter(c => !c.isActive);

  const handleAcceptFriend = (friendshipId) => {
    setFriendships(prev => prev.map(f => 
      f.id === friendshipId ? { ...f, status: 'accepted' } : f
    ));
  };

  const handleRejectFriend = (friendshipId) => {
    setFriendships(prev => prev.filter(f => f.id !== friendshipId));
  };

  const handleJoinChallenge = (challengeId) => {
    const updatedChallenges = challenges.map(challenge => {
      if (challenge.id === challengeId) {
        const newParticipant = {
          user: {
            id: user.id,
            firstName: user.firstName,
            lastName: user.lastName,
            avatar: user.avatar || `https://ui-avatars.com/api/?name=${user.firstName}+${user.lastName}`
          },
          progress: 0,
          rank: challenge.participants.length + 1,
          joinedAt: new Date(),
          isCompleted: false,
          lastActivity: new Date()
        };

        return {
          ...challenge,
          participants: [...challenge.participants, newParticipant]
        };
      }
      return challenge;
    });

    setChallenges(updatedChallenges);
    localStorage.setItem('socialChallenges', JSON.stringify(updatedChallenges));
  };

  const createChallenge = () => {
    if (!newChallenge.name || !newChallenge.target) return;

    const challenge = {
      id: `challenge_${Date.now()}`,
      name: newChallenge.name,
      description: newChallenge.description,
      type: newChallenge.type,
      target: parseFloat(newChallenge.target),
      duration: newChallenge.duration,
      difficulty: newChallenge.difficulty,
      isActive: true,
      startDate: new Date(),
      endDate: new Date(Date.now() + newChallenge.duration * 24 * 60 * 60 * 1000),
      createdBy: user?.firstName || 'Utilisateur',
      participants: [{
        user: {
          id: user?.id || 'user1',
          firstName: user?.firstName || 'Utilisateur',
          lastName: user?.lastName || '',
          avatar: user?.avatar || `https://ui-avatars.com/api/?name=${user?.firstName}+${user?.lastName}`
        },
        progress: 0,
        rank: 1,
        joinedAt: new Date(),
        isCompleted: false,
        lastActivity: new Date()
      }],
      icon: getChallengeIcon(newChallenge.type)
    };

    const updatedChallenges = [challenge, ...challenges];
    setChallenges(updatedChallenges);
    localStorage.setItem('socialChallenges', JSON.stringify(updatedChallenges));

    setShowCreateChallenge(false);
    setNewChallenge({
      name: '',
      description: '',
      type: 'distance',
      target: '',
      duration: 7,
      difficulty: 'modéré'
    });
  };

  const deleteChallenge = (challengeId) => {
    const updatedChallenges = challenges.filter(c => c.id !== challengeId);
    setChallenges(updatedChallenges);
    localStorage.setItem('socialChallenges', JSON.stringify(updatedChallenges));
  };

  const updateChallengeProgress = (challengeId, progress) => {
    const updatedChallenges = challenges.map(challenge => {
      if (challenge.id === challengeId) {
        const updatedParticipants = challenge.participants.map(p => {
          if (p.user.id === user?.id) {
            return {
              ...p,
              progress: Math.min(progress, challenge.target),
              isCompleted: progress >= challenge.target,
              lastActivity: new Date()
            };
          }
          return p;
        });

        // Recalculer les rangs
        const sortedParticipants = updatedParticipants.sort((a, b) => b.progress - a.progress);
        sortedParticipants.forEach((p, index) => {
          p.rank = index + 1;
        });

        return {
          ...challenge,
          participants: sortedParticipants
        };
      }
      return challenge;
    });

    setChallenges(updatedChallenges);
    localStorage.setItem('socialChallenges', JSON.stringify(updatedChallenges));
  };

  const filteredFriends = acceptedFriends.filter(friendship =>
    friendship.user2.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    friendship.user2.lastName.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getDifficultyColor = (difficulty) => {
    switch (difficulty) {
      case 'facile': return 'text-green-600 bg-green-100';
      case 'modéré': return 'text-yellow-600 bg-yellow-100';
      case 'difficile': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getTypeIcon = (type) => {
    switch (type) {
      case 'running': return '🏃‍♂️';
      case 'cycling': return '🚴‍♂️';
      case 'hiking': return '🥾';
      case 'walking': return '🚶‍♂️';
      default: return '📍';
    }
  };

  const getChallengeIcon = (type) => {
    switch (type) {
      case 'distance': return '📏';
      case 'activities': return '🏃‍♂️';
      case 'time': return '⏱️';
      case 'streak': return '🔥';
      default: return '🎯';
    }
  };

  const addActivity = (activity) => {
    const newActivity = {
      id: Date.now(),
      user: {
        id: user?.id || 'user1',
        firstName: user?.firstName || 'Utilisateur',
        lastName: user?.lastName || '',
        avatar: user?.avatar || `https://ui-avatars.com/api/?name=${user?.firstName}+${user?.lastName}`
      },
      ...activity,
      timestamp: new Date(),
      likes: [],
      comments: []
    };

    const updatedActivities = [newActivity, ...userActivities];
    setUserActivities(updatedActivities);
    localStorage.setItem('userActivities', JSON.stringify(updatedActivities));
  };

  const likeActivity = (activityId) => {
    const updatedActivities = userActivities.map(activity => {
      if (activity.id === activityId) {
        const hasLiked = activity.likes.includes(user?.id);
        return {
          ...activity,
          likes: hasLiked
            ? activity.likes.filter(id => id !== user?.id)
            : [...activity.likes, user?.id]
        };
      }
      return activity;
    });

    setUserActivities(updatedActivities);
    localStorage.setItem('userActivities', JSON.stringify(updatedActivities));
  };

  const addComment = (activityId, comment) => {
    const updatedActivities = userActivities.map(activity => {
      if (activity.id === activityId) {
        const newComment = {
          id: Date.now(),
          user: {
            id: user?.id || 'user1',
            firstName: user?.firstName || 'Utilisateur',
            lastName: user?.lastName || ''
          },
          text: comment,
          timestamp: new Date()
        };

        return {
          ...activity,
          comments: [...activity.comments, newComment]
        };
      }
      return activity;
    });

    setUserActivities(updatedActivities);
    localStorage.setItem('userActivities', JSON.stringify(updatedActivities));
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            Communauté FitTracker
          </h1>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            Connectez-vous avec d'autres athlètes, participez à des défis et 
            découvrez les classements des segments populaires.
          </p>
        </div>

        {/* Tabs */}
        <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg mb-8">
          <button
            onClick={() => setActiveTab('friends')}
            className={`flex-1 py-3 px-4 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'friends'
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <FiUsers className="inline mr-2" />
            Amis ({acceptedFriends.length})
          </button>
          <button
            onClick={() => setActiveTab('challenges')}
            className={`flex-1 py-3 px-4 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'challenges'
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <FiTarget className="inline mr-2" />
            Défis ({activeChallenges.length})
          </button>
          <button
            onClick={() => setActiveTab('leaderboards')}
            className={`flex-1 py-3 px-4 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'leaderboards'
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <FiTrendingUp className="inline mr-2" />
            Classements
          </button>
        </div>

        {/* Friends Tab */}
        {activeTab === 'friends' && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Friends List */}
            <div className="lg:col-span-2">
              <div className="bg-white rounded-lg shadow-lg p-6">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-xl font-semibold text-gray-900">
                    Mes Amis ({acceptedFriends.length})
                  </h2>
                  <div className="relative">
                    <FiSearch className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <input
                      type="text"
                      placeholder="Rechercher un ami..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </div>

                <div className="space-y-4">
                  {filteredFriends.map(friendship => (
                    <div key={friendship.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                      <div className="flex items-center space-x-4">
                        <img
                          src={friendship.user2.avatar}
                          alt={`${friendship.user2.firstName} ${friendship.user2.lastName}`}
                          className="w-12 h-12 rounded-full"
                        />
                        <div>
                          <h3 className="font-medium text-gray-900">
                            {friendship.user2.firstName} {friendship.user2.lastName}
                          </h3>
                          <div className="flex items-center space-x-4 text-sm text-gray-600">
                            <span className="flex items-center">
                              <FiMapPin className="h-4 w-4 mr-1" />
                              {friendship.user2.city}
                            </span>
                            <span className="flex items-center">
                              <FiClock className="h-4 w-4 mr-1" />
                              {friendship.user2.isOnline ? 'En ligne' : 'Hors ligne'}
                            </span>
                          </div>
                          <div className="text-xs text-gray-500 mt-1">
                            {friendship.mutualFriends} amis en commun • {friendship.sharedActivities} activités partagées
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <button className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg">
                          <FiMessageCircle className="h-5 w-5" />
                        </button>
                        <div className="text-right">
                          <div className="text-sm font-medium text-gray-900">
                            {friendship.user2.stats.totalDistance} km
                          </div>
                          <div className="text-xs text-gray-500">
                            {friendship.user2.stats.totalActivities} activités
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Pending Requests */}
              {pendingRequests.length > 0 && (
                <div className="bg-white rounded-lg shadow-lg p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">
                    Demandes d'amitié ({pendingRequests.length})
                  </h3>
                  <div className="space-y-3">
                    {pendingRequests.map(request => (
                      <div key={request.id} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                        <div className="flex items-center space-x-3">
                          <img
                            src={request.user1.avatar}
                            alt={`${request.user1.firstName} ${request.user1.lastName}`}
                            className="w-10 h-10 rounded-full"
                          />
                          <div>
                            <h4 className="font-medium text-gray-900 text-sm">
                              {request.user1.firstName} {request.user1.lastName}
                            </h4>
                            <p className="text-xs text-gray-600">{request.user1.city}</p>
                          </div>
                        </div>
                        <div className="flex space-x-1">
                          <button
                            onClick={() => handleAcceptFriend(request.id)}
                            className="p-1 text-green-600 hover:bg-green-50 rounded"
                          >
                            <FiCheck className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleRejectFriend(request.id)}
                            className="p-1 text-red-600 hover:bg-red-50 rounded"
                          >
                            <FiX className="h-4 w-4" />
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Friend Suggestions */}
              <div className="bg-white rounded-lg shadow-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Suggestions d'amis
                </h3>
                <div className="space-y-3">
                  {friendSuggestions.slice(0, 5).map(suggestion => (
                    <div key={suggestion.user.id} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <img
                          src={suggestion.user.avatar}
                          alt={`${suggestion.user.firstName} ${suggestion.user.lastName}`}
                          className="w-10 h-10 rounded-full"
                        />
                        <div>
                          <h4 className="font-medium text-gray-900 text-sm">
                            {suggestion.user.firstName} {suggestion.user.lastName}
                          </h4>
                          <p className="text-xs text-gray-600">{suggestion.reason}</p>
                        </div>
                      </div>
                      <button className="p-1 text-blue-600 hover:bg-blue-50 rounded">
                        <FiUserPlus className="h-4 w-4" />
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Challenges Tab */}
        {activeTab === 'challenges' && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Active Challenges */}
            <div className="lg:col-span-2">
              <div className="bg-white rounded-lg shadow-lg p-6">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-xl font-semibold text-gray-900">
                    Défis Actifs ({activeChallenges.length})
                  </h2>
                  <button
                    onClick={() => setShowCreateChallenge(true)}
                    className="bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center"
                  >
                    <FiPlus className="mr-2" />
                    Créer un défi
                  </button>
                </div>
                
                <div className="grid gap-6">
                  {activeChallenges.map(challenge => (
                    <div key={challenge.id} className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex items-center space-x-3">
                          <span className="text-2xl">{getChallengeIcon(challenge.type)}</span>
                          <div>
                            <h3 className="font-semibold text-gray-900">{challenge.name}</h3>
                            <p className="text-sm text-gray-600">{challenge.description}</p>
                          </div>
                        </div>
                        <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                          challenge.category === 'team' ? 'bg-purple-100 text-purple-700' : 'bg-blue-100 text-blue-700'
                        }`}>
                          {challenge.category === 'team' ? 'Équipe' : 'Individuel'}
                        </span>
                      </div>
                      
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                        <div className="text-center">
                          <div className="text-lg font-bold text-gray-900">{challenge.participants.length}</div>
                          <div className="text-xs text-gray-600">Participants</div>
                        </div>
                        <div className="text-center">
                          <div className="text-lg font-bold text-gray-900">{challenge.target}</div>
                          <div className="text-xs text-gray-600">{challenge.unit}</div>
                        </div>
                        <div className="text-center">
                          <div className="text-lg font-bold text-gray-900">{challenge.duration}</div>
                          <div className="text-xs text-gray-600">jours</div>
                        </div>
                        <div className="text-center">
                          <div className="text-lg font-bold text-gray-900">
                            {Math.ceil((challenge.endDate - new Date()) / (24 * 60 * 60 * 1000))}
                          </div>
                          <div className="text-xs text-gray-600">jours restants</div>
                        </div>
                      </div>
                      
                      {/* Barre de progression pour l'utilisateur */}
                      {challenge.participants.find(p => p.user.id === user?.id) && (
                        <div className="mb-4">
                          <div className="flex items-center justify-between text-sm mb-2">
                            <span>Votre progression</span>
                            <span>{challenge.participants.find(p => p.user.id === user?.id)?.progress || 0} / {challenge.target}</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div
                              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                              style={{
                                width: `${Math.min(100, ((challenge.participants.find(p => p.user.id === user?.id)?.progress || 0) / challenge.target) * 100)}%`
                              }}
                            ></div>
                          </div>
                        </div>
                      )}

                      <div className="flex items-center justify-between">
                        <div className="text-sm text-gray-600">
                          <span className="font-medium">Prix :</span> {challenge.prize || 'Badge de réussite'}
                        </div>
                        <div className="flex items-center space-x-2">
                          {challenge.participants.find(p => p.user.id === user?.id) ? (
                            <>
                              <input
                                type="number"
                                placeholder="Progression"
                                className="w-20 px-2 py-1 border border-gray-300 rounded text-sm"
                                onKeyPress={(e) => {
                                  if (e.key === 'Enter') {
                                    updateChallengeProgress(challenge.id, parseFloat(e.target.value));
                                    e.target.value = '';
                                  }
                                }}
                              />
                              <button
                                onClick={() => deleteChallenge(challenge.id)}
                                className="text-red-600 hover:text-red-800 transition-colors"
                                title="Supprimer le défi"
                              >
                                <FiTrash2 className="h-4 w-4" />
                              </button>
                            </>
                          ) : (
                            <button
                              onClick={() => handleJoinChallenge(challenge.id)}
                              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm"
                            >
                              Rejoindre
                            </button>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Challenge Details Sidebar */}
            <div className="space-y-6">
              <div className="bg-white rounded-lg shadow-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Mes Défis en Cours
                </h3>
                <div className="space-y-3">
                  {activeChallenges.slice(0, 3).map(challenge => {
                    const userParticipation = challenge.participants.find(p => p.user.id === user?.id);
                    if (!userParticipation) return null;
                    
                    const progressPercentage = (userParticipation.progress / challenge.target) * 100;
                    
                    return (
                      <div key={challenge.id} className="p-3 border border-gray-200 rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-medium text-gray-900 text-sm">{challenge.name}</h4>
                          <span className="text-xs text-gray-600">#{userParticipation.rank}</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                          <div
                            className="bg-blue-600 h-2 rounded-full"
                            style={{ width: `${Math.min(progressPercentage, 100)}%` }}
                          ></div>
                        </div>
                        <div className="text-xs text-gray-600">
                          {userParticipation.progress} / {challenge.target} {challenge.unit}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>

              <div className="bg-white rounded-lg shadow-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Défis Terminés
                </h3>
                <div className="space-y-3">
                  {completedChallenges.slice(0, 3).map(challenge => (
                    <div key={challenge.id} className="p-3 border border-gray-200 rounded-lg">
                      <h4 className="font-medium text-gray-900 text-sm">{challenge.name}</h4>
                      <div className="flex items-center justify-between mt-2">
                        <span className="text-xs text-gray-600">
                          {challenge.participants.length} participants
                        </span>
                        <span className="text-xs text-green-600 font-medium">Terminé</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Leaderboards Tab */}
        {activeTab === 'leaderboards' && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Segments Leaderboards */}
            <div className="lg:col-span-2">
              <div className="bg-white rounded-lg shadow-lg p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-6">
                  Classements des Segments
                </h2>
                
                <div className="space-y-6">
                  {segments.slice(0, 5).map(segment => (
                    <div key={segment.id} className="border border-gray-200 rounded-lg p-6">
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex items-center space-x-3">
                          <span className="text-xl">{getTypeIcon(segment.type)}</span>
                          <div>
                            <h3 className="font-semibold text-gray-900">{segment.name}</h3>
                            <p className="text-sm text-gray-600">{segment.description}</p>
                            <div className="flex items-center space-x-4 mt-1">
                              <span className="text-xs text-gray-500">{segment.distance.toFixed(1)} km</span>
                              <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(segment.difficulty)}`}>
                                {segment.difficulty}
                              </span>
                              <span className="text-xs text-gray-500">{segment.totalAttempts} tentatives</span>
                            </div>
                          </div>
                        </div>
                      </div>
                      
                      <div className="space-y-2">
                        <h4 className="font-medium text-gray-900 text-sm mb-3">Top 5</h4>
                        {segment.leaderboard.slice(0, 5).map((entry, index) => (
                          <div key={index} className="flex items-center justify-between p-2 rounded-lg hover:bg-gray-50">
                            <div className="flex items-center space-x-3">
                              <span className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold ${
                                index === 0 ? 'bg-yellow-100 text-yellow-800' :
                                index === 1 ? 'bg-gray-100 text-gray-800' :
                                index === 2 ? 'bg-orange-100 text-orange-800' :
                                'bg-blue-100 text-blue-800'
                              }`}>
                                {entry.rank}
                              </span>
                              <div>
                                <span className="font-medium text-gray-900 text-sm">{entry.athlete}</span>
                                {entry.isLocalLegend && (
                                  <FiAward className="inline ml-2 h-4 w-4 text-yellow-500" />
                                )}
                              </div>
                            </div>
                            <div className="text-right">
                              <div className="font-medium text-gray-900 text-sm">
                                {formatTime(entry.time)}
                              </div>
                              <div className="text-xs text-gray-500">
                                {formatPace(entry.pace)}
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Leaderboard Stats */}
            <div className="space-y-6">
              <div className="bg-white rounded-lg shadow-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Mes Performances
                </h3>
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                    <div className="flex items-center space-x-2">
                      <FiAward className="h-5 w-5 text-yellow-600" />
                      <span className="font-medium text-gray-900">Légendes Locales</span>
                    </div>
                    <span className="text-lg font-bold text-yellow-600">2</span>
                  </div>
                  
                  <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                    <div className="flex items-center space-x-2">
                      <FiTrendingUp className="h-5 w-5 text-blue-600" />
                      <span className="font-medium text-gray-900">Top 10</span>
                    </div>
                    <span className="text-lg font-bold text-blue-600">8</span>
                  </div>
                  
                  <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                    <div className="flex items-center space-x-2">
                      <FiAward className="h-5 w-5 text-green-600" />
                      <span className="font-medium text-gray-900">Records Personnels</span>
                    </div>
                    <span className="text-lg font-bold text-green-600">15</span>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Segments Populaires
                </h3>
                <div className="space-y-3">
                  {segments.slice(0, 5).map(segment => (
                    <div key={segment.id} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                      <div>
                        <h4 className="font-medium text-gray-900 text-sm">{segment.name}</h4>
                        <div className="flex items-center space-x-2 text-xs text-gray-600">
                          <span>{segment.distance.toFixed(1)} km</span>
                          <span>•</span>
                          <span>{segment.totalAttempts} tentatives</span>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-sm font-medium text-gray-900">
                          {formatTime(segment.leaderboard[0]?.time || 0)}
                        </div>
                        <div className="text-xs text-gray-500">Record</div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Modal de création de défi */}
        {showCreateChallenge && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Créer un nouveau défi
              </h3>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Nom du défi
                  </label>
                  <input
                    type="text"
                    value={newChallenge.name}
                    onChange={(e) => setNewChallenge({...newChallenge, name: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Ex: Défi 100km en janvier"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Description
                  </label>
                  <textarea
                    value={newChallenge.description}
                    onChange={(e) => setNewChallenge({...newChallenge, description: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    rows="3"
                    placeholder="Décrivez votre défi..."
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Type
                    </label>
                    <select
                      value={newChallenge.type}
                      onChange={(e) => setNewChallenge({...newChallenge, type: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="distance">Distance (km)</option>
                      <option value="activities">Nombre d'activités</option>
                      <option value="time">Temps (minutes)</option>
                      <option value="streak">Série de jours</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Objectif
                    </label>
                    <input
                      type="number"
                      value={newChallenge.target}
                      onChange={(e) => setNewChallenge({...newChallenge, target: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="100"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Durée (jours)
                    </label>
                    <input
                      type="number"
                      value={newChallenge.duration}
                      onChange={(e) => setNewChallenge({...newChallenge, duration: parseInt(e.target.value)})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      min="1"
                      max="365"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Difficulté
                    </label>
                    <select
                      value={newChallenge.difficulty}
                      onChange={(e) => setNewChallenge({...newChallenge, difficulty: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="facile">Facile</option>
                      <option value="modéré">Modéré</option>
                      <option value="difficile">Difficile</option>
                    </select>
                  </div>
                </div>
              </div>

              <div className="flex space-x-3 mt-6">
                <button
                  onClick={createChallenge}
                  className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Créer le défi
                </button>
                <button
                  onClick={() => setShowCreateChallenge(false)}
                  className="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-400 transition-colors"
                >
                  Annuler
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Social;
