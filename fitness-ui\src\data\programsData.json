[{"id": 1, "title": "Débutant Force", "category": "Musculation", "level": "Débutant", "duration": "30 min", "equipment": "Haltères, tapis", "description": "Programme idéal pour débuter en musculation et développer sa force de base.", "exercises": [{"name": "Squats", "sets": 3, "reps": 10, "rest": "60s"}, {"name": "Pompes sur genoux", "sets": 3, "reps": 8, "rest": "60s"}, {"name": "Rowing haltère", "sets": 3, "reps": 10, "rest": "60s"}, {"name": "<PERSON><PERSON>", "sets": 2, "reps": 8, "rest": "60s"}, {"name": "Planche", "sets": 3, "reps": "30s", "rest": "30s"}]}, {"id": 2, "title": "Cardio Brûle-Graisse", "category": "Cardio", "level": "Intermédiaire", "duration": "25 min", "equipment": "<PERSON><PERSON>", "description": "Séance cardio intense pour maximiser la dépense calorique et améliorer l'endurance.", "exercises": [{"name": "Jumping jacks", "sets": 1, "reps": "60s", "rest": "15s"}, {"name": "Mountain climbers", "sets": 1, "reps": "45s", "rest": "15s"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "sets": 1, "reps": "30s", "rest": "15s"}, {"name": "High knees", "sets": 1, "reps": "45s", "rest": "15s"}, {"name": "Squat jumps", "sets": 1, "reps": "30s", "rest": "15s"}]}, {"id": 3, "title": "HIIT Full Body", "category": "HIIT", "level": "<PERSON><PERSON><PERSON>", "duration": "20 min", "equipment": "<PERSON><PERSON>", "description": "Entraînement par intervalles de haute intensité pour un impact maximal en un minimum de temps.", "exercises": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "sets": 4, "reps": "20s", "rest": "10s"}, {"name": "Mountain climbers", "sets": 4, "reps": "20s", "rest": "10s"}, {"name": "Jumping lunges", "sets": 4, "reps": "20s", "rest": "10s"}, {"name": "Push-up to jump", "sets": 4, "reps": "20s", "rest": "10s"}]}, {"id": 4, "title": "Mobilité Matinale", "category": "Mobilité", "level": "Débutant", "duration": "15 min", "equipment": "<PERSON><PERSON>", "description": "Routine douce pour améliorer la mobilité articulaire et préparer le corps pour la journée.", "exercises": [{"name": "Cat-cow stretch", "sets": 1, "reps": "60s", "rest": "0s"}, {"name": "World's greatest stretch", "sets": 1, "reps": "60s par côté", "rest": "0s"}, {"name": "Shoulder rolls", "sets": 1, "reps": "30s dans chaque direction", "rest": "0s"}, {"name": "Hip circles", "sets": 1, "reps": "30s dans chaque direction", "rest": "0s"}, {"name": "Ankle mobility", "sets": 1, "reps": "30s par cheville", "rest": "0s"}]}, {"id": 5, "title": "Force Intermédiaire", "category": "Musculation", "level": "Intermédiaire", "duration": "45 min", "equipment": "Haltères, barre, banc", "description": "Programme de musculation pour progresser en force et en volume musculaire.", "exercises": [{"name": "Squat avec haltères", "sets": 4, "reps": 12, "rest": "90s"}, {"name": "<PERSON>évelop<PERSON><PERSON> couché", "sets": 4, "reps": 10, "rest": "90s"}, {"name": "Rowing barre", "sets": 4, "reps": 10, "rest": "90s"}, {"name": "Overhead press", "sets": 3, "reps": 10, "rest": "90s"}, {"name": "Romanian deadlift", "sets": 3, "reps": 12, "rest": "90s"}]}, {"id": 6, "title": "Cardio Endurance", "category": "Cardio", "level": "Débutant", "duration": "20 min", "equipment": "Aucun", "description": "Séance cardio progressive pour développer l'endurance de base.", "exercises": [{"name": "Marche rapide", "sets": 1, "reps": "5 min", "rest": "0s"}, {"name": "Jogging léger", "sets": 1, "reps": "3 min", "rest": "1 min"}, {"name": "Marche rapide", "sets": 1, "reps": "2 min", "rest": "0s"}, {"name": "Jogging léger", "sets": 1, "reps": "3 min", "rest": "1 min"}, {"name": "Marche rapide", "sets": 1, "reps": "5 min", "rest": "0s"}]}, {"id": 7, "title": "HIIT Tabata", "category": "HIIT", "level": "Intermédiaire", "duration": "16 min", "equipment": "<PERSON><PERSON>", "description": "Entraînement Tabata classique avec 8 rounds de 20 secondes d'effort et 10 secondes de récupération.", "exercises": [{"name": "Squat jumps", "sets": 8, "reps": "20s", "rest": "10s"}, {"name": "Push-ups", "sets": 8, "reps": "20s", "rest": "10s"}, {"name": "Jumping lunges", "sets": 8, "reps": "20s", "rest": "10s"}, {"name": "Mountain climbers", "sets": 8, "reps": "20s", "rest": "10s"}]}, {"id": 8, "title": "Mobilité Complète", "category": "Mobilité", "level": "Intermédiaire", "duration": "30 min", "equipment": "Tapis, foam roller", "description": "Session complète de mobilité pour améliorer la flexibilité et prévenir les blessures.", "exercises": [{"name": "Foam rolling - quadriceps", "sets": 1, "reps": "60s par jambe", "rest": "0s"}, {"name": "Foam rolling - dos", "sets": 1, "reps": "60s", "rest": "0s"}, {"name": "Dynamic hip stretch", "sets": 1, "reps": "45s par côté", "rest": "0s"}, {"name": "Thoracic bridge", "sets": 1, "reps": "45s par côté", "rest": "0s"}, {"name": "Ankle mobility drill", "sets": 1, "reps": "30s par cheville", "rest": "0s"}, {"name": "Shoulder dislocates", "sets": 1, "reps": 10, "rest": "0s"}]}, {"id": 9, "title": "Force Avancée", "category": "Musculation", "level": "<PERSON><PERSON><PERSON>", "duration": "60 min", "equipment": "Haltères, barre, banc, rack", "description": "Programme de musculation avancé pour maximiser la force et l'hypertrophie.", "exercises": [{"name": "Back squat", "sets": 5, "reps": "5", "rest": "3 min"}, {"name": "Bench press", "sets": 5, "reps": "5", "rest": "3 min"}, {"name": "Deadlift", "sets": 3, "reps": "5", "rest": "3 min"}, {"name": "Pull-ups", "sets": 4, "reps": "8", "rest": "2 min"}, {"name": "Military press", "sets": 4, "reps": "8", "rest": "2 min"}]}, {"id": 10, "title": "Cardio HIIT Avancé", "category": "Cardio", "level": "<PERSON><PERSON><PERSON>", "duration": "35 min", "equipment": "<PERSON><PERSON>, corde à sauter", "description": "Entraînement cardio avancé combinant des exercices de haute intensité pour une dépense calorique maximale.", "exercises": [{"name": "Corde à sauter - double unders", "sets": 5, "reps": "30s", "rest": "15s"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "sets": 5, "reps": "30s", "rest": "15s"}, {"name": "Box jumps", "sets": 5, "reps": "30s", "rest": "15s"}, {"name": "Sprint sur place", "sets": 5, "reps": "30s", "rest": "15s"}, {"name": "Jumping lunges", "sets": 5, "reps": "30s", "rest": "15s"}]}, {"id": 11, "title": "HIIT pour Débutants", "category": "HIIT", "level": "Débutant", "duration": "20 min", "equipment": "<PERSON><PERSON>", "description": "Introduction au HIIT avec des exercices adaptés aux débutants et des périodes de récupération plus longues.", "exercises": [{"name": "Jumping jacks", "sets": 4, "reps": "20s", "rest": "40s"}, {"name": "Squats", "sets": 4, "reps": "20s", "rest": "40s"}, {"name": "Modified push-ups", "sets": 4, "reps": "20s", "rest": "40s"}, {"name": "Glute bridges", "sets": 4, "reps": "20s", "rest": "40s"}, {"name": "Marche rapide sur place", "sets": 4, "reps": "20s", "rest": "40s"}]}, {"id": 12, "title": "Mobilité Avancée", "category": "Mobilité", "level": "<PERSON><PERSON><PERSON>", "duration": "45 min", "equipment": "Tapis, foam roller, bande élastique", "description": "Programme avancé de mobilité pour les athlètes cherchant à optimiser leurs performances et leur récupération.", "exercises": [{"name": "Banded shoulder distraction", "sets": 1, "reps": "2 min par côté", "rest": "0s"}, {"name": "Hip flow sequence", "sets": 1, "reps": "3 min", "rest": "0s"}, {"name": "<PERSON> curl", "sets": 3, "reps": "8 reps lentes", "rest": "30s"}, {"name": "Cossack squats", "sets": 3, "reps": "8 par côté", "rest": "30s"}, {"name": "Turkish get-up", "sets": 3, "reps": "3 par côté", "rest": "60s"}, {"name": "Skin the cat", "sets": 3, "reps": "5", "rest": "60s"}]}]