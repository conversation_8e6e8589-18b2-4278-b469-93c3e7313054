{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projet fitness\\\\DATA\\\\fitness-ui\\\\src\\\\pages\\\\Exercises.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { FiPlay, FiClock, FiZap, FiFilter, FiSearch, FiTarget, FiHeart, FiStar, FiInfo, FiPlus, FiTrendingUp, FiBookmark, FiVideo, FiBarChart3, FiCalendar, FiX, FiEdit3, FiSave } from 'react-icons/fi';\nimport { useAuth } from '../contexts/AuthContext';\nimport { exercises, exerciseCategories, muscleGroups, equipmentTypes, getDifficultyColor, getRecommendedExercises, searchExercises, createWorkoutPlan, toggleFavorite, loadFavorites, addToHistory, getExerciseHistory, calculateProgressStats, createCustomProgram, getExerciseVideo, getExerciseAnimation } from '../utils/exerciseUtils';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Exercises = () => {\n  _s();\n  var _exerciseCategories$s, _exerciseCategories$s2, _equipmentTypes$selec;\n  const {\n    user\n  } = useAuth();\n  const [selectedExercise, setSelectedExercise] = useState(null);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [showFilters, setShowFilters] = useState(false);\n  const [filters, setFilters] = useState({\n    category: 'all',\n    muscleGroup: 'all',\n    equipment: 'all',\n    difficulty: 'all',\n    maxDuration: null\n  });\n  const [activeTab, setActiveTab] = useState('all');\n  const [recommendedExercises, setRecommendedExercises] = useState([]);\n  const [favoriteExercises, setFavoriteExercises] = useState([]);\n  const [filteredExercises, setFilteredExercises] = useState(exercises);\n\n  // Nouveaux états pour les fonctionnalités avancées\n  const [exerciseHistory, setExerciseHistory] = useState([]);\n  const [showCreateProgram, setShowCreateProgram] = useState(false);\n  const [selectedExercisesForProgram, setSelectedExercisesForProgram] = useState([]);\n  const [showProgressModal, setShowProgressModal] = useState(false);\n  const [progressData, setProgressData] = useState(null);\n  const [showPerformanceModal, setShowPerformanceModal] = useState(false);\n  const [performanceData, setPerformanceData] = useState({\n    sets: 3,\n    reps: 12,\n    weight: '',\n    duration: '',\n    notes: ''\n  });\n  useEffect(() => {\n    // Générer des recommandations basées sur le profil utilisateur\n    if (user) {\n      const recommendations = getRecommendedExercises(user, user.primaryGoal || 'general_fitness');\n      setRecommendedExercises(recommendations);\n    }\n\n    // Charger les favoris depuis localStorage\n    const savedFavorites = loadFavorites();\n    const favoriteExerciseObjects = exercises.filter(ex => savedFavorites.includes(ex.id));\n    setFavoriteExercises(favoriteExerciseObjects);\n\n    // Charger l'historique des exercices\n    const history = getExerciseHistory();\n    setExerciseHistory(history);\n  }, [user]);\n  useEffect(() => {\n    // Appliquer les filtres et la recherche\n    let results = exercises;\n    if (activeTab === 'recommended') {\n      results = recommendedExercises;\n    } else if (activeTab === 'favorites') {\n      results = favoriteExercises;\n    }\n    if (searchQuery || Object.values(filters).some(f => f !== 'all' && f !== null)) {\n      results = searchExercises(searchQuery, filters);\n    }\n    setFilteredExercises(results);\n  }, [searchQuery, filters, activeTab, recommendedExercises, favoriteExercises]);\n  const handleFilterChange = (filterType, value) => {\n    setFilters(prev => ({\n      ...prev,\n      [filterType]: value\n    }));\n  };\n  const clearFilters = () => {\n    setFilters({\n      category: 'all',\n      muscleGroup: 'all',\n      equipment: 'all',\n      difficulty: 'all',\n      maxDuration: null\n    });\n    setSearchQuery('');\n  };\n  const generateQuickWorkout = () => {\n    const selectedExercises = filteredExercises.slice(0, 4);\n    const workout = createWorkoutPlan(selectedExercises, 30);\n\n    // Ici on pourrait ouvrir une modal avec le programme généré\n    alert(`Programme généré !\\nDurée: ${workout.totalDuration} min\\nCalories estimées: ${workout.estimatedCalories}\\nExercices: ${workout.exercises.length}`);\n  };\n  const handleToggleFavorite = exerciseId => {\n    const savedFavorites = loadFavorites();\n    const isNowFavorite = toggleFavorite(exerciseId, savedFavorites, () => {});\n\n    // Mettre à jour l'état local\n    if (isNowFavorite) {\n      const exercise = exercises.find(ex => ex.id === exerciseId);\n      setFavoriteExercises(prev => [...prev, exercise]);\n    } else {\n      setFavoriteExercises(prev => prev.filter(ex => ex.id !== exerciseId));\n    }\n  };\n\n  // Ajouter un exercice à l'historique avec performance\n  const handleAddToHistory = exercise => {\n    const entry = addToHistory(exercise, performanceData);\n    if (entry) {\n      setExerciseHistory(prev => [entry, ...prev]);\n      setShowPerformanceModal(false);\n      setPerformanceData({\n        sets: 3,\n        reps: 12,\n        weight: '',\n        duration: '',\n        notes: ''\n      });\n      alert('Exercice ajouté à votre historique !');\n    }\n  };\n\n  // Afficher les statistiques de progression\n  const handleShowProgress = exerciseId => {\n    const stats = calculateProgressStats(exerciseId);\n    setProgressData(stats);\n    setShowProgressModal(true);\n  };\n\n  // Ajouter/retirer un exercice de la sélection pour programme\n  const handleToggleExerciseSelection = exercise => {\n    setSelectedExercisesForProgram(prev => {\n      const isSelected = prev.some(ex => ex.id === exercise.id);\n      if (isSelected) {\n        return prev.filter(ex => ex.id !== exercise.id);\n      } else {\n        return [...prev, exercise];\n      }\n    });\n  };\n\n  // Créer un programme personnalisé\n  const handleCreateProgram = programName => {\n    if (selectedExercisesForProgram.length === 0) {\n      alert('Veuillez sélectionner au moins un exercice');\n      return;\n    }\n    const program = createCustomProgram(programName, selectedExercisesForProgram);\n    setSelectedExercisesForProgram([]);\n    setShowCreateProgram(false);\n    alert(`Programme \"${program.name}\" créé avec succès !`);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold text-gray-900 mb-4\",\n          children: \"Biblioth\\xE8que d'Exercices\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg text-gray-600 max-w-3xl mx-auto\",\n          children: \"D\\xE9couvrez notre collection compl\\xE8te d'exercices avec instructions d\\xE9taill\\xE9es, recommandations personnalis\\xE9es et programmes adapt\\xE9s \\xE0 vos objectifs.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 201,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-lg p-6 mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col lg:flex-row gap-4 items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 relative\",\n            children: [/*#__PURE__*/_jsxDEV(FiSearch, {\n              className: \"absolute left-3 top-3 h-5 w-5 text-gray-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"Rechercher un exercice, groupe musculaire...\",\n              value: searchQuery,\n              onChange: e => setSearchQuery(e.target.value),\n              className: \"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-wrap gap-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowFilters(!showFilters),\n              className: `px-4 py-3 rounded-lg font-medium transition-colors flex items-center ${showFilters ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`,\n              children: [/*#__PURE__*/_jsxDEV(FiFilter, {\n                className: \"h-4 w-4 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 231,\n                columnNumber: 17\n              }, this), \"Filtres\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: generateQuickWorkout,\n              className: \"px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(FiTarget, {\n                className: \"h-4 w-4 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 17\n              }, this), \"Programme rapide\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowCreateProgram(true),\n              className: \"px-4 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(FiPlus, {\n                className: \"h-4 w-4 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 17\n              }, this), \"Cr\\xE9er programme\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setActiveTab('history'),\n              className: \"px-4 py-3 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(FiCalendar, {\n                className: \"h-4 w-4 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 17\n              }, this), \"Historique\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 11\n        }, this), showFilters && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-6 pt-6 border-t border-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Cat\\xE9gorie\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: filters.category,\n                onChange: e => handleFilterChange('category', e.target.value),\n                className: \"w-full p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"all\",\n                  children: \"Toutes les cat\\xE9gories\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 269,\n                  columnNumber: 21\n                }, this), Object.entries(exerciseCategories).map(([key, category]) => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: key,\n                  children: category.name\n                }, key, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 271,\n                  columnNumber: 23\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Groupe musculaire\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 277,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: filters.muscleGroup,\n                onChange: e => handleFilterChange('muscleGroup', e.target.value),\n                className: \"w-full p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"all\",\n                  children: \"Tous les groupes\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 283,\n                  columnNumber: 21\n                }, this), Object.entries(muscleGroups).map(([key, group]) => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: key,\n                  children: group.name\n                }, key, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 285,\n                  columnNumber: 23\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 278,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"\\xC9quipement\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: filters.equipment,\n                onChange: e => handleFilterChange('equipment', e.target.value),\n                className: \"w-full p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"all\",\n                  children: \"Tout \\xE9quipement\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 297,\n                  columnNumber: 21\n                }, this), Object.entries(equipmentTypes).map(([key, equipment]) => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: key,\n                  children: equipment.name\n                }, key, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 299,\n                  columnNumber: 23\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 292,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Difficult\\xE9\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 305,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: filters.difficulty,\n                onChange: e => handleFilterChange('difficulty', e.target.value),\n                className: \"w-full p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"all\",\n                  children: \"Toutes difficult\\xE9s\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 311,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Facile\",\n                  children: \"Facile\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 312,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Mod\\xE9r\\xE9\",\n                  children: \"Mod\\xE9r\\xE9\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 313,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Difficile\",\n                  children: \"Difficile\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 314,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 306,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 304,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-4 flex justify-between items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700\",\n                children: \"Dur\\xE9e max (min):\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 321,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                value: filters.maxDuration || '',\n                onChange: e => handleFilterChange('maxDuration', e.target.value ? parseInt(e.target.value) : null),\n                placeholder: \"60\",\n                className: \"w-20 p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 322,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 320,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: clearFilters,\n              className: \"px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors\",\n              children: \"Effacer les filtres\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 319,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 212,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex space-x-1 bg-gray-100 p-1 rounded-lg mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setActiveTab('all'),\n          className: `flex-1 py-3 px-4 rounded-md text-sm font-medium transition-colors ${activeTab === 'all' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'}`,\n          children: [\"Tous (\", exercises.length, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 343,\n          columnNumber: 11\n        }, this), user && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setActiveTab('recommended'),\n            className: `flex-1 py-3 px-4 rounded-md text-sm font-medium transition-colors ${activeTab === 'recommended' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'}`,\n            children: [/*#__PURE__*/_jsxDEV(FiStar, {\n              className: \"inline mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 363,\n              columnNumber: 17\n            }, this), \"Recommand\\xE9s (\", recommendedExercises.length, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 355,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setActiveTab('favorites'),\n            className: `flex-1 py-3 px-4 rounded-md text-sm font-medium transition-colors ${activeTab === 'favorites' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'}`,\n            children: [/*#__PURE__*/_jsxDEV(FiHeart, {\n              className: \"inline mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 374,\n              columnNumber: 17\n            }, this), \"Favoris (\", favoriteExercises.length, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 366,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setActiveTab('history'),\n            className: `flex-1 py-3 px-4 rounded-md text-sm font-medium transition-colors ${activeTab === 'history' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'}`,\n            children: [/*#__PURE__*/_jsxDEV(FiCalendar, {\n              className: \"inline mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 385,\n              columnNumber: 17\n            }, this), \"Historique (\", exerciseHistory.length, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 377,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 342,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-6\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: [filteredExercises.length, \" exercice\", filteredExercises.length > 1 ? 's' : '', \" trouv\\xE9\", filteredExercises.length > 1 ? 's' : '', searchQuery && ` pour \"${searchQuery}\"`]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 394,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 393,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8\",\n        children: filteredExercises.map(exercise => {\n          var _exerciseCategories$e, _exerciseCategories$e2, _equipmentTypes$exerc, _equipmentTypes$exerc2;\n          const isFavorite = favoriteExercises.some(fav => fav.id === exercise.id);\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start justify-between mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-xl font-semibold text-gray-900 mb-2\",\n                    children: exercise.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 413,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-2 mb-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `px-3 py-1 rounded-full text-sm font-medium ${getDifficultyColor(exercise.difficulty)}`,\n                      children: exercise.difficulty\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 417,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-xs text-gray-500\",\n                      children: [(_exerciseCategories$e = exerciseCategories[exercise.category]) === null || _exerciseCategories$e === void 0 ? void 0 : _exerciseCategories$e.icon, \" \", (_exerciseCategories$e2 = exerciseCategories[exercise.category]) === null || _exerciseCategories$e2 === void 0 ? void 0 : _exerciseCategories$e2.name]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 420,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 416,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 412,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: e => {\n                      e.stopPropagation();\n                      handleShowProgress(exercise.id);\n                    },\n                    className: \"p-2 rounded-full text-gray-400 hover:text-blue-500 transition-colors\",\n                    title: \"Voir les statistiques\",\n                    children: /*#__PURE__*/_jsxDEV(FiBarChart3, {\n                      className: \"h-5 w-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 434,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 426,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: e => {\n                      e.stopPropagation();\n                      handleToggleFavorite(exercise.id);\n                    },\n                    className: `p-2 rounded-full transition-colors ${isFavorite ? 'text-red-500 hover:text-red-600' : 'text-gray-400 hover:text-red-500'}`,\n                    title: isFavorite ? 'Retirer des favoris' : 'Ajouter aux favoris',\n                    children: /*#__PURE__*/_jsxDEV(FiHeart, {\n                      className: `h-5 w-5 ${isFavorite ? 'fill-current' : ''}`\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 448,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 436,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 425,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 411,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 mb-4 line-clamp-2\",\n                children: exercise.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 453,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-wrap gap-1 mb-4\",\n                children: exercise.muscleGroups.map(mg => {\n                  var _muscleGroups$mg;\n                  return /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full\",\n                    children: (_muscleGroups$mg = muscleGroups[mg]) === null || _muscleGroups$mg === void 0 ? void 0 : _muscleGroups$mg.name\n                  }, mg, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 460,\n                    columnNumber: 23\n                  }, this);\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 458,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center mb-4 text-sm text-gray-600\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"mr-2\",\n                  children: (_equipmentTypes$exerc = equipmentTypes[exercise.equipment]) === null || _equipmentTypes$exerc === void 0 ? void 0 : _equipmentTypes$exerc.icon\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 468,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: (_equipmentTypes$exerc2 = equipmentTypes[exercise.equipment]) === null || _equipmentTypes$exerc2 === void 0 ? void 0 : _equipmentTypes$exerc2.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 469,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 467,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between text-sm text-gray-500 mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(FiClock, {\n                    className: \"h-4 w-4 mr-1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 474,\n                    columnNumber: 23\n                  }, this), exercise.duration, \" min\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 473,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(FiZap, {\n                    className: \"h-4 w-4 mr-1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 478,\n                    columnNumber: 23\n                  }, this), exercise.calories, \" cal\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 477,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 472,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex gap-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setSelectedExercise(exercise),\n                  className: \"flex-1 bg-gray-100 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-200 transition-colors flex items-center justify-center text-sm\",\n                  children: [/*#__PURE__*/_jsxDEV(FiInfo, {\n                    className: \"h-4 w-4 mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 488,\n                    columnNumber: 23\n                  }, this), \"D\\xE9tails\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 484,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center text-sm\",\n                  children: [/*#__PURE__*/_jsxDEV(FiPlay, {\n                    className: \"h-4 w-4 mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 492,\n                    columnNumber: 23\n                  }, this), \"Commencer\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 491,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 483,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 410,\n              columnNumber: 17\n            }, this)\n          }, exercise.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 406,\n            columnNumber: 15\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 401,\n        columnNumber: 9\n      }, this), filteredExercises.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-gray-400 mb-4\",\n          children: /*#__PURE__*/_jsxDEV(FiSearch, {\n            className: \"h-16 w-16 mx-auto\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 506,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 505,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium text-gray-900 mb-2\",\n          children: \"Aucun exercice trouv\\xE9\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 508,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mb-4\",\n          children: \"Essayez de modifier vos crit\\xE8res de recherche ou vos filtres.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 511,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: clearFilters,\n          className: \"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors\",\n          children: \"Effacer les filtres\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 514,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 504,\n        columnNumber: 11\n      }, this), selectedExercise && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"text-3xl font-bold text-gray-900 mb-2\",\n                  children: selectedExercise.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 530,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `px-3 py-1 rounded-full text-sm font-medium ${getDifficultyColor(selectedExercise.difficulty)}`,\n                    children: selectedExercise.difficulty\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 534,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-600\",\n                    children: [(_exerciseCategories$s = exerciseCategories[selectedExercise.category]) === null || _exerciseCategories$s === void 0 ? void 0 : _exerciseCategories$s.icon, \" \", (_exerciseCategories$s2 = exerciseCategories[selectedExercise.category]) === null || _exerciseCategories$s2 === void 0 ? void 0 : _exerciseCategories$s2.name]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 537,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 533,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 529,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setSelectedExercise(null),\n                className: \"text-gray-400 hover:text-gray-600 text-2xl\",\n                children: \"\\u2715\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 542,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 528,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"lg:col-span-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-xl font-semibold text-gray-900 mb-3\",\n                    children: \"Description\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 554,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-gray-600 leading-relaxed\",\n                    children: selectedExercise.description\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 555,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 553,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-xl font-semibold text-gray-900 mb-4\",\n                    children: \"Instructions d\\xE9taill\\xE9es\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 559,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"ol\", {\n                    className: \"list-decimal list-inside space-y-3\",\n                    children: selectedExercise.instructions.map((instruction, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n                      className: \"text-gray-600 leading-relaxed pl-2\",\n                      children: instruction\n                    }, index, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 562,\n                      columnNumber: 27\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 560,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 558,\n                  columnNumber: 21\n                }, this), selectedExercise.tips && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-xl font-semibold text-gray-900 mb-4\",\n                    children: \"Conseils\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 571,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                    className: \"space-y-2\",\n                    children: selectedExercise.tips.map((tip, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n                      className: \"flex items-start text-gray-600\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-blue-500 mr-2 mt-1\",\n                        children: \"\\u2022\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 575,\n                        columnNumber: 31\n                      }, this), tip]\n                    }, index, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 574,\n                      columnNumber: 29\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 572,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 570,\n                  columnNumber: 23\n                }, this), selectedExercise.variations && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-xl font-semibold text-gray-900 mb-4\",\n                    children: \"Variations\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 585,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                    className: \"space-y-2\",\n                    children: selectedExercise.variations.map((variation, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n                      className: \"flex items-start text-gray-600\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-green-500 mr-2 mt-1\",\n                        children: \"\\u2022\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 589,\n                        columnNumber: 31\n                      }, this), variation]\n                    }, index, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 588,\n                      columnNumber: 29\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 586,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 584,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 552,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"lg:col-span-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-gray-50 rounded-lg p-6 mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-lg font-semibold text-gray-900 mb-4\",\n                    children: \"Informations\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 601,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"space-y-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex justify-between\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-gray-600\",\n                        children: \"Dur\\xE9e\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 605,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"font-medium\",\n                        children: [selectedExercise.duration, \" min\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 606,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 604,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex justify-between\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-gray-600\",\n                        children: \"Calories\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 609,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"font-medium\",\n                        children: [selectedExercise.calories, \" cal\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 610,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 608,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex justify-between\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-gray-600\",\n                        children: \"\\xC9quipement\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 613,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"font-medium\",\n                        children: (_equipmentTypes$selec = equipmentTypes[selectedExercise.equipment]) === null || _equipmentTypes$selec === void 0 ? void 0 : _equipmentTypes$selec.name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 614,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 612,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 603,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 600,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-gray-50 rounded-lg p-6 mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-lg font-semibold text-gray-900 mb-4\",\n                    children: \"Groupes musculaires\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 620,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex flex-wrap gap-2\",\n                    children: selectedExercise.muscleGroups.map(mg => {\n                      var _muscleGroups$mg2, _muscleGroups$mg3;\n                      return /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"px-3 py-1 bg-blue-100 text-blue-700 text-sm rounded-full\",\n                        children: [(_muscleGroups$mg2 = muscleGroups[mg]) === null || _muscleGroups$mg2 === void 0 ? void 0 : _muscleGroups$mg2.icon, \" \", (_muscleGroups$mg3 = muscleGroups[mg]) === null || _muscleGroups$mg3 === void 0 ? void 0 : _muscleGroups$mg3.name]\n                      }, mg, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 623,\n                        columnNumber: 27\n                      }, this);\n                    })\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 621,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 619,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => toggleFavorite(selectedExercise.id),\n                    className: `w-full py-3 px-6 rounded-lg font-medium transition-colors flex items-center justify-center ${favoriteExercises.some(fav => fav.id === selectedExercise.id) ? 'bg-red-100 text-red-700 hover:bg-red-200' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`,\n                    children: [/*#__PURE__*/_jsxDEV(FiHeart, {\n                      className: \"h-5 w-5 mr-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 639,\n                      columnNumber: 25\n                    }, this), favoriteExercises.some(fav => fav.id === selectedExercise.id) ? 'Retirer des favoris' : 'Ajouter aux favoris']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 631,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"w-full bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center font-medium\",\n                    children: [/*#__PURE__*/_jsxDEV(FiPlay, {\n                      className: \"h-5 w-5 mr-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 644,\n                      columnNumber: 25\n                    }, this), \"Commencer l'exercice\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 643,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 630,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 599,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 550,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 527,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 526,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 525,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 199,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 198,\n    columnNumber: 5\n  }, this);\n};\n_s(Exercises, \"8mInFApJY8LT+1SCG/Obu5+1mGw=\", false, function () {\n  return [useAuth];\n});\n_c = Exercises;\nexport default Exercises;\nvar _c;\n$RefreshReg$(_c, \"Exercises\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "FiPlay", "<PERSON><PERSON><PERSON>", "FiZap", "<PERSON><PERSON><PERSON><PERSON>", "FiSearch", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "FiStar", "FiInfo", "FiPlus", "FiTrendingUp", "FiBookmark", "FiVideo", "FiBarChart3", "FiCalendar", "FiX", "FiEdit3", "FiSave", "useAuth", "exercises", "exerciseCategories", "muscleGroups", "equipmentTypes", "getDifficultyColor", "getRecommendedExercises", "searchExercises", "createWorkoutPlan", "toggleFavorite", "loadFavorites", "addToHistory", "getExerciseHistory", "calculateProgressStats", "createCustomProgram", "getExerciseVideo", "getExerciseAnimation", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Exercises", "_s", "_exerciseCategories$s", "_exerciseCategories$s2", "_equipmentTypes$selec", "user", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "setSelectedExercise", "searchQuery", "setSearch<PERSON>uery", "showFilters", "setShowFilters", "filters", "setFilters", "category", "muscleGroup", "equipment", "difficulty", "maxDuration", "activeTab", "setActiveTab", "recommendedExercises", "setRecommendedExercises", "favoriteExercises", "setFavoriteExercises", "filteredExercises", "setFilteredExercises", "exerciseHistory", "setExerciseHistory", "showCreateProgram", "setShowCreateProgram", "selectedExercisesForProgram", "setSelectedExercisesForProgram", "showProgressModal", "setShowProgressModal", "progressData", "setProgressData", "showPerformanceModal", "setShowPerformanceModal", "performanceData", "setPerformanceData", "sets", "reps", "weight", "duration", "notes", "recommendations", "primaryGoal", "savedFavorites", "favoriteExerciseObjects", "filter", "ex", "includes", "id", "history", "results", "Object", "values", "some", "f", "handleFilterChange", "filterType", "value", "prev", "clearFilters", "generateQuickWorkout", "selectedExercises", "slice", "workout", "alert", "totalDuration", "estimatedCalories", "length", "handleToggleFavorite", "exerciseId", "isNowFavorite", "exercise", "find", "handleAddToHistory", "entry", "handleShowProgress", "stats", "handleToggleExerciseSelection", "isSelected", "handleCreateProgram", "programName", "program", "name", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "placeholder", "onChange", "e", "target", "onClick", "entries", "map", "key", "group", "parseInt", "_exerciseCategories$e", "_exerciseCategories$e2", "_equipmentTypes$exerc", "_equipmentTypes$exerc2", "isFavorite", "fav", "icon", "stopPropagation", "title", "description", "mg", "_muscleGroups$mg", "calories", "instructions", "instruction", "index", "tips", "tip", "variations", "variation", "_muscleGroups$mg2", "_muscleGroups$mg3", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/projet fitness/DATA/fitness-ui/src/pages/Exercises.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  <PERSON><PERSON>lay,\n  Fi<PERSON>lock,\n  FiZap,\n  FiFilter,\n  FiSearch,\n  FiTarget,\n  FiHeart,\n  FiStar,\n  FiInfo,\n  FiPlus,\n  FiTrendingUp,\n  FiBookmark,\n  FiVideo,\n  FiBarChart3,\n  FiCalendar,\n  FiX,\n  FiEdit3,\n  FiSave\n} from 'react-icons/fi';\nimport { useAuth } from '../contexts/AuthContext';\nimport {\n  exercises,\n  exerciseCategories,\n  muscleGroups,\n  equipmentTypes,\n  getDifficultyColor,\n  getRecommendedExercises,\n  searchExercises,\n  createWorkoutPlan,\n  toggleFavorite,\n  loadFavorites,\n  addToHistory,\n  getExerciseHistory,\n  calculateProgressStats,\n  createCustomProgram,\n  getExerciseVideo,\n  getExerciseAnimation\n} from '../utils/exerciseUtils';\n\nconst Exercises = () => {\n  const { user } = useAuth();\n\n  const [selectedExercise, setSelectedExercise] = useState(null);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [showFilters, setShowFilters] = useState(false);\n  const [filters, setFilters] = useState({\n    category: 'all',\n    muscleGroup: 'all',\n    equipment: 'all',\n    difficulty: 'all',\n    maxDuration: null\n  });\n  const [activeTab, setActiveTab] = useState('all');\n  const [recommendedExercises, setRecommendedExercises] = useState([]);\n  const [favoriteExercises, setFavoriteExercises] = useState([]);\n  const [filteredExercises, setFilteredExercises] = useState(exercises);\n\n  // Nouveaux états pour les fonctionnalités avancées\n  const [exerciseHistory, setExerciseHistory] = useState([]);\n  const [showCreateProgram, setShowCreateProgram] = useState(false);\n  const [selectedExercisesForProgram, setSelectedExercisesForProgram] = useState([]);\n  const [showProgressModal, setShowProgressModal] = useState(false);\n  const [progressData, setProgressData] = useState(null);\n  const [showPerformanceModal, setShowPerformanceModal] = useState(false);\n  const [performanceData, setPerformanceData] = useState({\n    sets: 3,\n    reps: 12,\n    weight: '',\n    duration: '',\n    notes: ''\n  });\n\n  useEffect(() => {\n    // Générer des recommandations basées sur le profil utilisateur\n    if (user) {\n      const recommendations = getRecommendedExercises(user, user.primaryGoal || 'general_fitness');\n      setRecommendedExercises(recommendations);\n    }\n\n    // Charger les favoris depuis localStorage\n    const savedFavorites = loadFavorites();\n    const favoriteExerciseObjects = exercises.filter(ex => savedFavorites.includes(ex.id));\n    setFavoriteExercises(favoriteExerciseObjects);\n\n    // Charger l'historique des exercices\n    const history = getExerciseHistory();\n    setExerciseHistory(history);\n  }, [user]);\n\n  useEffect(() => {\n    // Appliquer les filtres et la recherche\n    let results = exercises;\n    \n    if (activeTab === 'recommended') {\n      results = recommendedExercises;\n    } else if (activeTab === 'favorites') {\n      results = favoriteExercises;\n    }\n    \n    if (searchQuery || Object.values(filters).some(f => f !== 'all' && f !== null)) {\n      results = searchExercises(searchQuery, filters);\n    }\n    \n    setFilteredExercises(results);\n  }, [searchQuery, filters, activeTab, recommendedExercises, favoriteExercises]);\n\n  const handleFilterChange = (filterType, value) => {\n    setFilters(prev => ({\n      ...prev,\n      [filterType]: value\n    }));\n  };\n\n  const clearFilters = () => {\n    setFilters({\n      category: 'all',\n      muscleGroup: 'all',\n      equipment: 'all',\n      difficulty: 'all',\n      maxDuration: null\n    });\n    setSearchQuery('');\n  };\n\n  const generateQuickWorkout = () => {\n    const selectedExercises = filteredExercises.slice(0, 4);\n    const workout = createWorkoutPlan(selectedExercises, 30);\n    \n    // Ici on pourrait ouvrir une modal avec le programme généré\n    alert(`Programme généré !\\nDurée: ${workout.totalDuration} min\\nCalories estimées: ${workout.estimatedCalories}\\nExercices: ${workout.exercises.length}`);\n  };\n\n  const handleToggleFavorite = (exerciseId) => {\n    const savedFavorites = loadFavorites();\n    const isNowFavorite = toggleFavorite(exerciseId, savedFavorites, () => {});\n\n    // Mettre à jour l'état local\n    if (isNowFavorite) {\n      const exercise = exercises.find(ex => ex.id === exerciseId);\n      setFavoriteExercises(prev => [...prev, exercise]);\n    } else {\n      setFavoriteExercises(prev => prev.filter(ex => ex.id !== exerciseId));\n    }\n  };\n\n  // Ajouter un exercice à l'historique avec performance\n  const handleAddToHistory = (exercise) => {\n    const entry = addToHistory(exercise, performanceData);\n    if (entry) {\n      setExerciseHistory(prev => [entry, ...prev]);\n      setShowPerformanceModal(false);\n      setPerformanceData({\n        sets: 3,\n        reps: 12,\n        weight: '',\n        duration: '',\n        notes: ''\n      });\n      alert('Exercice ajouté à votre historique !');\n    }\n  };\n\n  // Afficher les statistiques de progression\n  const handleShowProgress = (exerciseId) => {\n    const stats = calculateProgressStats(exerciseId);\n    setProgressData(stats);\n    setShowProgressModal(true);\n  };\n\n  // Ajouter/retirer un exercice de la sélection pour programme\n  const handleToggleExerciseSelection = (exercise) => {\n    setSelectedExercisesForProgram(prev => {\n      const isSelected = prev.some(ex => ex.id === exercise.id);\n      if (isSelected) {\n        return prev.filter(ex => ex.id !== exercise.id);\n      } else {\n        return [...prev, exercise];\n      }\n    });\n  };\n\n  // Créer un programme personnalisé\n  const handleCreateProgram = (programName) => {\n    if (selectedExercisesForProgram.length === 0) {\n      alert('Veuillez sélectionner au moins un exercice');\n      return;\n    }\n\n    const program = createCustomProgram(programName, selectedExercisesForProgram);\n    setSelectedExercisesForProgram([]);\n    setShowCreateProgram(false);\n    alert(`Programme \"${program.name}\" créé avec succès !`);\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Header */}\n        <div className=\"text-center mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-4\">\n            Bibliothèque d'Exercices\n          </h1>\n          <p className=\"text-lg text-gray-600 max-w-3xl mx-auto\">\n            Découvrez notre collection complète d'exercices avec instructions détaillées, \n            recommandations personnalisées et programmes adaptés à vos objectifs.\n          </p>\n        </div>\n\n        {/* Search and Quick Actions */}\n        <div className=\"bg-white rounded-lg shadow-lg p-6 mb-8\">\n          <div className=\"flex flex-col lg:flex-row gap-4 items-center\">\n            <div className=\"flex-1 relative\">\n              <FiSearch className=\"absolute left-3 top-3 h-5 w-5 text-gray-400\" />\n              <input\n                type=\"text\"\n                placeholder=\"Rechercher un exercice, groupe musculaire...\"\n                value={searchQuery}\n                onChange={(e) => setSearchQuery(e.target.value)}\n                className=\"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              />\n            </div>\n            <div className=\"flex flex-wrap gap-3\">\n              <button\n                onClick={() => setShowFilters(!showFilters)}\n                className={`px-4 py-3 rounded-lg font-medium transition-colors flex items-center ${\n                  showFilters ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'\n                }`}\n              >\n                <FiFilter className=\"h-4 w-4 mr-2\" />\n                Filtres\n              </button>\n              <button\n                onClick={generateQuickWorkout}\n                className=\"px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center\"\n              >\n                <FiTarget className=\"h-4 w-4 mr-2\" />\n                Programme rapide\n              </button>\n              <button\n                onClick={() => setShowCreateProgram(true)}\n                className=\"px-4 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors flex items-center\"\n              >\n                <FiPlus className=\"h-4 w-4 mr-2\" />\n                Créer programme\n              </button>\n              <button\n                onClick={() => setActiveTab('history')}\n                className=\"px-4 py-3 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors flex items-center\"\n              >\n                <FiCalendar className=\"h-4 w-4 mr-2\" />\n                Historique\n              </button>\n            </div>\n          </div>\n\n          {/* Advanced Filters */}\n          {showFilters && (\n            <div className=\"mt-6 pt-6 border-t border-gray-200\">\n              <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Catégorie</label>\n                  <select\n                    value={filters.category}\n                    onChange={(e) => handleFilterChange('category', e.target.value)}\n                    className=\"w-full p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  >\n                    <option value=\"all\">Toutes les catégories</option>\n                    {Object.entries(exerciseCategories).map(([key, category]) => (\n                      <option key={key} value={key}>{category.name}</option>\n                    ))}\n                  </select>\n                </div>\n                \n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Groupe musculaire</label>\n                  <select\n                    value={filters.muscleGroup}\n                    onChange={(e) => handleFilterChange('muscleGroup', e.target.value)}\n                    className=\"w-full p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  >\n                    <option value=\"all\">Tous les groupes</option>\n                    {Object.entries(muscleGroups).map(([key, group]) => (\n                      <option key={key} value={key}>{group.name}</option>\n                    ))}\n                  </select>\n                </div>\n                \n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Équipement</label>\n                  <select\n                    value={filters.equipment}\n                    onChange={(e) => handleFilterChange('equipment', e.target.value)}\n                    className=\"w-full p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  >\n                    <option value=\"all\">Tout équipement</option>\n                    {Object.entries(equipmentTypes).map(([key, equipment]) => (\n                      <option key={key} value={key}>{equipment.name}</option>\n                    ))}\n                  </select>\n                </div>\n                \n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Difficulté</label>\n                  <select\n                    value={filters.difficulty}\n                    onChange={(e) => handleFilterChange('difficulty', e.target.value)}\n                    className=\"w-full p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  >\n                    <option value=\"all\">Toutes difficultés</option>\n                    <option value=\"Facile\">Facile</option>\n                    <option value=\"Modéré\">Modéré</option>\n                    <option value=\"Difficile\">Difficile</option>\n                  </select>\n                </div>\n              </div>\n              \n              <div className=\"mt-4 flex justify-between items-center\">\n                <div className=\"flex items-center space-x-4\">\n                  <label className=\"block text-sm font-medium text-gray-700\">Durée max (min):</label>\n                  <input\n                    type=\"number\"\n                    value={filters.maxDuration || ''}\n                    onChange={(e) => handleFilterChange('maxDuration', e.target.value ? parseInt(e.target.value) : null)}\n                    placeholder=\"60\"\n                    className=\"w-20 p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  />\n                </div>\n                <button\n                  onClick={clearFilters}\n                  className=\"px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors\"\n                >\n                  Effacer les filtres\n                </button>\n              </div>\n            </div>\n          )}\n        </div>\n\n        {/* Tabs */}\n        <div className=\"flex space-x-1 bg-gray-100 p-1 rounded-lg mb-8\">\n          <button\n            onClick={() => setActiveTab('all')}\n            className={`flex-1 py-3 px-4 rounded-md text-sm font-medium transition-colors ${\n              activeTab === 'all'\n                ? 'bg-white text-blue-600 shadow-sm'\n                : 'text-gray-600 hover:text-gray-900'\n            }`}\n          >\n            Tous ({exercises.length})\n          </button>\n          {user && (\n            <>\n              <button\n                onClick={() => setActiveTab('recommended')}\n                className={`flex-1 py-3 px-4 rounded-md text-sm font-medium transition-colors ${\n                  activeTab === 'recommended'\n                    ? 'bg-white text-blue-600 shadow-sm'\n                    : 'text-gray-600 hover:text-gray-900'\n                }`}\n              >\n                <FiStar className=\"inline mr-2\" />\n                Recommandés ({recommendedExercises.length})\n              </button>\n              <button\n                onClick={() => setActiveTab('favorites')}\n                className={`flex-1 py-3 px-4 rounded-md text-sm font-medium transition-colors ${\n                  activeTab === 'favorites'\n                    ? 'bg-white text-blue-600 shadow-sm'\n                    : 'text-gray-600 hover:text-gray-900'\n                }`}\n              >\n                <FiHeart className=\"inline mr-2\" />\n                Favoris ({favoriteExercises.length})\n              </button>\n              <button\n                onClick={() => setActiveTab('history')}\n                className={`flex-1 py-3 px-4 rounded-md text-sm font-medium transition-colors ${\n                  activeTab === 'history'\n                    ? 'bg-white text-blue-600 shadow-sm'\n                    : 'text-gray-600 hover:text-gray-900'\n                }`}\n              >\n                <FiCalendar className=\"inline mr-2\" />\n                Historique ({exerciseHistory.length})\n              </button>\n            </>\n          )}\n        </div>\n\n        {/* Results Summary */}\n        <div className=\"mb-6\">\n          <p className=\"text-gray-600\">\n            {filteredExercises.length} exercice{filteredExercises.length > 1 ? 's' : ''} trouvé{filteredExercises.length > 1 ? 's' : ''}\n            {searchQuery && ` pour \"${searchQuery}\"`}\n          </p>\n        </div>\n\n        {/* Exercise Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8\">\n          {filteredExercises.map(exercise => {\n            const isFavorite = favoriteExercises.some(fav => fav.id === exercise.id);\n\n            return (\n              <div\n                key={exercise.id}\n                className=\"bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow\"\n              >\n                <div className=\"p-6\">\n                  <div className=\"flex items-start justify-between mb-4\">\n                    <div className=\"flex-1\">\n                      <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">\n                        {exercise.name}\n                      </h3>\n                      <div className=\"flex items-center space-x-2 mb-3\">\n                        <span className={`px-3 py-1 rounded-full text-sm font-medium ${getDifficultyColor(exercise.difficulty)}`}>\n                          {exercise.difficulty}\n                        </span>\n                        <span className=\"text-xs text-gray-500\">\n                          {exerciseCategories[exercise.category]?.icon} {exerciseCategories[exercise.category]?.name}\n                        </span>\n                      </div>\n                    </div>\n                    <div className=\"flex space-x-2\">\n                      <button\n                        onClick={(e) => {\n                          e.stopPropagation();\n                          handleShowProgress(exercise.id);\n                        }}\n                        className=\"p-2 rounded-full text-gray-400 hover:text-blue-500 transition-colors\"\n                        title=\"Voir les statistiques\"\n                      >\n                        <FiBarChart3 className=\"h-5 w-5\" />\n                      </button>\n                      <button\n                        onClick={(e) => {\n                          e.stopPropagation();\n                          handleToggleFavorite(exercise.id);\n                        }}\n                        className={`p-2 rounded-full transition-colors ${\n                          isFavorite\n                            ? 'text-red-500 hover:text-red-600'\n                            : 'text-gray-400 hover:text-red-500'\n                        }`}\n                        title={isFavorite ? 'Retirer des favoris' : 'Ajouter aux favoris'}\n                      >\n                        <FiHeart className={`h-5 w-5 ${isFavorite ? 'fill-current' : ''}`} />\n                      </button>\n                    </div>\n                  </div>\n\n                  <p className=\"text-gray-600 mb-4 line-clamp-2\">\n                    {exercise.description}\n                  </p>\n\n                  {/* Muscle Groups */}\n                  <div className=\"flex flex-wrap gap-1 mb-4\">\n                    {exercise.muscleGroups.map(mg => (\n                      <span key={mg} className=\"px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full\">\n                        {muscleGroups[mg]?.name}\n                      </span>\n                    ))}\n                  </div>\n\n                  {/* Equipment */}\n                  <div className=\"flex items-center mb-4 text-sm text-gray-600\">\n                    <span className=\"mr-2\">{equipmentTypes[exercise.equipment]?.icon}</span>\n                    <span>{equipmentTypes[exercise.equipment]?.name}</span>\n                  </div>\n\n                  <div className=\"flex items-center justify-between text-sm text-gray-500 mb-4\">\n                    <div className=\"flex items-center\">\n                      <FiClock className=\"h-4 w-4 mr-1\" />\n                      {exercise.duration} min\n                    </div>\n                    <div className=\"flex items-center\">\n                      <FiZap className=\"h-4 w-4 mr-1\" />\n                      {exercise.calories} cal\n                    </div>\n                  </div>\n\n                  <div className=\"flex gap-2\">\n                    <button\n                      onClick={() => setSelectedExercise(exercise)}\n                      className=\"flex-1 bg-gray-100 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-200 transition-colors flex items-center justify-center text-sm\"\n                    >\n                      <FiInfo className=\"h-4 w-4 mr-2\" />\n                      Détails\n                    </button>\n                    <button className=\"flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center text-sm\">\n                      <FiPlay className=\"h-4 w-4 mr-2\" />\n                      Commencer\n                    </button>\n                  </div>\n                </div>\n              </div>\n            );\n          })}\n        </div>\n\n        {/* Empty State */}\n        {filteredExercises.length === 0 && (\n          <div className=\"text-center py-12\">\n            <div className=\"text-gray-400 mb-4\">\n              <FiSearch className=\"h-16 w-16 mx-auto\" />\n            </div>\n            <h3 className=\"text-lg font-medium text-gray-900 mb-2\">\n              Aucun exercice trouvé\n            </h3>\n            <p className=\"text-gray-600 mb-4\">\n              Essayez de modifier vos critères de recherche ou vos filtres.\n            </p>\n            <button\n              onClick={clearFilters}\n              className=\"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors\"\n            >\n              Effacer les filtres\n            </button>\n          </div>\n        )}\n\n        {/* Exercise Detail Modal */}\n        {selectedExercise && (\n          <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\">\n            <div className=\"bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto\">\n              <div className=\"p-6\">\n                <div className=\"flex items-center justify-between mb-6\">\n                  <div>\n                    <h2 className=\"text-3xl font-bold text-gray-900 mb-2\">\n                      {selectedExercise.name}\n                    </h2>\n                    <div className=\"flex items-center space-x-3\">\n                      <span className={`px-3 py-1 rounded-full text-sm font-medium ${getDifficultyColor(selectedExercise.difficulty)}`}>\n                        {selectedExercise.difficulty}\n                      </span>\n                      <span className=\"text-gray-600\">\n                        {exerciseCategories[selectedExercise.category]?.icon} {exerciseCategories[selectedExercise.category]?.name}\n                      </span>\n                    </div>\n                  </div>\n                  <button\n                    onClick={() => setSelectedExercise(null)}\n                    className=\"text-gray-400 hover:text-gray-600 text-2xl\"\n                  >\n                    ✕\n                  </button>\n                </div>\n\n                <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n                  {/* Main Content */}\n                  <div className=\"lg:col-span-2\">\n                    <div className=\"mb-6\">\n                      <h3 className=\"text-xl font-semibold text-gray-900 mb-3\">Description</h3>\n                      <p className=\"text-gray-600 leading-relaxed\">{selectedExercise.description}</p>\n                    </div>\n\n                    <div className=\"mb-6\">\n                      <h3 className=\"text-xl font-semibold text-gray-900 mb-4\">Instructions détaillées</h3>\n                      <ol className=\"list-decimal list-inside space-y-3\">\n                        {selectedExercise.instructions.map((instruction, index) => (\n                          <li key={index} className=\"text-gray-600 leading-relaxed pl-2\">\n                            {instruction}\n                          </li>\n                        ))}\n                      </ol>\n                    </div>\n\n                    {selectedExercise.tips && (\n                      <div className=\"mb-6\">\n                        <h3 className=\"text-xl font-semibold text-gray-900 mb-4\">Conseils</h3>\n                        <ul className=\"space-y-2\">\n                          {selectedExercise.tips.map((tip, index) => (\n                            <li key={index} className=\"flex items-start text-gray-600\">\n                              <span className=\"text-blue-500 mr-2 mt-1\">•</span>\n                              {tip}\n                            </li>\n                          ))}\n                        </ul>\n                      </div>\n                    )}\n\n                    {selectedExercise.variations && (\n                      <div className=\"mb-6\">\n                        <h3 className=\"text-xl font-semibold text-gray-900 mb-4\">Variations</h3>\n                        <ul className=\"space-y-2\">\n                          {selectedExercise.variations.map((variation, index) => (\n                            <li key={index} className=\"flex items-start text-gray-600\">\n                              <span className=\"text-green-500 mr-2 mt-1\">•</span>\n                              {variation}\n                            </li>\n                          ))}\n                        </ul>\n                      </div>\n                    )}\n                  </div>\n\n                  {/* Sidebar */}\n                  <div className=\"lg:col-span-1\">\n                    <div className=\"bg-gray-50 rounded-lg p-6 mb-6\">\n                      <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Informations</h3>\n\n                      <div className=\"space-y-4\">\n                        <div className=\"flex justify-between\">\n                          <span className=\"text-gray-600\">Durée</span>\n                          <span className=\"font-medium\">{selectedExercise.duration} min</span>\n                        </div>\n                        <div className=\"flex justify-between\">\n                          <span className=\"text-gray-600\">Calories</span>\n                          <span className=\"font-medium\">{selectedExercise.calories} cal</span>\n                        </div>\n                        <div className=\"flex justify-between\">\n                          <span className=\"text-gray-600\">Équipement</span>\n                          <span className=\"font-medium\">{equipmentTypes[selectedExercise.equipment]?.name}</span>\n                        </div>\n                      </div>\n                    </div>\n\n                    <div className=\"bg-gray-50 rounded-lg p-6 mb-6\">\n                      <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Groupes musculaires</h3>\n                      <div className=\"flex flex-wrap gap-2\">\n                        {selectedExercise.muscleGroups.map(mg => (\n                          <span key={mg} className=\"px-3 py-1 bg-blue-100 text-blue-700 text-sm rounded-full\">\n                            {muscleGroups[mg]?.icon} {muscleGroups[mg]?.name}\n                          </span>\n                        ))}\n                      </div>\n                    </div>\n\n                    <div className=\"space-y-3\">\n                      <button\n                        onClick={() => toggleFavorite(selectedExercise.id)}\n                        className={`w-full py-3 px-6 rounded-lg font-medium transition-colors flex items-center justify-center ${\n                          favoriteExercises.some(fav => fav.id === selectedExercise.id)\n                            ? 'bg-red-100 text-red-700 hover:bg-red-200'\n                            : 'bg-gray-100 text-gray-700 hover:bg-gray-200'\n                        }`}\n                      >\n                        <FiHeart className=\"h-5 w-5 mr-2\" />\n                        {favoriteExercises.some(fav => fav.id === selectedExercise.id) ? 'Retirer des favoris' : 'Ajouter aux favoris'}\n                      </button>\n\n                      <button className=\"w-full bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center font-medium\">\n                        <FiPlay className=\"h-5 w-5 mr-2\" />\n                        Commencer l'exercice\n                      </button>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default Exercises;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,MAAM,EACNC,OAAO,EACPC,KAAK,EACLC,QAAQ,EACRC,QAAQ,EACRC,QAAQ,EACRC,OAAO,EACPC,MAAM,EACNC,MAAM,EACNC,MAAM,EACNC,YAAY,EACZC,UAAU,EACVC,OAAO,EACPC,WAAW,EACXC,UAAU,EACVC,GAAG,EACHC,OAAO,EACPC,MAAM,QACD,gBAAgB;AACvB,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SACEC,SAAS,EACTC,kBAAkB,EAClBC,YAAY,EACZC,cAAc,EACdC,kBAAkB,EAClBC,uBAAuB,EACvBC,eAAe,EACfC,iBAAiB,EACjBC,cAAc,EACdC,aAAa,EACbC,YAAY,EACZC,kBAAkB,EAClBC,sBAAsB,EACtBC,mBAAmB,EACnBC,gBAAgB,EAChBC,oBAAoB,QACf,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEhC,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA;EACtB,MAAM;IAAEC;EAAK,CAAC,GAAG1B,OAAO,CAAC,CAAC;EAE1B,MAAM,CAAC2B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGhD,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACiD,WAAW,EAAEC,cAAc,CAAC,GAAGlD,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACmD,WAAW,EAAEC,cAAc,CAAC,GAAGpD,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACqD,OAAO,EAAEC,UAAU,CAAC,GAAGtD,QAAQ,CAAC;IACrCuD,QAAQ,EAAE,KAAK;IACfC,WAAW,EAAE,KAAK;IAClBC,SAAS,EAAE,KAAK;IAChBC,UAAU,EAAE,KAAK;IACjBC,WAAW,EAAE;EACf,CAAC,CAAC;EACF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG7D,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC8D,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG/D,QAAQ,CAAC,EAAE,CAAC;EACpE,MAAM,CAACgE,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGjE,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACkE,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGnE,QAAQ,CAACqB,SAAS,CAAC;;EAErE;EACA,MAAM,CAAC+C,eAAe,EAAEC,kBAAkB,CAAC,GAAGrE,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACsE,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGvE,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACwE,2BAA2B,EAAEC,8BAA8B,CAAC,GAAGzE,QAAQ,CAAC,EAAE,CAAC;EAClF,MAAM,CAAC0E,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG3E,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC4E,YAAY,EAAEC,eAAe,CAAC,GAAG7E,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC8E,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG/E,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAACgF,eAAe,EAAEC,kBAAkB,CAAC,GAAGjF,QAAQ,CAAC;IACrDkF,IAAI,EAAE,CAAC;IACPC,IAAI,EAAE,EAAE;IACRC,MAAM,EAAE,EAAE;IACVC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE;EACT,CAAC,CAAC;EAEFrF,SAAS,CAAC,MAAM;IACd;IACA,IAAI6C,IAAI,EAAE;MACR,MAAMyC,eAAe,GAAG7D,uBAAuB,CAACoB,IAAI,EAAEA,IAAI,CAAC0C,WAAW,IAAI,iBAAiB,CAAC;MAC5FzB,uBAAuB,CAACwB,eAAe,CAAC;IAC1C;;IAEA;IACA,MAAME,cAAc,GAAG3D,aAAa,CAAC,CAAC;IACtC,MAAM4D,uBAAuB,GAAGrE,SAAS,CAACsE,MAAM,CAACC,EAAE,IAAIH,cAAc,CAACI,QAAQ,CAACD,EAAE,CAACE,EAAE,CAAC,CAAC;IACtF7B,oBAAoB,CAACyB,uBAAuB,CAAC;;IAE7C;IACA,MAAMK,OAAO,GAAG/D,kBAAkB,CAAC,CAAC;IACpCqC,kBAAkB,CAAC0B,OAAO,CAAC;EAC7B,CAAC,EAAE,CAACjD,IAAI,CAAC,CAAC;EAEV7C,SAAS,CAAC,MAAM;IACd;IACA,IAAI+F,OAAO,GAAG3E,SAAS;IAEvB,IAAIuC,SAAS,KAAK,aAAa,EAAE;MAC/BoC,OAAO,GAAGlC,oBAAoB;IAChC,CAAC,MAAM,IAAIF,SAAS,KAAK,WAAW,EAAE;MACpCoC,OAAO,GAAGhC,iBAAiB;IAC7B;IAEA,IAAIf,WAAW,IAAIgD,MAAM,CAACC,MAAM,CAAC7C,OAAO,CAAC,CAAC8C,IAAI,CAACC,CAAC,IAAIA,CAAC,KAAK,KAAK,IAAIA,CAAC,KAAK,IAAI,CAAC,EAAE;MAC9EJ,OAAO,GAAGrE,eAAe,CAACsB,WAAW,EAAEI,OAAO,CAAC;IACjD;IAEAc,oBAAoB,CAAC6B,OAAO,CAAC;EAC/B,CAAC,EAAE,CAAC/C,WAAW,EAAEI,OAAO,EAAEO,SAAS,EAAEE,oBAAoB,EAAEE,iBAAiB,CAAC,CAAC;EAE9E,MAAMqC,kBAAkB,GAAGA,CAACC,UAAU,EAAEC,KAAK,KAAK;IAChDjD,UAAU,CAACkD,IAAI,KAAK;MAClB,GAAGA,IAAI;MACP,CAACF,UAAU,GAAGC;IAChB,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAME,YAAY,GAAGA,CAAA,KAAM;IACzBnD,UAAU,CAAC;MACTC,QAAQ,EAAE,KAAK;MACfC,WAAW,EAAE,KAAK;MAClBC,SAAS,EAAE,KAAK;MAChBC,UAAU,EAAE,KAAK;MACjBC,WAAW,EAAE;IACf,CAAC,CAAC;IACFT,cAAc,CAAC,EAAE,CAAC;EACpB,CAAC;EAED,MAAMwD,oBAAoB,GAAGA,CAAA,KAAM;IACjC,MAAMC,iBAAiB,GAAGzC,iBAAiB,CAAC0C,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;IACvD,MAAMC,OAAO,GAAGjF,iBAAiB,CAAC+E,iBAAiB,EAAE,EAAE,CAAC;;IAExD;IACAG,KAAK,CAAC,8BAA8BD,OAAO,CAACE,aAAa,4BAA4BF,OAAO,CAACG,iBAAiB,gBAAgBH,OAAO,CAACxF,SAAS,CAAC4F,MAAM,EAAE,CAAC;EAC3J,CAAC;EAED,MAAMC,oBAAoB,GAAIC,UAAU,IAAK;IAC3C,MAAM1B,cAAc,GAAG3D,aAAa,CAAC,CAAC;IACtC,MAAMsF,aAAa,GAAGvF,cAAc,CAACsF,UAAU,EAAE1B,cAAc,EAAE,MAAM,CAAC,CAAC,CAAC;;IAE1E;IACA,IAAI2B,aAAa,EAAE;MACjB,MAAMC,QAAQ,GAAGhG,SAAS,CAACiG,IAAI,CAAC1B,EAAE,IAAIA,EAAE,CAACE,EAAE,KAAKqB,UAAU,CAAC;MAC3DlD,oBAAoB,CAACuC,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEa,QAAQ,CAAC,CAAC;IACnD,CAAC,MAAM;MACLpD,oBAAoB,CAACuC,IAAI,IAAIA,IAAI,CAACb,MAAM,CAACC,EAAE,IAAIA,EAAE,CAACE,EAAE,KAAKqB,UAAU,CAAC,CAAC;IACvE;EACF,CAAC;;EAED;EACA,MAAMI,kBAAkB,GAAIF,QAAQ,IAAK;IACvC,MAAMG,KAAK,GAAGzF,YAAY,CAACsF,QAAQ,EAAErC,eAAe,CAAC;IACrD,IAAIwC,KAAK,EAAE;MACTnD,kBAAkB,CAACmC,IAAI,IAAI,CAACgB,KAAK,EAAE,GAAGhB,IAAI,CAAC,CAAC;MAC5CzB,uBAAuB,CAAC,KAAK,CAAC;MAC9BE,kBAAkB,CAAC;QACjBC,IAAI,EAAE,CAAC;QACPC,IAAI,EAAE,EAAE;QACRC,MAAM,EAAE,EAAE;QACVC,QAAQ,EAAE,EAAE;QACZC,KAAK,EAAE;MACT,CAAC,CAAC;MACFwB,KAAK,CAAC,sCAAsC,CAAC;IAC/C;EACF,CAAC;;EAED;EACA,MAAMW,kBAAkB,GAAIN,UAAU,IAAK;IACzC,MAAMO,KAAK,GAAGzF,sBAAsB,CAACkF,UAAU,CAAC;IAChDtC,eAAe,CAAC6C,KAAK,CAAC;IACtB/C,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMgD,6BAA6B,GAAIN,QAAQ,IAAK;IAClD5C,8BAA8B,CAAC+B,IAAI,IAAI;MACrC,MAAMoB,UAAU,GAAGpB,IAAI,CAACL,IAAI,CAACP,EAAE,IAAIA,EAAE,CAACE,EAAE,KAAKuB,QAAQ,CAACvB,EAAE,CAAC;MACzD,IAAI8B,UAAU,EAAE;QACd,OAAOpB,IAAI,CAACb,MAAM,CAACC,EAAE,IAAIA,EAAE,CAACE,EAAE,KAAKuB,QAAQ,CAACvB,EAAE,CAAC;MACjD,CAAC,MAAM;QACL,OAAO,CAAC,GAAGU,IAAI,EAAEa,QAAQ,CAAC;MAC5B;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMQ,mBAAmB,GAAIC,WAAW,IAAK;IAC3C,IAAItD,2BAA2B,CAACyC,MAAM,KAAK,CAAC,EAAE;MAC5CH,KAAK,CAAC,4CAA4C,CAAC;MACnD;IACF;IAEA,MAAMiB,OAAO,GAAG7F,mBAAmB,CAAC4F,WAAW,EAAEtD,2BAA2B,CAAC;IAC7EC,8BAA8B,CAAC,EAAE,CAAC;IAClCF,oBAAoB,CAAC,KAAK,CAAC;IAC3BuC,KAAK,CAAC,cAAciB,OAAO,CAACC,IAAI,sBAAsB,CAAC;EACzD,CAAC;EAED,oBACE1F,OAAA;IAAK2F,SAAS,EAAC,yBAAyB;IAAAC,QAAA,eACtC5F,OAAA;MAAK2F,SAAS,EAAC,6CAA6C;MAAAC,QAAA,gBAE1D5F,OAAA;QAAK2F,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/B5F,OAAA;UAAI2F,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAEtD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLhG,OAAA;UAAG2F,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EAAC;QAGvD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGNhG,OAAA;QAAK2F,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrD5F,OAAA;UAAK2F,SAAS,EAAC,8CAA8C;UAAAC,QAAA,gBAC3D5F,OAAA;YAAK2F,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9B5F,OAAA,CAAChC,QAAQ;cAAC2H,SAAS,EAAC;YAA6C;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpEhG,OAAA;cACEiG,IAAI,EAAC,MAAM;cACXC,WAAW,EAAC,8CAA8C;cAC1DjC,KAAK,EAAEtD,WAAY;cACnBwF,QAAQ,EAAGC,CAAC,IAAKxF,cAAc,CAACwF,CAAC,CAACC,MAAM,CAACpC,KAAK,CAAE;cAChD0B,SAAS,EAAC;YAA8G;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNhG,OAAA;YAAK2F,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnC5F,OAAA;cACEsG,OAAO,EAAEA,CAAA,KAAMxF,cAAc,CAAC,CAACD,WAAW,CAAE;cAC5C8E,SAAS,EAAE,wEACT9E,WAAW,GAAG,wBAAwB,GAAG,6CAA6C,EACrF;cAAA+E,QAAA,gBAEH5F,OAAA,CAACjC,QAAQ;gBAAC4H,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,WAEvC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACThG,OAAA;cACEsG,OAAO,EAAElC,oBAAqB;cAC9BuB,SAAS,EAAC,qGAAqG;cAAAC,QAAA,gBAE/G5F,OAAA,CAAC/B,QAAQ;gBAAC0H,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,oBAEvC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACThG,OAAA;cACEsG,OAAO,EAAEA,CAAA,KAAMrE,oBAAoB,CAAC,IAAI,CAAE;cAC1C0D,SAAS,EAAC,uGAAuG;cAAAC,QAAA,gBAEjH5F,OAAA,CAAC3B,MAAM;gBAACsH,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,sBAErC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACThG,OAAA;cACEsG,OAAO,EAAEA,CAAA,KAAM/E,YAAY,CAAC,SAAS,CAAE;cACvCoE,SAAS,EAAC,uGAAuG;cAAAC,QAAA,gBAEjH5F,OAAA,CAACtB,UAAU;gBAACiH,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,cAEzC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGLnF,WAAW,iBACVb,OAAA;UAAK2F,SAAS,EAAC,oCAAoC;UAAAC,QAAA,gBACjD5F,OAAA;YAAK2F,SAAS,EAAC,sDAAsD;YAAAC,QAAA,gBACnE5F,OAAA;cAAA4F,QAAA,gBACE5F,OAAA;gBAAO2F,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACjFhG,OAAA;gBACEiE,KAAK,EAAElD,OAAO,CAACE,QAAS;gBACxBkF,QAAQ,EAAGC,CAAC,IAAKrC,kBAAkB,CAAC,UAAU,EAAEqC,CAAC,CAACC,MAAM,CAACpC,KAAK,CAAE;gBAChE0B,SAAS,EAAC,kGAAkG;gBAAAC,QAAA,gBAE5G5F,OAAA;kBAAQiE,KAAK,EAAC,KAAK;kBAAA2B,QAAA,EAAC;gBAAqB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EACjDrC,MAAM,CAAC4C,OAAO,CAACvH,kBAAkB,CAAC,CAACwH,GAAG,CAAC,CAAC,CAACC,GAAG,EAAExF,QAAQ,CAAC,kBACtDjB,OAAA;kBAAkBiE,KAAK,EAAEwC,GAAI;kBAAAb,QAAA,EAAE3E,QAAQ,CAACyE;gBAAI,GAA/Be,GAAG;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAqC,CACtD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENhG,OAAA;cAAA4F,QAAA,gBACE5F,OAAA;gBAAO2F,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACzFhG,OAAA;gBACEiE,KAAK,EAAElD,OAAO,CAACG,WAAY;gBAC3BiF,QAAQ,EAAGC,CAAC,IAAKrC,kBAAkB,CAAC,aAAa,EAAEqC,CAAC,CAACC,MAAM,CAACpC,KAAK,CAAE;gBACnE0B,SAAS,EAAC,kGAAkG;gBAAAC,QAAA,gBAE5G5F,OAAA;kBAAQiE,KAAK,EAAC,KAAK;kBAAA2B,QAAA,EAAC;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EAC5CrC,MAAM,CAAC4C,OAAO,CAACtH,YAAY,CAAC,CAACuH,GAAG,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,kBAC7C1G,OAAA;kBAAkBiE,KAAK,EAAEwC,GAAI;kBAAAb,QAAA,EAAEc,KAAK,CAAChB;gBAAI,GAA5Be,GAAG;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAkC,CACnD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENhG,OAAA;cAAA4F,QAAA,gBACE5F,OAAA;gBAAO2F,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAClFhG,OAAA;gBACEiE,KAAK,EAAElD,OAAO,CAACI,SAAU;gBACzBgF,QAAQ,EAAGC,CAAC,IAAKrC,kBAAkB,CAAC,WAAW,EAAEqC,CAAC,CAACC,MAAM,CAACpC,KAAK,CAAE;gBACjE0B,SAAS,EAAC,kGAAkG;gBAAAC,QAAA,gBAE5G5F,OAAA;kBAAQiE,KAAK,EAAC,KAAK;kBAAA2B,QAAA,EAAC;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EAC3CrC,MAAM,CAAC4C,OAAO,CAACrH,cAAc,CAAC,CAACsH,GAAG,CAAC,CAAC,CAACC,GAAG,EAAEtF,SAAS,CAAC,kBACnDnB,OAAA;kBAAkBiE,KAAK,EAAEwC,GAAI;kBAAAb,QAAA,EAAEzE,SAAS,CAACuE;gBAAI,GAAhCe,GAAG;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAsC,CACvD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENhG,OAAA;cAAA4F,QAAA,gBACE5F,OAAA;gBAAO2F,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAClFhG,OAAA;gBACEiE,KAAK,EAAElD,OAAO,CAACK,UAAW;gBAC1B+E,QAAQ,EAAGC,CAAC,IAAKrC,kBAAkB,CAAC,YAAY,EAAEqC,CAAC,CAACC,MAAM,CAACpC,KAAK,CAAE;gBAClE0B,SAAS,EAAC,kGAAkG;gBAAAC,QAAA,gBAE5G5F,OAAA;kBAAQiE,KAAK,EAAC,KAAK;kBAAA2B,QAAA,EAAC;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC/ChG,OAAA;kBAAQiE,KAAK,EAAC,QAAQ;kBAAA2B,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtChG,OAAA;kBAAQiE,KAAK,EAAC,cAAQ;kBAAA2B,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtChG,OAAA;kBAAQiE,KAAK,EAAC,WAAW;kBAAA2B,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENhG,OAAA;YAAK2F,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACrD5F,OAAA;cAAK2F,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1C5F,OAAA;gBAAO2F,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACnFhG,OAAA;gBACEiG,IAAI,EAAC,QAAQ;gBACbhC,KAAK,EAAElD,OAAO,CAACM,WAAW,IAAI,EAAG;gBACjC8E,QAAQ,EAAGC,CAAC,IAAKrC,kBAAkB,CAAC,aAAa,EAAEqC,CAAC,CAACC,MAAM,CAACpC,KAAK,GAAG0C,QAAQ,CAACP,CAAC,CAACC,MAAM,CAACpC,KAAK,CAAC,GAAG,IAAI,CAAE;gBACrGiC,WAAW,EAAC,IAAI;gBAChBP,SAAS,EAAC;cAAgG;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3G,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNhG,OAAA;cACEsG,OAAO,EAAEnC,YAAa;cACtBwB,SAAS,EAAC,+DAA+D;cAAAC,QAAA,EAC1E;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNhG,OAAA;QAAK2F,SAAS,EAAC,gDAAgD;QAAAC,QAAA,gBAC7D5F,OAAA;UACEsG,OAAO,EAAEA,CAAA,KAAM/E,YAAY,CAAC,KAAK,CAAE;UACnCoE,SAAS,EAAE,qEACTrE,SAAS,KAAK,KAAK,GACf,kCAAkC,GAClC,mCAAmC,EACtC;UAAAsE,QAAA,GACJ,QACO,EAAC7G,SAAS,CAAC4F,MAAM,EAAC,GAC1B;QAAA;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EACRxF,IAAI,iBACHR,OAAA,CAAAE,SAAA;UAAA0F,QAAA,gBACE5F,OAAA;YACEsG,OAAO,EAAEA,CAAA,KAAM/E,YAAY,CAAC,aAAa,CAAE;YAC3CoE,SAAS,EAAE,qEACTrE,SAAS,KAAK,aAAa,GACvB,kCAAkC,GAClC,mCAAmC,EACtC;YAAAsE,QAAA,gBAEH5F,OAAA,CAAC7B,MAAM;cAACwH,SAAS,EAAC;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,oBACrB,EAACxE,oBAAoB,CAACmD,MAAM,EAAC,GAC5C;UAAA;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACThG,OAAA;YACEsG,OAAO,EAAEA,CAAA,KAAM/E,YAAY,CAAC,WAAW,CAAE;YACzCoE,SAAS,EAAE,qEACTrE,SAAS,KAAK,WAAW,GACrB,kCAAkC,GAClC,mCAAmC,EACtC;YAAAsE,QAAA,gBAEH5F,OAAA,CAAC9B,OAAO;cAACyH,SAAS,EAAC;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,aAC1B,EAACtE,iBAAiB,CAACiD,MAAM,EAAC,GACrC;UAAA;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACThG,OAAA;YACEsG,OAAO,EAAEA,CAAA,KAAM/E,YAAY,CAAC,SAAS,CAAE;YACvCoE,SAAS,EAAE,qEACTrE,SAAS,KAAK,SAAS,GACnB,kCAAkC,GAClC,mCAAmC,EACtC;YAAAsE,QAAA,gBAEH5F,OAAA,CAACtB,UAAU;cAACiH,SAAS,EAAC;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAC1B,EAAClE,eAAe,CAAC6C,MAAM,EAAC,GACtC;UAAA;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA,eACT,CACH;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNhG,OAAA;QAAK2F,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnB5F,OAAA;UAAG2F,SAAS,EAAC,eAAe;UAAAC,QAAA,GACzBhE,iBAAiB,CAAC+C,MAAM,EAAC,WAAS,EAAC/C,iBAAiB,CAAC+C,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAC,YAAO,EAAC/C,iBAAiB,CAAC+C,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAC1HhE,WAAW,IAAI,UAAUA,WAAW,GAAG;QAAA;UAAAkF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGNhG,OAAA;QAAK2F,SAAS,EAAC,2DAA2D;QAAAC,QAAA,EACvEhE,iBAAiB,CAAC4E,GAAG,CAACzB,QAAQ,IAAI;UAAA,IAAA6B,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA;UACjC,MAAMC,UAAU,GAAGtF,iBAAiB,CAACmC,IAAI,CAACoD,GAAG,IAAIA,GAAG,CAACzD,EAAE,KAAKuB,QAAQ,CAACvB,EAAE,CAAC;UAExE,oBACExD,OAAA;YAEE2F,SAAS,EAAC,iFAAiF;YAAAC,QAAA,eAE3F5F,OAAA;cAAK2F,SAAS,EAAC,KAAK;cAAAC,QAAA,gBAClB5F,OAAA;gBAAK2F,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,gBACpD5F,OAAA;kBAAK2F,SAAS,EAAC,QAAQ;kBAAAC,QAAA,gBACrB5F,OAAA;oBAAI2F,SAAS,EAAC,0CAA0C;oBAAAC,QAAA,EACrDb,QAAQ,CAACW;kBAAI;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACZ,CAAC,eACLhG,OAAA;oBAAK2F,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,gBAC/C5F,OAAA;sBAAM2F,SAAS,EAAE,8CAA8CxG,kBAAkB,CAAC4F,QAAQ,CAAC3D,UAAU,CAAC,EAAG;sBAAAwE,QAAA,EACtGb,QAAQ,CAAC3D;oBAAU;sBAAAyE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChB,CAAC,eACPhG,OAAA;sBAAM2F,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,IAAAgB,qBAAA,GACpC5H,kBAAkB,CAAC+F,QAAQ,CAAC9D,QAAQ,CAAC,cAAA2F,qBAAA,uBAArCA,qBAAA,CAAuCM,IAAI,EAAC,GAAC,GAAAL,sBAAA,GAAC7H,kBAAkB,CAAC+F,QAAQ,CAAC9D,QAAQ,CAAC,cAAA4F,sBAAA,uBAArCA,sBAAA,CAAuCnB,IAAI;oBAAA;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNhG,OAAA;kBAAK2F,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC7B5F,OAAA;oBACEsG,OAAO,EAAGF,CAAC,IAAK;sBACdA,CAAC,CAACe,eAAe,CAAC,CAAC;sBACnBhC,kBAAkB,CAACJ,QAAQ,CAACvB,EAAE,CAAC;oBACjC,CAAE;oBACFmC,SAAS,EAAC,sEAAsE;oBAChFyB,KAAK,EAAC,uBAAuB;oBAAAxB,QAAA,eAE7B5F,OAAA,CAACvB,WAAW;sBAACkH,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7B,CAAC,eACThG,OAAA;oBACEsG,OAAO,EAAGF,CAAC,IAAK;sBACdA,CAAC,CAACe,eAAe,CAAC,CAAC;sBACnBvC,oBAAoB,CAACG,QAAQ,CAACvB,EAAE,CAAC;oBACnC,CAAE;oBACFmC,SAAS,EAAE,sCACTqB,UAAU,GACN,iCAAiC,GACjC,kCAAkC,EACrC;oBACHI,KAAK,EAAEJ,UAAU,GAAG,qBAAqB,GAAG,qBAAsB;oBAAApB,QAAA,eAElE5F,OAAA,CAAC9B,OAAO;sBAACyH,SAAS,EAAE,WAAWqB,UAAU,GAAG,cAAc,GAAG,EAAE;oBAAG;sBAAAnB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/D,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENhG,OAAA;gBAAG2F,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,EAC3Cb,QAAQ,CAACsC;cAAW;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC,eAGJhG,OAAA;gBAAK2F,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EACvCb,QAAQ,CAAC9F,YAAY,CAACuH,GAAG,CAACc,EAAE;kBAAA,IAAAC,gBAAA;kBAAA,oBAC3BvH,OAAA;oBAAe2F,SAAS,EAAC,0DAA0D;oBAAAC,QAAA,GAAA2B,gBAAA,GAChFtI,YAAY,CAACqI,EAAE,CAAC,cAAAC,gBAAA,uBAAhBA,gBAAA,CAAkB7B;kBAAI,GADd4B,EAAE;oBAAAzB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEP,CAAC;gBAAA,CACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAGNhG,OAAA;gBAAK2F,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,gBAC3D5F,OAAA;kBAAM2F,SAAS,EAAC,MAAM;kBAAAC,QAAA,GAAAkB,qBAAA,GAAE5H,cAAc,CAAC6F,QAAQ,CAAC5D,SAAS,CAAC,cAAA2F,qBAAA,uBAAlCA,qBAAA,CAAoCI;gBAAI;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACxEhG,OAAA;kBAAA4F,QAAA,GAAAmB,sBAAA,GAAO7H,cAAc,CAAC6F,QAAQ,CAAC5D,SAAS,CAAC,cAAA4F,sBAAA,uBAAlCA,sBAAA,CAAoCrB;gBAAI;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC,eAENhG,OAAA;gBAAK2F,SAAS,EAAC,8DAA8D;gBAAAC,QAAA,gBAC3E5F,OAAA;kBAAK2F,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChC5F,OAAA,CAACnC,OAAO;oBAAC8H,SAAS,EAAC;kBAAc;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EACnCjB,QAAQ,CAAChC,QAAQ,EAAC,MACrB;gBAAA;kBAAA8C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNhG,OAAA;kBAAK2F,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChC5F,OAAA,CAAClC,KAAK;oBAAC6H,SAAS,EAAC;kBAAc;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EACjCjB,QAAQ,CAACyC,QAAQ,EAAC,MACrB;gBAAA;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENhG,OAAA;gBAAK2F,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB5F,OAAA;kBACEsG,OAAO,EAAEA,CAAA,KAAM5F,mBAAmB,CAACqE,QAAQ,CAAE;kBAC7CY,SAAS,EAAC,oIAAoI;kBAAAC,QAAA,gBAE9I5F,OAAA,CAAC5B,MAAM;oBAACuH,SAAS,EAAC;kBAAc;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,cAErC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACThG,OAAA;kBAAQ2F,SAAS,EAAC,iIAAiI;kBAAAC,QAAA,gBACjJ5F,OAAA,CAACpC,MAAM;oBAAC+H,SAAS,EAAC;kBAAc;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,aAErC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC,GAzFDjB,QAAQ,CAACvB,EAAE;YAAAqC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA0Fb,CAAC;QAEV,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,EAGLpE,iBAAiB,CAAC+C,MAAM,KAAK,CAAC,iBAC7B3E,OAAA;QAAK2F,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChC5F,OAAA;UAAK2F,SAAS,EAAC,oBAAoB;UAAAC,QAAA,eACjC5F,OAAA,CAAChC,QAAQ;YAAC2H,SAAS,EAAC;UAAmB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC,eACNhG,OAAA;UAAI2F,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAEvD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLhG,OAAA;UAAG2F,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAElC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJhG,OAAA;UACEsG,OAAO,EAAEnC,YAAa;UACtBwB,SAAS,EAAC,iFAAiF;UAAAC,QAAA,EAC5F;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN,EAGAvF,gBAAgB,iBACfT,OAAA;QAAK2F,SAAS,EAAC,gFAAgF;QAAAC,QAAA,eAC7F5F,OAAA;UAAK2F,SAAS,EAAC,mEAAmE;UAAAC,QAAA,eAChF5F,OAAA;YAAK2F,SAAS,EAAC,KAAK;YAAAC,QAAA,gBAClB5F,OAAA;cAAK2F,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrD5F,OAAA;gBAAA4F,QAAA,gBACE5F,OAAA;kBAAI2F,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,EAClDnF,gBAAgB,CAACiF;gBAAI;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB,CAAC,eACLhG,OAAA;kBAAK2F,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1C5F,OAAA;oBAAM2F,SAAS,EAAE,8CAA8CxG,kBAAkB,CAACsB,gBAAgB,CAACW,UAAU,CAAC,EAAG;oBAAAwE,QAAA,EAC9GnF,gBAAgB,CAACW;kBAAU;oBAAAyE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB,CAAC,eACPhG,OAAA;oBAAM2F,SAAS,EAAC,eAAe;oBAAAC,QAAA,IAAAvF,qBAAA,GAC5BrB,kBAAkB,CAACyB,gBAAgB,CAACQ,QAAQ,CAAC,cAAAZ,qBAAA,uBAA7CA,qBAAA,CAA+C6G,IAAI,EAAC,GAAC,GAAA5G,sBAAA,GAACtB,kBAAkB,CAACyB,gBAAgB,CAACQ,QAAQ,CAAC,cAAAX,sBAAA,uBAA7CA,sBAAA,CAA+CoF,IAAI;kBAAA;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNhG,OAAA;gBACEsG,OAAO,EAAEA,CAAA,KAAM5F,mBAAmB,CAAC,IAAI,CAAE;gBACzCiF,SAAS,EAAC,4CAA4C;gBAAAC,QAAA,EACvD;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENhG,OAAA;cAAK2F,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBAEpD5F,OAAA;gBAAK2F,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5B5F,OAAA;kBAAK2F,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBACnB5F,OAAA;oBAAI2F,SAAS,EAAC,0CAA0C;oBAAAC,QAAA,EAAC;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACzEhG,OAAA;oBAAG2F,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,EAAEnF,gBAAgB,CAAC4G;kBAAW;oBAAAxB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5E,CAAC,eAENhG,OAAA;kBAAK2F,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBACnB5F,OAAA;oBAAI2F,SAAS,EAAC,0CAA0C;oBAAAC,QAAA,EAAC;kBAAuB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACrFhG,OAAA;oBAAI2F,SAAS,EAAC,oCAAoC;oBAAAC,QAAA,EAC/CnF,gBAAgB,CAACgH,YAAY,CAACjB,GAAG,CAAC,CAACkB,WAAW,EAAEC,KAAK,kBACpD3H,OAAA;sBAAgB2F,SAAS,EAAC,oCAAoC;sBAAAC,QAAA,EAC3D8B;oBAAW,GADLC,KAAK;sBAAA9B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEV,CACL;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,EAELvF,gBAAgB,CAACmH,IAAI,iBACpB5H,OAAA;kBAAK2F,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBACnB5F,OAAA;oBAAI2F,SAAS,EAAC,0CAA0C;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACtEhG,OAAA;oBAAI2F,SAAS,EAAC,WAAW;oBAAAC,QAAA,EACtBnF,gBAAgB,CAACmH,IAAI,CAACpB,GAAG,CAAC,CAACqB,GAAG,EAAEF,KAAK,kBACpC3H,OAAA;sBAAgB2F,SAAS,EAAC,gCAAgC;sBAAAC,QAAA,gBACxD5F,OAAA;wBAAM2F,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,EAAC;sBAAC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,EACjD6B,GAAG;oBAAA,GAFGF,KAAK;sBAAA9B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAGV,CACL;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CACN,EAEAvF,gBAAgB,CAACqH,UAAU,iBAC1B9H,OAAA;kBAAK2F,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBACnB5F,OAAA;oBAAI2F,SAAS,EAAC,0CAA0C;oBAAAC,QAAA,EAAC;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACxEhG,OAAA;oBAAI2F,SAAS,EAAC,WAAW;oBAAAC,QAAA,EACtBnF,gBAAgB,CAACqH,UAAU,CAACtB,GAAG,CAAC,CAACuB,SAAS,EAAEJ,KAAK,kBAChD3H,OAAA;sBAAgB2F,SAAS,EAAC,gCAAgC;sBAAAC,QAAA,gBACxD5F,OAAA;wBAAM2F,SAAS,EAAC,0BAA0B;wBAAAC,QAAA,EAAC;sBAAC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,EAClD+B,SAAS;oBAAA,GAFHJ,KAAK;sBAAA9B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAGV,CACL;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAGNhG,OAAA;gBAAK2F,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5B5F,OAAA;kBAAK2F,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,gBAC7C5F,OAAA;oBAAI2F,SAAS,EAAC,0CAA0C;oBAAAC,QAAA,EAAC;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAE1EhG,OAAA;oBAAK2F,SAAS,EAAC,WAAW;oBAAAC,QAAA,gBACxB5F,OAAA;sBAAK2F,SAAS,EAAC,sBAAsB;sBAAAC,QAAA,gBACnC5F,OAAA;wBAAM2F,SAAS,EAAC,eAAe;wBAAAC,QAAA,EAAC;sBAAK;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAC5ChG,OAAA;wBAAM2F,SAAS,EAAC,aAAa;wBAAAC,QAAA,GAAEnF,gBAAgB,CAACsC,QAAQ,EAAC,MAAI;sBAAA;wBAAA8C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjE,CAAC,eACNhG,OAAA;sBAAK2F,SAAS,EAAC,sBAAsB;sBAAAC,QAAA,gBACnC5F,OAAA;wBAAM2F,SAAS,EAAC,eAAe;wBAAAC,QAAA,EAAC;sBAAQ;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAC/ChG,OAAA;wBAAM2F,SAAS,EAAC,aAAa;wBAAAC,QAAA,GAAEnF,gBAAgB,CAAC+G,QAAQ,EAAC,MAAI;sBAAA;wBAAA3B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjE,CAAC,eACNhG,OAAA;sBAAK2F,SAAS,EAAC,sBAAsB;sBAAAC,QAAA,gBACnC5F,OAAA;wBAAM2F,SAAS,EAAC,eAAe;wBAAAC,QAAA,EAAC;sBAAU;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACjDhG,OAAA;wBAAM2F,SAAS,EAAC,aAAa;wBAAAC,QAAA,GAAArF,qBAAA,GAAErB,cAAc,CAACuB,gBAAgB,CAACU,SAAS,CAAC,cAAAZ,qBAAA,uBAA1CA,qBAAA,CAA4CmF;sBAAI;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENhG,OAAA;kBAAK2F,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,gBAC7C5F,OAAA;oBAAI2F,SAAS,EAAC,0CAA0C;oBAAAC,QAAA,EAAC;kBAAmB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACjFhG,OAAA;oBAAK2F,SAAS,EAAC,sBAAsB;oBAAAC,QAAA,EAClCnF,gBAAgB,CAACxB,YAAY,CAACuH,GAAG,CAACc,EAAE;sBAAA,IAAAU,iBAAA,EAAAC,iBAAA;sBAAA,oBACnCjI,OAAA;wBAAe2F,SAAS,EAAC,0DAA0D;wBAAAC,QAAA,IAAAoC,iBAAA,GAChF/I,YAAY,CAACqI,EAAE,CAAC,cAAAU,iBAAA,uBAAhBA,iBAAA,CAAkBd,IAAI,EAAC,GAAC,GAAAe,iBAAA,GAAChJ,YAAY,CAACqI,EAAE,CAAC,cAAAW,iBAAA,uBAAhBA,iBAAA,CAAkBvC,IAAI;sBAAA,GADvC4B,EAAE;wBAAAzB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAEP,CAAC;oBAAA,CACR;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENhG,OAAA;kBAAK2F,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBACxB5F,OAAA;oBACEsG,OAAO,EAAEA,CAAA,KAAM/G,cAAc,CAACkB,gBAAgB,CAAC+C,EAAE,CAAE;oBACnDmC,SAAS,EAAE,8FACTjE,iBAAiB,CAACmC,IAAI,CAACoD,GAAG,IAAIA,GAAG,CAACzD,EAAE,KAAK/C,gBAAgB,CAAC+C,EAAE,CAAC,GACzD,0CAA0C,GAC1C,6CAA6C,EAChD;oBAAAoC,QAAA,gBAEH5F,OAAA,CAAC9B,OAAO;sBAACyH,SAAS,EAAC;oBAAc;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EACnCtE,iBAAiB,CAACmC,IAAI,CAACoD,GAAG,IAAIA,GAAG,CAACzD,EAAE,KAAK/C,gBAAgB,CAAC+C,EAAE,CAAC,GAAG,qBAAqB,GAAG,qBAAqB;kBAAA;oBAAAqC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxG,CAAC,eAEThG,OAAA;oBAAQ2F,SAAS,EAAC,qIAAqI;oBAAAC,QAAA,gBACrJ5F,OAAA,CAACpC,MAAM;sBAAC+H,SAAS,EAAC;oBAAc;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,wBAErC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC5F,EAAA,CAvmBID,SAAS;EAAA,QACIrB,OAAO;AAAA;AAAAoJ,EAAA,GADpB/H,SAAS;AAymBf,eAAeA,SAAS;AAAC,IAAA+H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}