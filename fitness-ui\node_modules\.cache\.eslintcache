[{"C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Timer.js": "4", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Home.js": "5", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\components\\Navbar.js": "6", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Programs.js": "7", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\components\\Footer.js": "8", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Progress.js": "9", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Features.js": "10", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\About.js": "11", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Dashboard.js": "12", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\components\\ProtectedRoute.js": "13", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Auth.js": "14", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\contexts\\AuthContext.js": "15", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\components\\auth\\LoginForm.js": "16", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\components\\auth\\RegisterForm.js": "17", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Performance.js": "18", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\utils\\performancePrediction.js": "19", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Routes.js": "20", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Social.js": "21", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Exercises.js": "22", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\utils\\mapUtils.js": "23", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\utils\\exerciseUtils.js": "24", "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\utils\\socialUtils.js": "25"}, {"size": 648, "mtime": 1750768565997, "results": "26", "hashOfConfig": "27"}, {"size": 2653, "mtime": 1750838920471, "results": "28", "hashOfConfig": "27"}, {"size": 375, "mtime": 1750768575776, "results": "29", "hashOfConfig": "27"}, {"size": 10842, "mtime": 1750775069651, "results": "30", "hashOfConfig": "27"}, {"size": 6703, "mtime": 1750768747649, "results": "31", "hashOfConfig": "27"}, {"size": 7644, "mtime": 1750838937946, "results": "32", "hashOfConfig": "27"}, {"size": 12336, "mtime": 1750775057314, "results": "33", "hashOfConfig": "27"}, {"size": 3466, "mtime": 1750768701081, "results": "34", "hashOfConfig": "27"}, {"size": 7868, "mtime": 1750774457559, "results": "35", "hashOfConfig": "27"}, {"size": 7686, "mtime": 1750774497155, "results": "36", "hashOfConfig": "27"}, {"size": 9384, "mtime": 1750774543945, "results": "37", "hashOfConfig": "27"}, {"size": 9809, "mtime": 1750839016634, "results": "38", "hashOfConfig": "27"}, {"size": 826, "mtime": 1750776245084, "results": "39", "hashOfConfig": "27"}, {"size": 1104, "mtime": 1750776230119, "results": "40", "hashOfConfig": "27"}, {"size": 5719, "mtime": 1750776029978, "results": "41", "hashOfConfig": "27"}, {"size": 5990, "mtime": 1750776119596, "results": "42", "hashOfConfig": "27"}, {"size": 15313, "mtime": 1750776195115, "results": "43", "hashOfConfig": "27"}, {"size": 19413, "mtime": 1750845671080, "results": "44", "hashOfConfig": "27"}, {"size": 8451, "mtime": 1750836275026, "results": "45", "hashOfConfig": "27"}, {"size": 34218, "mtime": 1750845445331, "results": "46", "hashOfConfig": "27"}, {"size": 41381, "mtime": 1750845915689, "results": "47", "hashOfConfig": "27"}, {"size": 49797, "mtime": 1750846153362, "results": "48", "hashOfConfig": "27"}, {"size": 12795, "mtime": 1750839579993, "results": "49", "hashOfConfig": "27"}, {"size": 22604, "mtime": 1750842751821, "results": "50", "hashOfConfig": "27"}, {"size": 13396, "mtime": 1750837589801, "results": "51", "hashOfConfig": "27"}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "923mfg", {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Timer.js", [], ["127"], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Home.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\components\\Navbar.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Programs.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\components\\Footer.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Progress.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Features.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\About.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Dashboard.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\components\\ProtectedRoute.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Auth.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\contexts\\AuthContext.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\components\\auth\\LoginForm.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\components\\auth\\RegisterForm.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Performance.js", ["128", "129"], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\utils\\performancePrediction.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Routes.js", ["130", "131"], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Social.js", ["132", "133", "134", "135", "136", "137", "138", "139"], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\pages\\Exercises.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\utils\\mapUtils.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\utils\\exerciseUtils.js", [], [], "C:\\Users\\<USER>\\Desktop\\projet fitness\\DATA\\fitness-ui\\src\\utils\\socialUtils.js", [], [], {"ruleId": "140", "severity": 1, "message": "141", "line": 34, "column": 6, "nodeType": "142", "endLine": 34, "endColumn": 16, "suggestions": "143", "suppressions": "144"}, {"ruleId": "145", "severity": 1, "message": "146", "line": 33, "column": 10, "nodeType": "147", "messageId": "148", "endLine": 33, "endColumn": 19}, {"ruleId": "145", "severity": 1, "message": "149", "line": 33, "column": 21, "nodeType": "147", "messageId": "148", "endLine": 33, "endColumn": 33}, {"ruleId": "145", "severity": 1, "message": "150", "line": 16, "column": 3, "nodeType": "147", "messageId": "148", "endLine": 16, "endColumn": 10}, {"ruleId": "140", "severity": 1, "message": "151", "line": 101, "column": 6, "nodeType": "142", "endLine": 101, "endColumn": 8, "suggestions": "152"}, {"ruleId": "145", "severity": 1, "message": "153", "line": 15, "column": 3, "nodeType": "147", "messageId": "148", "endLine": 15, "endColumn": 10}, {"ruleId": "145", "severity": 1, "message": "154", "line": 17, "column": 3, "nodeType": "147", "messageId": "148", "endLine": 17, "endColumn": 11}, {"ruleId": "145", "severity": 1, "message": "155", "line": 18, "column": 3, "nodeType": "147", "messageId": "148", "endLine": 18, "endColumn": 10}, {"ruleId": "145", "severity": 1, "message": "156", "line": 19, "column": 3, "nodeType": "147", "messageId": "148", "endLine": 19, "endColumn": 13}, {"ruleId": "140", "severity": 1, "message": "157", "line": 108, "column": 6, "nodeType": "142", "endLine": 108, "endColumn": 12, "suggestions": "158"}, {"ruleId": "145", "severity": 1, "message": "159", "line": 273, "column": 9, "nodeType": "147", "messageId": "148", "endLine": 273, "endColumn": 20}, {"ruleId": "145", "severity": 1, "message": "160", "line": 293, "column": 9, "nodeType": "147", "messageId": "148", "endLine": 293, "endColumn": 21}, {"ruleId": "145", "severity": 1, "message": "161", "line": 311, "column": 9, "nodeType": "147", "messageId": "148", "endLine": 311, "endColumn": 19}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'resetTimer'. Either include it or remove the dependency array.", "ArrayExpression", ["162"], ["163"], "no-unused-vars", "'isLoading' is assigned a value but never used.", "Identifier", "unusedVar", "'setIsLoading' is assigned a value but never used.", "'FiClock' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadRoutesAndPOIs'. Either include it or remove the dependency array.", ["164"], "'FiEdit3' is defined but never used.", "'FiShare2' is defined but never used.", "'FiHeart' is defined but never used.", "'FiThumbsUp' is defined but never used.", "React Hook useEffect has missing dependencies: 'challenges.length' and 'friendships.length'. Either include them or remove the dependency array.", ["165"], "'addActivity' is assigned a value but never used.", "'likeActivity' is assigned a value but never used.", "'addComment' is assigned a value but never used.", {"desc": "166", "fix": "167"}, {"kind": "168", "justification": "169"}, {"desc": "170", "fix": "171"}, {"desc": "172", "fix": "173"}, "Update the dependencies array to be: [resetTimer, settings]", {"range": "174", "text": "175"}, "directive", "", "Update the dependencies array to be: [loadRoutesAndPOIs]", {"range": "176", "text": "177"}, "Update the dependencies array to be: [challenges.length, friendships.length, user]", {"range": "178", "text": "179"}, [1205, 1215], "[resetTimer, settings]", [3287, 3289], "[loadRoutesAndPOIs]", [3206, 3212], "[challenges.length, friendships.length, user]"]